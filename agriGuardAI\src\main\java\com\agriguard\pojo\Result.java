package com.agriguard.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class Result<T> {
    private int code;
    private String message;
    private T data;

    // 成功带数据
    public static <T> Result<T> success(T data) {
        return new Result<>(0, "操作成功", data);
    }

    // 错误
    public static <T> Result<T> error(String message) {
        return new Result<>(1, message, null);
    }
}
