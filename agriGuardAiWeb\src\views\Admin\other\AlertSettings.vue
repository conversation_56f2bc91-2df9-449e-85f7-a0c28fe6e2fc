<template>
  <div class="warning-container">
    <!-- 阈值设置区域 -->
    <el-card class="threshold-card">
      <template #header>
        <div class="card-header">
          <span>阈值设置</span>
        </div>
      </template>

      <div class="threshold-controls">
        <el-select v-model="selectedPlace" placeholder="选择区域" @change="loadThresholds">
          <el-option
              v-for="place in places"
              :key="place"
              :label="place"
              :value="place"
          />
        </el-select>

        <el-table :data="thresholds" style="width: 100%" border>
          <el-table-column prop="thresholdType" label="监测类型" width="180">
            <template #default="{row}">
              {{ getAlarmTypeName(row.thresholdType) }}传感器
            </template>
          </el-table-column>
          <el-table-column label="下限值" width="250">
            <template #default="{row}">
              <el-input-number
                  v-model="row.downValue"
                  :precision="getPrecision(row.thresholdType)"
                  :step="getStep(row.thresholdType)"
                  :min="getMinValue(row.thresholdType)"
              />
              <span class="unit">{{ getUnit(row.thresholdType) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上限值">
            <template #default="{row}">
              <el-input-number
                  v-model="row.upValue"
                  :precision="getPrecision(row.thresholdType)"
                  :step="getStep(row.thresholdType)"
                  :max="getMaxValue(row.thresholdType)"
              />
              <span class="unit">{{ getUnit(row.thresholdType) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{row}">
              <el-button type="primary" size="small" @click="saveThreshold(row)">保存</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 报警通知规则管理 -->
    <el-card class="notification-card">
      <template #header>
        <div class="card-header">
          <span>报警通知规则</span>
          <el-button type="primary" @click="showAddNotificationDialog">添加规则</el-button>
        </div>
      </template>

      <el-table :data="notificationRules" style="width: 100%" border>
        <el-table-column prop="alarm_type" label="报警类型" width="150">
          <template #default="{row}">
            {{ row.alarm_type }}传感器
          </template>
        </el-table-column>
        <el-table-column prop="place" label="区域" width="120">
          <template #default="{row}">
            {{ row.place || '所有区域' }}
          </template>
        </el-table-column>
        <el-table-column prop="staff_names" label="通知人员" width="150">
          <template #default="{row}">
            <el-tag v-for="staff in row.staff_names" :key="staff" class="staff-tag">
              {{ staff }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notification_method" label="通知方式" width="120">
          <template #default="{row}">
            <el-tag type="info">{{ row.noticeWay }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="{row}">
            <el-button type="primary" size="small" @click="editNotificationRule(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteNotificationRule(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 报警信息区域 -->
    <el-card class="alarm-card">
      <template #header>
        <div class="card-header">
          <span>报警记录</span>
          <div>
            <el-select v-model="alarmFilter.type" placeholder="报警类型" clearable>
              <el-option
                  v-for="type in alarmTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
              />
            </el-select>

            <el-select v-model="alarmFilter.place" placeholder="区域" clearable>
              <el-option
                  v-for="place in places"
                  :key="place"
                  :label="place"
                  :value="place"
              />
            </el-select>

            <el-date-picker
                v-model="alarmFilter.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DDTHH:mm:ss"
            />

            <el-button type="primary" @click="loadAlarms">查询</el-button>
          </div>
        </div>
      </template>

      <el-table :data="alarms" style="width: 100%" border v-loading="alarmLoading">
        <el-table-column prop="deviceName" label="设备名称" width="150" />
        <el-table-column prop="place" label="区域" width="120" />
        <el-table-column prop="alarmTypes" label="报警类型" width="120">
          <template #default="{row}">
            <el-tag :type="getAlarmTagType(row.alarmTypes)">
              {{ row.alarmTypes }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentValue" label="当前值" width="120">
          <template #default="{row}">
            {{ formatValue(row.alarmTypes, row.currentValue) }}
          </template>
        </el-table-column>
        <el-table-column prop="threshold" label="阈值" width="150">
          <template #default="{row}">
            {{ row.threshold }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="报警时间" width="180" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{row}">
            <el-tag :type="row.processingStatus === '已处理' ? 'success' : 'danger'">
              {{ row.processingStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notifiedStaff" label="已通知人员" width="180">
          <template #default="{row}">
            <el-tag
                v-for="staff in row.notifiedStaff"
                :key="staff"
                class="staff-tag"
            >
              {{ staff }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{row}">
            <el-button
                v-if="row.processingStatus === '未处理'"
                type="success"
                size="small"
                @click="resolveAlarm(row)"
            >
              标记为已处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
            v-model:current-page="alarmPagination.current"
            v-model:page-size="alarmPagination.size"
            :total="alarmPagination.total"
            @current-change="loadAlarms"
            layout="total, prev, pager, next, jumper"
        />
      </div>
    </el-card>

    <!-- 添加/编辑通知规则对话框 -->
    <el-dialog
        v-model="notificationDialog.visible"
        :title="notificationDialog.isEdit ? '编辑通知规则' : '添加通知规则'"
        width="50%"
    >
      <el-form :model="notificationDialog.form" label-width="120px">
        <el-form-item label="报警类型" prop="alarm_type">
          <el-select v-model="notificationDialog.form.alarm_type" placeholder="请选择报警类型">
            <el-option
                v-for="type in alarmTypeOptionsForNotification"
                :key="type.value"
                :label="type.label"
                :value="type.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="适用区域" prop="place">
          <el-select
              v-model="notificationDialog.form.place"
              placeholder="请选择区域（留空表示所有区域）"
              clearable
          >
            <el-option
                v-for="place in places"
                :key="place"
                :label="place"
                :value="place"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="通知人员" prop="staff_names">
          <el-select
              v-model="notificationDialog.form.staff_names"
              multiple
              placeholder="请选择通知人员"
          >
            <el-option
                v-for="staff in staffList"
                :key="staff"
                :label="staff"
                :value="staff"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="通知方式" prop="noticeWay">
          <el-select v-model="notificationDialog.form.noticeWay" placeholder="请选择通知方式">
            <el-option
                v-for="method in notificationMethods"
                :key="method.value"
                :label="method.label"
                :value="method.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="notificationDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitNotificationRule">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onMounted, toRaw} from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import eqAPI from '@/api/equipment_management.js'
import asAPI from '@/api/AlertSettings.js'

// 报警类型选项（使用中文值）
const alarmTypeOptionsForNotification = [
  { value: '温度', label: '温度' },
  { value: '湿度', label: '湿度' },
  { value: '氨气', label: '氨气' },
  { value: '二氧化碳', label: '二氧化碳' },
  { value: '气压', label: '气压' },
  { value: '电流', label: '电流' }
]

// 通知方式选项
const notificationMethods = [
  { value: '邮件', label: '邮件' },
  { value: '电话', label: '电话' },
  { value: '短信', label: '短信' }
]

// 报警类型选项（使用中文值）
const alarmTypes = [
  { value: '温度', label: '温度' },
  { value: '湿度', label: '湿度' },
  { value: '氨气', label: '氨气' },
  { value: '二氧化碳', label: '二氧化碳' },
  { value: '气压', label: '气压' },
  { value: '电流', label: '电流' }
]

// 获取所有区域列表
const fetchPlaces = async () => {
  return (await eqAPI.get_all_location_of_device("温度传感器")).data
}

// 获取所有工作人员列表
const fetchStaffList = async () => {
  const response = (await asAPI.getAll_admin_name()).data
  if (response.code === 0) {
    return response.data
  }
  return []
}

// 获取指定区域的阈值设置
const fetchThresholds = async (place) => {
  return (await asAPI.selectThresholdTypeThreshold(place)).data
}

// 获取报警通知规则
const fetchNotificationRules = async () => {
  try {
    const response = (await asAPI.regulationandalarmSearch()).data
    if (response.code === 0) {
      return response.data.map(rule => ({
        id: rule.regulationId,
        alarm_type: rule.alarmTypes,
        place: rule.place,
        staff_names: rule.personName ? rule.personName.split(',') : [],
        noticeWay: rule.noticeWay
      }))
    }
    return []
  } catch (error) {
    console.error('获取通知规则失败:', error)
    return []
  }
}

// 添加通知规则
const saveNotificationRule = async (rule) => {
  const params = {
    place: rule.place || '',
    alarmTypes: rule.alarm_type,
    personName: rule.staff_names.join(','),
    noticeWay: rule.noticeWay
  }

  const response = (await asAPI.regulationandalarmCreate(
      params.place,
      params.alarmTypes,
      params.personName,
      params.noticeWay
  )).data

  return response.code === 0
}

// 更新通知规则
const updateNotificationRule = async (rule) => {
  const params = {
    place: rule.place || '',
    alarmTypes: rule.alarm_type,
    personName: rule.staff_names.join(','),
    noticeWay: rule.noticeWay
  }

  const response = (await asAPI.regulationandalarmUpdate(
      rule.id,
      params.place,
      params.alarmTypes,
      params.personName,
      params.noticeWay
  )).data

  return response.code === 0
}

// 删除通知规则
const deleteNotificationRuleOnServer = async (id) => {
  const response = (await asAPI.regulationandalarmDelete(id)).data
  return response.code === 0
}

// 获取报警记录（真实API）
const fetchAlarms = async (params) => {
  try {
    const response = (await asAPI.getAlarms(params)).data
    // 适配新的API响应结构
    const records = response.rows.map(row => ({
      ...row,
      // 转换通知人员格式
      notifiedStaff: row.personName ? row.personName.split(',') : []
    }))

    return {
      records: records,
      total: response.total
    }
  } catch (error) {
    console.error('获取报警记录失败:', error)
    ElMessage.error('获取报警记录失败')
    return {records: [], total: 0}
  }
}


// 响应式数据
const places = ref([])
const staffList = ref([])
const selectedPlace = ref('')
const thresholds = ref([])
const alarms = ref([])
const alarmLoading = ref(false)
const notificationRules = ref([])

const alarmFilter = ref({
  type: '',
  place: '',
  dateRange: []
})

const alarmPagination = ref({
  current: 1,
  size: 10,
  total: 0
})

const notificationDialog = ref({
  visible: false,
  isEdit: false,
  form: {
    id: null,
    alarm_type: '',
    place: '',
    staff_names: [],
    noticeWay: '邮件'
  }
})

// 方法
const loadPlaces = async () => {
  places.value = await fetchPlaces()
  if (places.value.length > 0) {
    selectedPlace.value = places.value[0]
    loadThresholds()
  }
}

const loadStaffList = async () => {
  staffList.value = await fetchStaffList()
}

const loadThresholds = async () => {
  if (!selectedPlace.value) return
  thresholds.value = await fetchThresholds(selectedPlace.value)
}

const loadNotificationRules = async () => {
  notificationRules.value = await fetchNotificationRules()
}

const saveThreshold = async (threshold) => {
  const row = toRaw(threshold)
  const a = (await asAPI.updateThresholdTypeThreshold(row.thresholdType, row.place, row.upValue, row.downValue)).data
  if (a.data === '阈值更新成功') {
    ElMessage.success(a.data)
    loadThresholds()
  } else {
    ElMessage.error(a.message)
  }
}

const loadAlarms = async () => {
  alarmLoading.value = true
  try {
    // 构造查询参数
    const params = {
      alarmTypes: alarmFilter.value.type === '' ? null : alarmFilter.value.type,
      place: alarmFilter.value.place === '' ? null : alarmFilter.value.place,
      page: alarmPagination.value.current - 1,
      size: alarmPagination.value.size
    }

    // 添加时间范围参数
    if (alarmFilter.value.dateRange && alarmFilter.value.dateRange.length === 2) {
      params.startTime = alarmFilter.value.dateRange[0]
      params.endTime = alarmFilter.value.dateRange[1]
    }

    // 调用API获取报警记录
    const result = await fetchAlarms(params)
    alarms.value = result.records
    alarmPagination.value.total = result.total
  } finally {
    alarmLoading.value = false
  }
}

const resolveAlarm = async (alarm) => {
  try {
    const out = (await asAPI.handleAlarm(alarm.alertId)).data
    if (out.code === 0) {
      ElMessage.success(out.data)
      // 重新加载报警记录
      loadAlarms()
    } else {
      ElMessage.error('处理报警失败')
    }
  } catch (error) {
    console.error('处理报警失败:', error)
    ElMessage.error('处理报警失败')
  }
}

const showAddNotificationDialog = () => {
  notificationDialog.value = {
    visible: true,
    isEdit: false,
    form: {
      id: null,
      alarm_type: '',
      place: '',
      staff_names: [],
      noticeWay: '邮件'
    }
  }
}

const editNotificationRule = (rule) => {
  notificationDialog.value = {
    visible: true,
    isEdit: true,
    form: {
      id: rule.id,
      alarm_type: rule.alarm_type,
      place: rule.place,
      staff_names: [...rule.staff_names],
      noticeWay: rule.noticeWay || '邮件'
    }
  }
}

const submitNotificationRule = async () => {
  if (!notificationDialog.value.form.alarm_type) {
    ElMessage.warning('请选择报警类型')
    return
  }

  if (notificationDialog.value.form.staff_names.length === 0) {
    ElMessage.warning('请至少选择一个通知人员')
    return
  }

  if (!notificationDialog.value.form.noticeWay) {
    ElMessage.warning('请选择通知方式')
    return
  }

  const ruleData = {
    ...notificationDialog.value.form,
    staff_names: [...notificationDialog.value.form.staff_names]
  }

  try {
    let success = false

    if (notificationDialog.value.isEdit) {
      success = await updateNotificationRule(ruleData)
    } else {
      success = await saveNotificationRule(ruleData)
    }

    if (success) {
      ElMessage.success(notificationDialog.value.isEdit ? '规则更新成功' : '规则添加成功')
      notificationDialog.value.visible = false
      loadNotificationRules()
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

const deleteNotificationRule = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const success = await deleteNotificationRuleOnServer(id)
    if (success) {
      ElMessage.success('规则删除成功')
      loadNotificationRules()
    } else {
      ElMessage.error('删除失败')
    }
  } catch {
    // 用户取消了删除
  }
}

const getAlarmTypeName = (type) => {
  const map = {
    temperature: '温度',
    humidity: '湿度',
    ammonia: '氨气',
    co2: '二氧化碳',
    air_pressure: '气压',
    electricity: '电流'
  }
  return map[type] || type
}

// 更新为中文类型标签
const getAlarmTagType = (type) => {
  const map = {
    '温度': 'danger',
    '湿度': 'warning',
    '氨气': 'danger',
    '二氧化碳': 'warning',
    '气压': 'info',
    '电流': 'danger'
  }
  return map[type] || ''
}

const getMinValue = (type) => {
  const map = {
    temperature: -50,
    humidity: 0,
    ammonia: 0,
    co2: 0,
    air_pressure: 0,
    electricity: 0
  }
  return map[type] || 0
}

const getMaxValue = (type) => {
  const map = {
    temperature: 100,
    humidity: 100,
    ammonia: 1000,
    co2: 10000,
    air_pressure: 2000,
    electricity: 100
  }
  return map[type] || 1000
}

const getPrecision = (type) => {
  const integerTypes = ['湿度', '氨气', '二氧化碳']
  return integerTypes.includes(type) ? 0 : 1
}

const getStep = (type) => {
  return getPrecision(type) === 0 ? 1 : 0.1
}

const getUnit = (type) => {
  const map = {
    '温度': '℃',
    '湿度': '%',
    '氨气': 'ppm',
    '二氧化碳': 'ppm',
    '气压': 'kPa',
    '电流': 'A'
  }
  return map[type] || ''
}

const formatValue = (type, value) => {
  const unit = getUnit(type)
  if (getPrecision(type) === 0) {
    return `${parseInt(value)}${unit}`
  }
  return `${parseFloat(value).toFixed(1)}${unit}`
}

// 生命周期钩子
onMounted(() => {
  loadPlaces()
  loadStaffList()
  loadNotificationRules()
  loadAlarms()
})
</script>

<style scoped>
.warning-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.threshold-card,
.alarm-card,
.notification-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.threshold-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.unit {
  margin-left: 5px;
  color: #666;
  font-size: 14px;
}

.staff-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>
