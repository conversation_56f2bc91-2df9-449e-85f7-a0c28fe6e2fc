package com.agriguard.controller;

import com.agriguard.entity.Regulation;
import com.agriguard.pojo.Result;
import com.agriguard.service.iotservice.RegulationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/regulationandalarm")
@RequiredArgsConstructor
public class RegulationController {
    private final RegulationService regulationAndAlarmService;

    //创建报警规则
    @PostMapping
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<Regulation> create(@RequestBody @Valid Regulation regulation) {
        regulationAndAlarmService.createRegulation(regulation);
        return Result.success(regulation);
    }
    //更新报警规则
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<Regulation> update(@PathVariable Integer id, @RequestBody @Valid Regulation regulation) {
        regulation.setRegulationId(id);
        regulationAndAlarmService.updateRegulation(regulation);
        return Result.success(regulation);
    }
    //删除报警规则
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ADMIN')")
    public Result<?> delete(@PathVariable Integer id) {
        regulationAndAlarmService.deleteRegulation(id);
        return Result.success("报警规则删除成功");
    }
    //根据ID查询报警规则
    @GetMapping("/{id}")
    public Result<Regulation> getById(@PathVariable Integer id) {
        return Result.success(regulationAndAlarmService.getById(id));
    }
    //根据条件查询报警规则
    @GetMapping
    public Result<List<Regulation>> search(
            @RequestParam(required = false) String place,
            @RequestParam(required = false) String alarmType,
            @RequestParam(required = false) String personName,
            @RequestHeader String Authorization
    ) {
        Regulation condition = new Regulation();
        condition.setPlace(place);
        condition.setAlarmTypes(alarmType);
        condition.setPersonName(personName);
        return Result.success(regulationAndAlarmService.searchRegulations(condition));
    }

    @GetMapping("/all_admin_name")
    public Result<List<String>> all_admin_name(@RequestHeader String Authorization) {
        return Result.success(regulationAndAlarmService.all_admin_name());
    }

}
