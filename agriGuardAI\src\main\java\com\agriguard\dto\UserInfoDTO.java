package com.agriguard.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoDTO {
    private Integer userId;
    private String userName;
    private String realName;
    private String email;
    private String phone;
    private LocalDateTime loginDate;
    private String token;
    private List<String> roles;
}
