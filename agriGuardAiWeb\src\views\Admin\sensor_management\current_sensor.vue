<template>
  <div class="electricity-sensor-container">
    <!-- 标题 -->
    <div class="header">
      <h2>电流传感器监控</h2>
      <div class="filter-group">
        <span class="filter-label">位置筛选：</span>
        <el-select v-model="locationFilter" placeholder="选择位置" @change="refreshData" style="width: 200px; margin-right: 10px;">
          <el-option v-for="location in locations" :key="location" :label="location" :value="location"/>
        </el-select>
      </div>
      <el-button type="primary" @click="refreshData">刷新数据</el-button>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-odometer"></i>
              </div>
              <div class="card-info">
                <div class="card-title">传感器总数</div>
                <div class="card-value">{{ overview.totalSensors }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div class="card-content">
              <div class="card-icon working">
                <i class="el-icon-success"></i>
              </div>
              <div class="card-info">
                <div class="card-title">正常工作</div>
<!--                <div class="card-value">{{ overview.workingSensors }}</div>-->
                <div class="card-value">{{ overview.totalSensors }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" :class="{ 'error-card': overview.errorSensors > 0 }" @click="showAbnormalSensors">
            <div class="card-content">
              <div class="card-icon error">
                <i class="el-icon-warning"></i>
              </div>
              <div class="card-info">
                <div class="card-title">异常设备</div>
<!--                <div class="card-value">{{ overview.errorSensors }}</div>-->
                <div class="card-value">0</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <!-- 模板中异常设备展示 -->
    <el-dialog v-model="abnormalDialogVisible" title="异常电流传感器列表" width="80%">
      <div v-if="abnormalSensorList.length > 0">
        <el-table :data="abnormalSensorList" style="width: 100%" v-loading="abnormalLoading" class="sensor-table"
                  :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
          <el-table-column prop="deviceId" label="设备ID" width="120" />
          <el-table-column prop="deviceName" label="设备名称" width="180" />
          <el-table-column prop="place" label="位置" width="150" />
          <el-table-column label="电流(A)" width="150">
            <template #default="{ row }">
              <span class="current-value">{{ row.ele }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="180" />
          <el-table-column label="操作" width="200" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="info" size="small" @click="viewHistory(row)" icon="Histogram">历史</el-button>
                <el-button type="primary" size="small" @click="showDetailDialog(row)" icon="View">详情</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="no-abnormal">
        <el-empty description="无异常设备" />
      </div>
    </el-dialog>
    <!-- 实时电流折线图 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>实时电流变化 (60分钟)</span>
        </div>
      </template>
      <div class="chart-container">
        <div ref="realTimeChart" style="width: 100%; height: 400px;"></div>
      </div>
    </el-card>

    <!-- 趋势和分布图 -->
    <div class="double-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>电流趋势 (24小时)</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="trendChart" style="width: 100%; height: 350px;"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>电流分布 (24小时)</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="distributionChart" style="width: 100%; height: 350px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 传感器管理区域 -->
    <div class="sensor-container">
      <!-- 操作区 -->
      <div class="operation-area">
        <!-- 搜索和添加 -->
        <div class="search-add-group">
          <el-input v-model="searchQuery" placeholder="设备ID/名称/位置" class="search-input" clearable @clear="handleSearch"
                    @keyup.enter="handleSearch">
            <template #append>
              <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
          <el-button type="primary" @click="showAddSensorDialog" icon="Plus">添加传感器</el-button>
        </div>
      </div>

      <!-- 电流传感器数据表格 -->
      <el-table :data="sensorList" style="width: 100%" v-loading="loading" class="sensor-table"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
        <el-table-column prop="deviceId" label="设备ID" width="120" />
        <el-table-column prop="deviceName" label="设备名称" width="180" />
        <el-table-column prop="place" label="位置" width="150" />
        <el-table-column label="电流(A)" width="150">
          <template #default="{ row }">
            <span class="current-value">{{ row.ele }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="info" size="small" @click="viewHistory(row)" icon="Histogram">历史</el-button>
              <el-button type="primary" size="small" @click="showDetailDialog(row)" icon="View">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="pagination">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
                       layout="total, sizes, prev, pager, next, jumper" :total="totalItems" @size-change="handleSizeChange"
                       @current-change="handleCurrentChange" />
      </div>

      <!-- 添加传感器对话框 -->
      <el-dialog v-model="addDialogVisible" title="添加新电流传感器" width="500px" class="add-dialog">
        <el-form :model="newSensorForm" :rules="sensorRules" ref="newSensorFormRef" label-position="top">
          <el-form-item label="设备ID" prop="deviceId">
            <el-input v-model="newSensorForm.deviceId" placeholder="输入电流传感器设备ID" />
            <div class="form-tip">只需输入电流传感器设备ID，系统将自动识别其他信息</div>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="addDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="addNewSensor">确认添加</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 历史记录对话框 -->
      <el-dialog v-model="historyDialogVisible" :title="`电流历史记录 - ${selectedSensor?.deviceName || ''}`" width="900px"
                 class="history-dialog">
        <div class="history-filter">
          <el-date-picker v-model="historyDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                          end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="fetchHistoryData" />
        </div>
        <el-table :data="historyData" height="400" style="width: 100%" v-loading="historyLoading">
          <el-table-column prop="collectDate" label="时间" width="180" />
          <el-table-column prop="collectHour" label="小时段" width="120">
            <template #default="{ row }">
              {{ row.collectHour }}:00
            </template>
          </el-table-column>
          <el-table-column prop="maxEle" label="最高电流(A)" width="120">
            <template #default="{ row }">
              <span class="current-value">{{ row.maxEle }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="minEle" label="最低电流(A)" width="120">
            <template #default="{ row }">
              <span class="current-value">{{ row.minEle }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="avgEle" label="平均电流(A)" width="120">
            <template #default="{ row }">
              <span class="current-value">{{ row.avgEle }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="dataCount" label="数据量" width="100" sortable />
        </el-table>
        <div class="pagination">
          <el-pagination v-model:current-page="historyPage" v-model:page-size="historyPageSize" :page-sizes="[10, 20, 30]"
                         layout="total, sizes, prev, pager, next, jumper" :total="historyTotalItems"
                         @size-change="handleHistorySizeChange" @current-change="handleHistoryPageChange" />
        </div>
      </el-dialog>

      <!-- 设备详情对话框 -->
      <el-dialog v-model="detailDialogVisible" :title="`设备详情 - ${currentDeviceDetail?.deviceName || ''}`" width="700px"
                 class="detail-dialog">
        <div v-if="currentDeviceDetail" class="device-detail">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>设备ID：</label>
                <span>{{ currentDeviceDetail.deviceId }}</span>
              </div>
              <div class="detail-item">
                <label>设备型号：</label>
                <span>{{ currentDeviceDetail.model }}</span>
              </div>
              <div class="detail-item">
                <label>制造商：</label>
                <span>{{ currentDeviceDetail.factory }}</span>
              </div>
              <div class="detail-item">
                <label>安装日期：</label>
                <span>{{ currentDeviceDetail.installTime }}</span>
              </div>
            </el-col>

            <el-col :span="12">
              <div class="detail-item">
                <label>设备类型：</label>
                <span>{{ currentDeviceDetail.type }}</span>
              </div>
              <div class="detail-item">
                <label>设备名称：</label>
                <span>{{ currentDeviceDetail.deviceName }}</span>
              </div>
              <div class="detail-item">
                <label>位置：</label>
                <span>{{ currentDeviceDetail.place }}</span>
              </div>
              <div class="detail-item">
                <label>上次更新：</label>
                <span>{{ currentDeviceDetail.updateTime }}</span>
              </div>
            </el-col>
          </el-row>

          <div class="detail-item description">
            <label>设备描述：</label>
            <p>{{ currentDeviceDetail.description }}</p>
          </div>

          <div class="detail-item current-current">
            <label>当前电流：</label>
            <span class="current-value">{{ currentDeviceDetail.ele }} A</span>
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="detailDialogVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, reactive } from 'vue'
import { ElMessage } from 'element-plus'

import seAPI from '@/api/sensor_management.js'
import eqAPI from '@/api/equipment_management.js'

import * as echarts from 'echarts/core'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  CanvasRenderer
])

const locationFilter = ref('');
const locations = ref([]);
// 异常设备
const abnormalDialogVisible = ref(false);
const abnormalSensorList = ref([]);
const abnormalLoading = ref(false);

// 异常设备对话框显示逻辑
const showAbnormalSensors = async () => {
  if (overview.value.errorSensors <= 0) return;

  abnormalDialogVisible.value = true;
  await fetchAbnormalSensors();
};

// 模板中异常设备展示
const fetchAbnormalSensors = async () => {
  abnormalLoading.value = true;
  abnormalSensorList.value = []; // Clear previous data

  try {
    // First get the list of abnormal device IDs
    const response = await eqAPI.abnormal_list("电流传感器",locationFilter.value);
    const abnormalDeviceIds = response.data.data;

    if (!abnormalDeviceIds || abnormalDeviceIds.length === 0) {
      return;
    }

    // Fetch details for each abnormal device
    const fetchPromises = abnormalDeviceIds.map(deviceId =>
        seAPI.get_electricity_list(1, 1, deviceId)
    );

    const results = await Promise.all(fetchPromises);

    // Combine all results
    const combinedResults = results
        .filter(res => res.data.data && res.data.data.rows && res.data.data.rows.length > 0)
        .flatMap(res => res.data.data.rows);

    abnormalSensorList.value = combinedResults;

  } catch (error) {
    console.error('获取异常传感器列表失败:', error);
    ElMessage.error('获取异常传感器列表失败');
  } finally {
    abnormalLoading.value = false;
  }
};

/**
 * 获取24小时电流趋势数据
 * @returns {Promise<Array<{hour: string, maxEle: number, minEle: number, avgEle: number}>>}
 */
const fetch24HourTrendData = async () => {
  const response = await seAPI.get_electricity_history_24hours(locationFilter.value);
  const apiData = response.data.data;

  // 转换数据格式并排序
  const trendData = apiData.map(item => ({
    hour: `${item.collectHour}:00`,
    maxEle: item.maxEle,
    minEle: item.minEle,
    avgEle: item.avgEle,
    // 添加排序用的时间戳
    timestamp: new Date(`${item.collectDate} ${item.collectHour}:00:00`).getTime()
  }))
      .sort((a, b) => a.timestamp - b.timestamp) // 按时间排序
      .map(item => ({
        hour: item.hour,
        maxEle: item.maxEle,
        minEle: item.minEle,
        avgEle: item.avgEle
      }));

  return trendData;
}

const fetch24HourDistributionData = async () => {
  try {
    const response = await seAPI.get_electricity_history_24hours_pie(locationFilter.value);
    const apiData = response.data.data;

    // 定义电流范围映射
    const rangeMap = {
      1: '0-0.3A',
      2: '0.3-0.6A',
      3: '0.6-0.9A',
      4: '0.9-1.2A',
      5: '1.2-1.5A'
    };

    // 转换数据格式并按fanwei排序
    const distributionData = apiData
        .map(item => ({
          fanwei: item.fanwei,
          count: item.eleNum,
          range: rangeMap[item.fanwei] || '未知范围'
        }))
        .sort((a, b) => a.fanwei - b.fanwei)
        .map(item => ({
          range: item.range,
          count: item.count
        }));

    // 确保所有电流范围都存在，即使数据中没有
    const allRanges = Object.values(rangeMap);
    const result = allRanges.map(range => {
      const existing = distributionData.find(item => item.range === range);
      return existing || { range, count: 0 };
    });

    return result;
  } catch (error) {
    console.error('获取电流分布数据失败:', error);
    return [];
  }
}

// 响应式数据
const overview = ref({
  totalSensors: 0,
  workingSensors: 0,
  errorSensors: 0
})

const realTimeChart = ref(null)
const trendChart = ref(null)
const distributionChart = ref(null)

// 初始化图表
const initCharts = () => {
  nextTick(async () => {
    // 获取数据
    const realTimeData = (await seAPI.get_electricity_avg_current(locationFilter.value)).data.data
    const trendData = await fetch24HourTrendData()
    const distributionData = await fetch24HourDistributionData()

    // 实时电流图表
    const realTimeChartInstance = echarts.init(realTimeChart.value)
    realTimeChartInstance.setOption({
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>{a0}: {c0}A'
      },
      legend: {
        data: ['电流']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: realTimeData.map(item => {
          const date = new Date(item.updateTime)
          return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
        })
      },
      yAxis: {
        type: 'value',
        name: '电流(A)',
        axisLabel: {
          formatter: '{value}A'
        }
      },
      series: [
        {
          name: '电流',
          type: 'line',
          data: realTimeData.map(item => item.ele),
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#5470C6'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
              { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
            ])
          },
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          }
        }
      ]
    })

    // 电流趋势图表
    const trendChartInstance = echarts.init(trendChart.value)
    trendChartInstance.setOption({
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>{a0}: {c0}A<br/>{a1}: {c1}A<br/>{a2}: {c2}A'
      },
      legend: {
        data: ['最高电流', '最低电流', '平均电流']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: trendData.map(item => item.hour)
      },
      yAxis: {
        type: 'value',
        name: '电流(A)',
        axisLabel: {
          formatter: '{value}A'
        }
      },
      series: [
        {
          name: '最高电流',
          type: 'line',
          data: trendData.map(item => item.maxEle),
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#EE6666'
          }
        },
        {
          name: '最低电流',
          type: 'line',
          data: trendData.map(item => item.minEle),
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#91CC75'
          }
        },
        {
          name: '平均电流',
          type: 'line',
          data: trendData.map(item => item.avgEle),
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#5470C6'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
              { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
            ])
          }
        }
      ]
    })

    // 电流分布图表
    const distributionChartInstance = echarts.init(distributionChart.value)
    distributionChartInstance.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: distributionData.map(item => item.range)
      },
      series: [
        {
          name: '电流分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: distributionData.map(item => ({
            value: item.count,
            name: item.range,
            itemStyle: {
              color: getColorForCurrentRange(item.range)
            }
          }))
        }
      ]
    })

    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', () => {
      realTimeChartInstance.resize()
      trendChartInstance.resize()
      distributionChartInstance.resize()
    })
  })
}

// 根据电流区间获取颜色
const getColorForCurrentRange = (range) => {
  const ele = parseFloat(range.split('-')[0])
  if (ele < 0.3) return '#5470C6' // 蓝色
  if (ele < 0.6) return '#91CC75' // 绿色
  if (ele < 0.9) return '#FAC858' // 黄色
  if (ele < 1.2) return '#EE6666' // 红色
  return '#73C0DE' // 青色
}

// 加载数据
const loadData = async () => {
  try {
    // 重新初始化图表以更新数据
    initCharts()
  } catch (error) {
    ElMessage.error('数据加载失败: ' + error.message)
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    const wa = await eqAPI.abnormal("电流传感器", locationFilter.value);
    console.log('传感器数量', wa.data.data);
    overview.value = {
      totalSensors: wa.data.data.totalCount,
      workingSensors: wa.data.data.normalCount,
      errorSensors: wa.data.data.abnormalCount
    };
  } catch (error) {
    console.error('获取数据失败:', error);
    // 提供默认值
    overview.value = {
      totalSensors: 99999,
      workingSensors: 99999,
      errorSensors: 99999
    };
  }
  loadData()
  ElMessage.success('数据已刷新')
}

// 传感器列表相关状态
const sensorList = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const searchQuery = ref('');

// 添加传感器对话框相关
const addDialogVisible = ref(false);
const newSensorForm = reactive({
  deviceId: ''
});
const sensorRules = reactive({
  deviceId: [
    { required: true, message: '请输入设备ID', trigger: 'blur' }
  ]
});

// 历史记录对话框相关
const historyDialogVisible = ref(false);
const historyData = ref([]);
const historyLoading = ref(false);
const historyPage = ref(1);
const historyPageSize = ref(10);
const historyTotalItems = ref(0);
const historyDateRange = ref([]);
const selectedSensor = ref(null);

// 设备详情对话框相关
const detailDialogVisible = ref(false);
const currentDeviceDetail = reactive({
  deviceId: "",
  type: "",
  model: "",
  factory: "",
  installTime: "",
  updateTime: "",
  deviceName: "",
  place: "",
  description: "",
  ele: null
});

// 获取传感器数据
const fetchSensorData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };

    if (searchQuery.value) {
      const query = searchQuery.value.trim();
      if (/^\d+$/.test(query)) {
        params.deviceId = parseInt(query);
      } else {
        params.deviceName = query;
      }
    }

    const response = await seAPI.get_electricity_list(
        params.pageNum,
        params.pageSize,
        params.deviceId,
        params.deviceName
    );

    const result = response.data.data;
    sensorList.value = result.rows;
    totalItems.value = result.total;

  } catch (error) {
    console.error('获取电流传感器数据失败:', error);
    ElMessage.error('获取电流传感器数据失败');
  } finally {
    loading.value = false;
  }
};

// 显示添加传感器对话框
const showAddSensorDialog = () => {
  addDialogVisible.value = true;
  newSensorForm.deviceId = '';
};

// 添加新传感器
const addNewSensor = async () => {
  try {
    const response = await seAPI.add_electricity_sensor(newSensorForm.deviceId);
    if (response.data.data === '添加成功') {
      ElMessage.success('电流传感器添加成功');
      addDialogVisible.value = false;
      fetchSensorData();
    } else {
      ElMessage.error('电流传感器添加失败：' + response.data.message);
    }
  } catch (error) {
    console.error('添加电流传感器失败:', error);
    ElMessage.error('添加电流传感器失败');
  }
};

// 查看历史记录
const viewHistory = (sensor) => {
  selectedSensor.value = sensor;
  historyDialogVisible.value = true;
  historyDateRange.value = [];
  historyPage.value = 1;
  fetchHistoryData();
};

// 获取历史记录数据
const fetchHistoryData = async () => {
  if (!selectedSensor.value) return;

  historyLoading.value = true;
  try {
    const response = await seAPI.get_electricity_history_data(
        historyPage.value,
        historyPageSize.value,
        selectedSensor.value.deviceId,
        historyDateRange.value[0],
        historyDateRange.value[1]
    );

    const result = response.data.data;
    historyData.value = result.rows;
    historyTotalItems.value = result.total;
  } catch (error) {
    console.error('获取历史记录失败:', error);
    ElMessage.error('获取历史记录失败');
  } finally {
    historyLoading.value = false;
  }
};

// 显示设备详情对话框
const showDetailDialog = async (sensor) => {
  try {
    const response = await seAPI.get_electricity_detail(sensor.deviceId);
    const detail = response.data.data;
    Object.assign(currentDeviceDetail, {
      deviceId: detail.deviceId,
      type: detail.type,
      model: detail.model,
      factory: detail.factory,
      installTime: detail.installTime,
      updateTime: detail.updateTime,
      deviceName: detail.deviceName,
      place: detail.place,
      description: detail.description,
      ele: sensor.ele
    });

    detailDialogVisible.value = true;
  } catch (error) {
    console.error('获取设备详情失败:', error);
    ElMessage.error('获取设备详情失败');
  }
};

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchSensorData();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchSensorData();
};

const handleHistorySizeChange = (size) => {
  historyPageSize.value = size;
  fetchHistoryData();
};

const handleHistoryPageChange = (page) => {
  historyPage.value = page;
  fetchHistoryData();
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchSensorData();
};

// 生命周期钩子
onMounted(async () => {
  try {
    await eqAPI.get_all_location_of_device('电流传感器').then(response => {
      locations.value = response.data
      locationFilter.value = response.data[0]
    })
  } catch (error) {
    console.error('获取位置数据失败:', error);
  }
  try {
    const wa = await eqAPI.abnormal("电流传感器", locationFilter.value);
    console.log('传感器数量', wa.data.data);
    // 如果需要，将数据赋值给响应式变量
    overview.value = {
      totalSensors: wa.data.data.totalCount,
      workingSensors: wa.data.data.normalCount,
      errorSensors: wa.data.data.abnormalCount
    };
  } catch (error) {
    console.error('获取数据失败:', error);
    // 提供默认值
    overview.value = {
      totalSensors: 99999,
      workingSensors: 99999,
      errorSensors: 99999
    };
  }
  initCharts()
  await fetchSensorData()
})
</script>

<style scoped>
.electricity-sensor-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon {
  font-size: 36px;
  margin-right: 20px;
  color: #409EFF;
}

.card-icon.working {
  color: #67C23A;
}

.card-icon.error {
  color: #F56C6C;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.double-charts {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
}

.sensor-container {
  padding: 20px;
  background-color: #f5f7fa;
  color: #333;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 操作区样式 */
.operation-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-add-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-input {
  width: 300px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 表格样式 */
.sensor-table {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

:deep(.el-table__row) {
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

:deep(.el-table__cell) {
  padding: 12px 0;
}

/* 电流值样式 */
.current-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 分页控件 */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 对话框样式 */
.add-dialog,
.detail-dialog {
  border-radius: 8px;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 历史记录筛选 */
.history-filter {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 设备详情对话框样式 */
.device-detail {
  padding: 15px;
}

.detail-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #eaeaea;
}

.detail-item label {
  font-weight: 600;
  color: #5a6c84;
  display: inline-block;
  width: 80px;
}

.detail-item span {
  color: #333;
}

.description p {
  margin-top: 6px;
  color: #666;
  line-height: 1.5;
}

.current-current .current-value {
  font-size: 16px;
  font-weight: 600;
  color: #1e88e5;
}

.error-card {
  cursor: pointer;
  border: 1px solid #F56C6C;
  transition: all 0.3s ease;
}

.error-card:hover {
  box-shadow: 0 2px 12px rgba(245, 108, 108, 0.3);
}

.no-abnormal {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
