package com.agriguard.service;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.Co2RealTime;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;

import java.time.LocalDate;
import java.util.List;

public interface Co2Service {

    //定时对Co2数据进行分析，存储历史数据，并进行数据删除
    void Co2DateSys();

    // 检查设备是否已存在Co2传感器
    boolean existsByDeviceId(Integer deviceId);

    // 添加Co2传感器
    String addCo2RealTime(Integer deviceId);

    // 查询Co2传感器详情
    Device findCo2DetailById(Integer deviceId);

    // 分页查询历史数据
    PageResult<Co2RealTime> findHistoryData(Integer pageNum, Integer pageSize,
                                            Integer deviceId, LocalDate beginData, LocalDate endData);

    // 多条件分页查询实时状态
    PageResult<Co2RealTime> findLatestStatus(Integer pageNum, Integer pageSize,
                                             Integer deviceId, String deviceName, String place);

    // 获取指定位置的分钟级平均Co2
    List<Co2RealTimeByMinute> getAvgCo2(String place);

    //插入Co2数据
    void insertTemperature();

    //获取过去24小时的历史Co2数据，用于折线图展示
    List<Co2History24> getHistory24Hours(String place);

    //获取过去24小时的历史Co2数据，用于饼图展示
    List<Co2Pie> getHistory24HoursForPie(String place);
}

