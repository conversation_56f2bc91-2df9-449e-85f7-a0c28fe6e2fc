package com.agriguard;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@SpringBootApplication
@EnableScheduling // 启用定时任务
@EnableAsync

public class AgriGuardAiApplication {

    public static void main(String[] args) {
        System.out.println("JVM默认时区: " + TimeZone.getDefault().getID());
        SpringApplication.run(AgriGuardAiApplication.class, args);
    }

}
