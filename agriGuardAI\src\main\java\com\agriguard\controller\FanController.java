package com.agriguard.controller;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.FanRealTime;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import com.agriguard.service.FanService;
import com.agriguard.service.iotservice.HuaweiIotCommandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/fan")
public class FanController {
    @Autowired
    private FanService fanService;


    //多条件分页模糊查询风机状态
    @GetMapping("/list")
    public Result<PageResult<FanRealTime>> list(
            @RequestHeader String Authorization,
            Integer pageNum,
            Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) Integer fanStatus,
            @RequestParam(required = false) String place,
            @RequestParam(required = false) Integer gear
    ) {
        try {
            PageResult<FanRealTime> pageResult = fanService.findLatestStatus(pageNum, pageSize, deviceId, deviceName,fanStatus, place,gear);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //增加风机状态
    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = fanService.addFanRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success("添加成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //修改风机状态
    @PutMapping("/updatestatus")
    public Result<String> updateStatus(@RequestHeader String Authorization,
                                       @RequestParam Integer deviceId,
                                       @RequestParam Integer fanStatus
    ) {
        try {
            fanService.updateStatus(deviceId, fanStatus);
            return Result.success("修改成功");
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("修改失败: " + e.getMessage());
        }
    }

    //修改风机档位
    @PutMapping("/updategear")
    public Result<String> updateGear(@RequestHeader String Authorization,
                                     @RequestParam Integer deviceId,
                                     @RequestParam Integer gear
    ) {
        try {
            fanService.updateGear(deviceId, gear);
            return Result.success("修改成功");
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("修改失败: " + e.getMessage());
        }
    }


    //获取单台风机详情
    @GetMapping("/detail")  // 注意这里是 GET 请求
    public Result<Device> getFanDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device fanDetail = fanService.findFanDetailById(deviceId);
            return Result.success(fanDetail);
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //获取单台风机历史数据
    @GetMapping("/history")
    public Result<PageResult<FanRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData
    ) {
        try {
            PageResult<FanRealTime> pageResult = fanService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
