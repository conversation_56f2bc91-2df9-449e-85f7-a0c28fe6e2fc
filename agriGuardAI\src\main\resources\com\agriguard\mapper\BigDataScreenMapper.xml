<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.BigDataScreenMapper">
    <!-- 设备类型分布SQL -->
    <select id="countDevicesByType" resultType="com.agriguard.dto.BigDataScreen.DeviceTypeDistribution">
        SELECT type as deviceType,
               COUNT(device_id) as count
        FROM device
        WHERE type IS NOT NULL
        GROUP BY type
    </select>

    <!-- 查询最近20分钟内每分钟的温度平均值 -->
    <select id="selectHourlyAvgTemp" resultType="com.agriguard.dto.BigDataScreen.TempHumidityByPeriod">
        SELECT
            DATE_FORMAT(update_time, '%H:%i') AS timePeriod,
            ROUND(COALESCE(AVG(temp), 0), 2) AS avgTemp
        FROM temperature_data_cache
        WHERE update_time >= NOW() - INTERVAL 20 MINUTE
        GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i'), timePeriod
        ORDER BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i')
    </select>


    <!-- 查询最近20分钟内每分钟的湿度(hum)平均值 -->
    <select id="selectHourlyAvgHumidity" resultType="com.agriguard.dto.BigDataScreen.TempHumidityByPeriod">
        SELECT
            DATE_FORMAT(update_time, '%H:%i') AS timePeriod,
            ROUND(COALESCE(AVG(hum), 0), 2) AS avgHumidity
        FROM humidity_data_cache
        WHERE update_time >= NOW() - INTERVAL 20 MINUTE
        GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i'), timePeriod
        ORDER BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i')
    </select>

    <!--传感器数量-->
    <select id="selectSensorCounts" resultType="com.agriguard.dto.BigDataScreen.SensorCountDTO">
        SELECT
            type AS sensorType,
            COUNT(*) AS count
        FROM device
        WHERE type IN ('气压传感器', '氨气传感器', 'Co2传感器', '电流传感器', '湿度传感器', '温度传感器')
        GROUP BY type
    </select>
    <!--气压实时平均值-->
    <select id="selectAvgAirPressure" resultType="com.agriguard.dto.BigDataScreen.AvgAirPressureDTO">
        SELECT
            ROUND(COALESCE(AVG(apre), 0), 2) AS avgAirPressure
        FROM air_pressure_real_time
    </select>
    <!-- 新增CO2平均值查询 -->

    <select id="selectHourlyAvgCo2" resultType="com.agriguard.dto.BigDataScreen.GasConcentrationByPeriod">
        SELECT
            DATE_FORMAT(update_time, '%H:%i') AS timePeriod,
            ROUND(AVG(co2), 2) AS avgCo2
        FROM co2_data_cache
        WHERE update_time >= NOW() - INTERVAL 20 MINUTE
        GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i'), timePeriod
        ORDER BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i')
    </select>
    <!-- 查询最近20分钟内每分钟的氨气(amm)平均值 -->
    <select id="selectHourlyAvgAmmonia" resultType="com.agriguard.dto.BigDataScreen.GasConcentrationByPeriod">
        SELECT
            DATE_FORMAT(update_time, '%H:%i') AS timePeriod,
            ROUND(AVG(amm), 2) AS avgAmm
        FROM ammonia_data_cache
        WHERE update_time >= NOW() - INTERVAL 20 MINUTE
        GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i'), timePeriod
        ORDER BY DATE_FORMAT(update_time, '%Y-%m-%d %H:%i')
    </select>
    <!-- 仓库设备数量统计 -->
    <select id="countWarehouseEquipment" resultType="com.agriguard.dto.BigDataScreen.WarehouseEquipmentCount">
        SELECT
            type as equipmentType,
            COUNT(device_id) as count
        FROM device
        WHERE (type IN ('风机', '水帘', '窗户', '加热片'))
           OR (type LIKE '%传感器')
        GROUP BY type
        ORDER BY FIELD(type, '风机', '水帘', '窗户', '加热片', '传感器')
    </select>
</mapper>
