import axios from 'axios';
import {useAuthStore} from '@/stores/auth'

// 使用代理路径，实际请求会被代理到后端服务器
const API_BASE_URL = '/api';

const auth = axios.create({
    baseURL: API_BASE_URL,
});


auth.interceptors.request.use(config => {
    const authStore = useAuthStore();
    const Authorization = authStore.user?.token;
    if (Authorization) {
        config.headers.Authorization = Authorization;
    }
    return config;
}, error => {
    return Promise.reject(error);
});

export default {
    async get_temperature_list(pageNum, pageSize, deviceId, deviceName, place) {
        return auth.get(`/temp/list`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                deviceName: deviceName,
                place: place
            }
        })
    },

    async add_temperature_sensor(deviceId) {
        return auth.post(`/temp/add`, null, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_temperature_detail(deviceId) {
        return auth.get(`/temp/detail`, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_temperature_history_data(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/temp/history`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                beginData: beginData,
                endData: endData
            }
        })
    },

    async get_temperature_avg_temperature(place) {
        return auth.get(`/temp/realtemp`, {
            params: {
                place: place
            }
        })
    },

    async get_temperature_history_24hours(place) {
        return auth.get(`/temp/history24`, {
            params: {
                place: place
            }
        })
    },

    async get_temperature_history_24hours_pie(place) {
        return auth.get(`/temp/history24pie`, {
            params: {
                place: place
            }
        })
    },

    async get_humidity_list(pageNum, pageSize, deviceId, deviceName, place) {
        return auth.get(`/humidity/list`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                deviceName: deviceName,
                place: place
            }
        })
    },

    async add_humidity_sensor(deviceId) {
        return auth.post(`/humidity/add`, null, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_humidity_detail(deviceId) {
        return auth.get(`/humidity/detail`, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_humidity_history_data(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/humidity/history`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                beginData: beginData,
                endData: endData
            }
        })
    },

    async get_humidity_avg_humidity(place) {
        return auth.get(`/humidity/avg`, {
            params: {
                place: place
            }
        })
    },

    async get_humidity_history_24hours(place) {
        return auth.get(`/humidity/history24`, {
            params: {
                place: place
            }
        })
    },

    async get_humidity_history_24hours_pie(place) {
        return auth.get(`/humidity/history24pie`, {
            params: {
                place: place
            }
        })
    },

    async get_co2_list(pageNum, pageSize, deviceId, deviceName, place) {
        return auth.get(`/co2/list`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                deviceName: deviceName,
                place: place
            }
        })
    },

    async add_co2_sensor(deviceId) {
        return auth.post(`/co2/add`, null, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_co2_detail(deviceId) {
        return auth.get(`/co2/detail`, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_co2_history_data(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/co2/history`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                beginData: beginData,
                endData: endData
            }
        })
    },

    async get_co2_avg_co2(place) {
        return auth.get(`/co2/avg`, {
            params: {
                place: place
            }
        })
    },

    async get_co2_history_24hours(place) {
        return auth.get(`/co2/history24`, {
            params: {
                place: place
            }
        })
    },

    async get_co2_history_24hours_pie(place) {
        return auth.get(`/co2/history24pie`, {
            params: {
                place: place
            }
        })
    },

    async get_ammonia_list(pageNum, pageSize, deviceId, deviceName, place) {
        return auth.get(`/ammonia/list`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                deviceName: deviceName,
                place: place
            }
        })
    },

    async add_ammonia_sensor(deviceId) {
        return auth.post(`/ammonia/add`, null, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_ammonia_detail(deviceId) {
        return auth.get(`/ammonia/detail`, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_ammonia_history_data(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/ammonia/history`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                beginData: beginData,
                endData: endData
            }
        })
    },

    async get_ammonia_avg_ammonia(place) {
        return auth.get(`/ammonia/avg`, {
            params: {
                place: place
            }
        })
    },

    async get_ammonia_history_24hours(place) {
        return auth.get(`/ammonia/history24`, {
            params: {
                place: place
            }
        })
    },

    async get_ammonia_history_24hours_pie(place) {
        return auth.get(`/ammonia/history24pie`, {
            params: {
                place: place
            }
        })
    },

    // Add these to sensor_management.js
    async get_airpressure_list(pageNum, pageSize, deviceId, deviceName, place) {
        return auth.get(`/airpressure/list`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                deviceName: deviceName,
                place: place
            }
        })
    },

    async add_airpressure_sensor(deviceId) {
        return auth.post(`/airpressure/add`, null, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_airpressure_detail(deviceId) {
        return auth.get(`/airpressure/detail`, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_airpressure_history_data(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/airpressure/history`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                beginData: beginData,
                endData: endData
            }
        })
    },

    async get_airpressure_avg(place) {
        return auth.get(`/airpressure/avg`, {
            params: {
                place: place
            }
        })
    },

    async get_airpressure_history_24hours(place) {
        return auth.get(`/airpressure/history24`, {
            params: {
                place: place
            }
        })
    },

    async get_airpressure_history_24hours_pie(place) {
        return auth.get(`/airpressure/history24pie`, {
            params: {
                place: place
            }
        })
    },

    // Add these to sensor_management.js
    async get_electricity_list(pageNum, pageSize, deviceId, deviceName) {
        return auth.get(`/electricity/list`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                deviceName: deviceName
            }
        })
    },

    async add_electricity_sensor(deviceId) {
        return auth.post(`/electricity/add`, null, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_electricity_detail(deviceId) {
        return auth.get(`/electricity/detail`, {
            params: {
                deviceId: deviceId
            }
        })
    },

    async get_electricity_history_data(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/electricity/history`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                beginData: beginData,
                endData: endData
            }
        })
    },

    async get_electricity_avg_current(place) {
        return auth.get(`/electricity/avg`, {
            params: {
                place: place
            }
        })
    },

    async get_electricity_history_24hours(place) {
        return auth.get(`/electricity/history24`, {
            params: {
                place: place
            }
        })
    },

    async get_electricity_history_24hours_pie(place) {
        return auth.get(`/electricity/history24pie`, {
            params: {
                place: place
            }
        })
    },

}
