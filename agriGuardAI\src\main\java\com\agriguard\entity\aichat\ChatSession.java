package com.agriguard.entity.aichat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天会话实体
 * 用于保存AI对话的上下文信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatSession {
    
    /**
     * 会话ID，唯一标识
     */
    private String sessionId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 会话标题
     */
    private String title;
    
    /**
     * 对话消息列表
     */
    private List<ChatMessage> messages;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 会话状态：1-活跃，0-已结束
     */
    private Integer status;
    
    /**
     * 累计消耗的token数量
     */
    private Integer totalTokens;
} 