package com.agriguard.service.iotservice;

import com.agriguard.mapper.NappeMapper;
import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class WaterCurtainHandler implements CommandHandlerService {


    @Autowired
    private NappeMapper nappeMapper;
    @Override
    public boolean supports(String commandType) {
        return commandType.startsWith("WP");
    }

    @Override
    public Result<String> handle(String commandName, String parasvalue, Integer deviceId) {

        //生成一个统一的时间便于实时状态表和历史数据表统一时间
        LocalDateTime updateTime = LocalDateTime.now();
        switch (parasvalue) {
            case "ON":
                // 处理水帘开启逻辑
                nappeMapper.ordercommand(deviceId, 1);
                nappeMapper.addNappeDataHistory(deviceId, 1, updateTime);
                break;
            case "OFF":
                // 处理水帘关闭逻辑
                nappeMapper.ordercommand(deviceId, 0);
                nappeMapper.addNappeDataHistory(deviceId, 0, updateTime);
                break;
            default:
                return Result.error("未知的水帘命令");
        }
        return Result.success("水帘操作成功");
    }
}
