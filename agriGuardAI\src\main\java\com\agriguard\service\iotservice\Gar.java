package com.agriguard.service.iotservice;

import com.agriguard.mapper.AirPressureMapper;
import com.agriguard.mapper.AmmoniaMapper;
import com.agriguard.mapper.Co2Mapper;
import com.agriguard.service.IotDeviceServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Slf4j
@RequiredArgsConstructor
@Service
public class Gar implements IotDeviceServer {
    private final AmmoniaMapper ammoniaMapper;
    private final Co2Mapper co2Mapper;
    private final AirPressureMapper airPressureMapper;

    /**
     * 查找设备类型
     * @param deviceType 设备类型
     * @return 是否存在
     */
    @Override
    public boolean findDeviceType(String deviceType) {
        String type = "gar";
        return type.equals(deviceType);
    }

    /**
     * 先查找当天该设备有没有记录数据，有的话修改，否则添加
     * @param
     */
    @Override
    public void addData(String value){
        System.out.println("gar的温度是："+value);
        //  String testvalue = "0B09179410811086";

        // 新增解析逻辑
        String data = value.replace("0B", ""); // 去除标识符
        LocalDateTime updateTime = LocalDateTime.now();
        // 解析氨气值（前2位）
        int amm = Integer.parseInt(data.substring(0, 2));
        // 解析CO2值（接下来4位）
        int co2 = Integer.parseInt(data.substring(2, 6));
        // 解析气压值（最后8位分成两组）
        String innerPressureStr = data.substring(6, 10);  // 内气压
        String outerPressureStr = data.substring(10, 14); // 外气压
        // 转换气压值（插入小数点）
        BigDecimal innerPressure = new BigDecimal(
                innerPressureStr.substring(0, 3) + "." + innerPressureStr.charAt(3));
        BigDecimal outerPressure = new BigDecimal(
                outerPressureStr.substring(0, 3) + "." + outerPressureStr.charAt(3));
        // 调用Mapper方法（需要注入对应的Mapper）
        // 氨气处理（ID=60）
        ammoniaMapper.updateAmmoniaToRealTime(60, amm, updateTime);
        ammoniaMapper.insertAmmoniaToDataCache(60, amm, updateTime);

        // CO2处理（ID=61）
        co2Mapper.updateCo2ToRealTime(61, co2, updateTime);
        co2Mapper.insertCo2ToDataCache(61, co2, updateTime);

        // 气压处理（ID=62内和63外）
        airPressureMapper.updateAirPressureToRealTime(62, innerPressure, updateTime);
        airPressureMapper.insertAirPressureToDataCache(62, innerPressure, updateTime);
        airPressureMapper.updateAirPressureToRealTime(63, outerPressure, updateTime);
        airPressureMapper.insertAirPressureToDataCache(63, outerPressure, updateTime);
    }

}

