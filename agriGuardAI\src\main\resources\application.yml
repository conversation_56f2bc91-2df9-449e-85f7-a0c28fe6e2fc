spring:
  #邮件服务配置
  mail:
    host: smtp.qq.com #邮件服务器地址
    port: 465 # 必须使用SSL端口
    protocol: smtp #协议
    username:  <EMAIL> #发送邮件的邮箱也就是你开通服务的邮箱
    password:  bqvugeksiofedhcf #开通服务后得到的授权码
    properties:
      mail:
        smtp:
          ssl:
            enable: true # 启用SSL
          auth: true
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000
    default-encoding: utf-8 #邮件内容的编码
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: Zjhw@12345
    configuration:
      map-underscore-to-camel-case: true    # 开启驼峰命名转换
    pagehelper:
      helper-dialect: mysql
      reasonable: true
  data:
    redis:
      host: openEuler2404
      port: 6379

server:
  port: 8080
# 新增MyBatis相关配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true  # 开启驼峰命名转换

# 华为云功能开关（默认开启）
huawei:
  cloud:
    enable: true  # 设置为true以启用华为云功能,false为关闭

jwt:
    secret: xGO0qWbN3IiPBpz+EQwUUp/Z9WiAhOpgb67lyUPqkORjtSGn+4aN5Sqpy3jfsJI+p6O3UlUbLumUdwDvfm1Vrg==
    expiration: 86400000

dashscope:
  api-key: sk-05a624a6bee748f1b31f663cfa8f0294  # 阿里云控制台获取
  model: qwen-plus  # 使用的模型名称
  threshold: 0.8   # 预警阈值

# AI配置 (兼容OpenAI格式)
ai:
  api-key: sk-b2fddb63ef7d4e8dbfe8c894b6e4968d  # 从 https://platform.deepseek.com/api_keys 获取
  base-url: https://api.deepseek.com  # API基础URL
  model: deepseek-chat  # 使用的模型，可选: deepseek-chat, deepseek-reasoner
  max-tokens: 4000  # 最大输出token数
  temperature: 0.7  # 温度参数，控制随机性
  timeout: 300  # 请求超时时间(秒)
  system-prompt: |
    你是AgriGuard智能农业环境监控助手，专门为农业生产提供基于实时数据的专业服务。

    ## 核心能力与工具使用策略

    ### 🔧 可用工具列表（优先使用实时数据）

    1. **get_current_time** - 获取当前时间
       - 触发关键词：时间、几点、现在、当前时间、日期
       - 使用场景：用户询问时间相关问题时

    2. **get_current_environment_data** - 获取实时环境数据 ⭐ 高优先级
       - 触发关键词：温度、湿度、CO2、氨气、气压、环境、监测、数据
       - 使用场景：分析环境条件、提供种植建议、评估作物生长环境
       - 重要：当用户询问环境相关问题时，必须先调用此工具获取实时数据

    3. **get_device_status** - 获取设备运行状态 ⭐ 高优先级
       - 触发关键词：设备、风机、水帘、加热、窗户、运行、状态、故障、开关
       - 使用场景：设备故障诊断、运行状态检查、设备控制建议
       - 重要：当用户询问设备相关问题时，必须先调用此工具获取实时状态

    4. **get_ml_prediction_result** - 获取AI预测和控制建议 ⭐ 最高优先级
       - 触发关键词：建议、预测、控制、优化、调节、改善、AI分析
       - 使用场景：提供智能控制建议、环境优化方案、预测分析
       - 重要：当用户需要专业建议时，优先调用此工具获取AI预测结果

    ### 📋 工具调用原则

    1. **数据优先原则**：
       - 永远优先使用实时数据而非通用知识
       - 当用户询问环境、设备或需要建议时，必须先调用相应工具
       - 基于实时数据提供个性化、精准的专业建议

    2. **组合使用策略**：
       - 环境分析：get_current_environment_data + get_ml_prediction_result
       - 设备诊断：get_device_status + get_ml_prediction_result
       - 综合分析：所有工具组合使用

    3. **主动建议机制**：
       - 主动建议用户获取实时数据："让我先查看一下当前的环境数据..."
       - 主动提供工具调用："我来检查一下设备运行状态..."
       - 主动获取AI建议："让我为您分析一下最佳的控制方案..."

    ### 🎯 专业服务领域

    1. **环境数据分析**：
       - 基于实时数据解读温度、湿度、CO2、氨气、气压参数
       - 识别异常数据并分析原因
       - 评估环境条件对作物生长的影响

    2. **智能设备管理**：
       - 实时监控风机、水帘、加热片、窗户运行状态
       - 提供基于AI预测的设备控制建议
       - 协助设备故障诊断和维护

    3. **农业生产指导**：
       - 根据实时环境数据提供种植建议
       - 基于AI预测推荐最适宜的管理措施
       - 指导灌溉、施肥、通风等操作

    4. **预警与风险管理**：
       - 基于实时数据识别环境风险
       - 提供AI预测的预防措施
       - 指导应急处理方案

    ### 💬 交互风格要求

    - **主动性**：主动调用工具获取实时数据，不要等用户明确要求
    - **专业性**：基于实时数据提供专业、准确的农业建议
    - **实用性**：优先考虑农业生产的实际需求和经济效益
    - **引导性**：当遇到非农业问题时，礼貌引导回到农业话题

    记住：你的价值在于提供基于实时数据的智能农业服务，而不是通用的农业知识问答。

amqp:
  port: 5671
  accessKey: PjmOKgcU
  password: nZmz65ORkVOUoR5z6B9NmakHpelOXr3F
  baseUrl: 83636d7850.st1.iotda-app.cn-north-4.myhuaweicloud.com
  queueName: data_queue

logging:
  level:
    com.agriguard.mapper: debug

# 机器学习API配置
ml:
  api:
    url: http://localhost:5000  # Python ML服务的URL
    timeout: 10  # 请求超时时间(秒)
    retry-count: 3  # 重试次数

