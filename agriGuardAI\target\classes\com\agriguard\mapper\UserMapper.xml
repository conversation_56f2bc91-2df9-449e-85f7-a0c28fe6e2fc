<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.UserMapper">
    <resultMap id="userPageMap" type="com.agriguard.dto.UserPageDto">
        <id property="userId" column="user_id"/> <!-- 关键映射 -->
        <result property="userName" column="user_name"/>
        <result property="realName" column="real_name"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <collection property="roles" ofType="string">
            <result column="role_name"/>
        </collection>
    </resultMap>
    <select id="findUserPage" resultMap="userPageMap">
        SELECT
            u.user_id,
            u.user_name,
            u.real_name,
            u.email,
            u.phone,
            r.role_name
        FROM
            user u
        LEFT JOIN
            user_role ur ON u.user_id = ur.user_id
        LEFT JOIN
            role r ON ur.role_id = r.role_id
    </select>

    <select id="existsByEmailExcludeSelf" resultType="boolean">
        SELECT COUNT(*) FROM user
        WHERE email = #{email} AND user_id != #{userId}
    </select>

    <select id="accountExistsExcludeSelf" resultType="boolean">
        SELECT COUNT(*) FROM user
        WHERE (user_name = #{account} OR email = #{account})
          AND user_id != #{userId}
    </select>

    <select id="selectByUserId" resultType="com.agriguard.entity.User">
        SELECT * FROM user
        WHERE user_id = #{userId}
    </select>

    <update id="updatebyuserid">
        UPDATE user SET
        user_name = #{userName},
        email = #{email},
        real_name = #{realName},
        phone = #{phone},
        <if test="password != null and password != ''">
            password = #{password},
        </if>
        login_date = #{loginDate}
        WHERE user_id = #{userId}
    </update>

    <delete id="deleteUserRoles">
        DELETE FROM user_role WHERE user_id = #{userId}
    </delete>

    <delete id="deleteUser">
        DELETE FROM user WHERE user_id = #{userId}
    </delete>
    <select id="findEmailsByUserNames" resultType="java.lang.String">
        SELECT email FROM user
        WHERE user_name IN
        <foreach item="name" collection="list" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>
</mapper>
