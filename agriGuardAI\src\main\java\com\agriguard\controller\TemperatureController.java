package com.agriguard.controller;


import com.agriguard.entity.device.TempHistory24;
import com.agriguard.entity.device.TempPie;
import com.agriguard.entity.device.TempRealTimeByMinute;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.pojo.TemperatureRealTime;
import com.agriguard.service.TemperatureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;


@RestController
@RequestMapping("/temp")
public class TemperatureController {
    @Autowired
    private TemperatureService temperatureService;


    // 使用Cron表达式配置每小时执行一次的定时任务
    //测试：每分钟执行一次
//    @Scheduled(cron = "0 0/1 * * * ? ")
    //使用每个小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ? ")
    @Async("asyncExecutor")
    public void scheduledTask() {
        try {
            // 调用Service中的业务方法
            temperatureService.TempDateSys();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //添加温度传感器
    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = temperatureService.addTempRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //获取温度传感器详情
    @GetMapping("/detail")
    public Result<Device> getTempDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device tempDetail = temperatureService.findTempDetailById(deviceId);
            return Result.success(tempDetail);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //分页查询温度历史状态
    @GetMapping("/history")
    public Result<PageResult<TemperatureRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData
    ) {
        try {
            PageResult<TemperatureRealTime> pageResult = temperatureService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //多条件分页查询温度传感器状态
    @GetMapping("/list")
    public Result<PageResult<TemperatureRealTime>> list(
            @RequestHeader String Authorization,
            Integer pageNum,
            Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<TemperatureRealTime> pageResult = temperatureService.findLatestStatus(pageNum, pageSize, deviceId, deviceName, place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //获取每个分钟的平均温度用于实时温度变化展示，根据位置来查询--某个位置的
    @GetMapping("/realtemp")
    public Result<List<TempRealTimeByMinute>> getAvgTemperature(@RequestHeader String Authorization, @RequestParam(required = false) String place) {
        try {
            List<TempRealTimeByMinute> avgTemperature = temperatureService.getAvgTemperature(place);
            return Result.success(avgTemperature);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史温度数据，用于折线图展示
    @GetMapping("/history24")
    public Result<List<TempHistory24>> getHistory24Hours(@RequestHeader String Authorization, @RequestParam(required = false) String place) {
        try {
            List<TempHistory24> history24Hours = temperatureService.getHistory24Hours(place);
            return Result.success(history24Hours);  // 返回成功响应
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史温度数据，用于饼图范围展示
    @GetMapping("/history24pie")
    public Result<List<TempPie>> getHistory24HoursForPie(@RequestHeader String Authorization, @RequestParam(required = false) String place) {
        try {
            List<TempPie> TempPie = temperatureService.getHistory24HoursForPie(place);
            return Result.success(TempPie);  // 返回成功响应
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

}
