package com.agriguard.mapper;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ElectricityMapper {
    //插入电流数据
    //更新电流实时状态表
    void updateElectricityToRealTime(Integer deviceId, BigDecimal ele, LocalDateTime updateTime);
    //插入电流值到温度缓存表
    void insertElectricityToDataCache(Integer deviceId, BigDecimal ele, LocalDateTime updateTime);

    //分析电流缓存数据
    List<ElectricityDataHistory> getElectricityStatsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    //添加电流历史数据
    void addElectricityDateHistory(ElectricityDataHistory item);

    //删除指定时间段内的电流缓存数据
    void deleteElectricityDateCacheByHour(LocalDateTime startTime, LocalDateTime endTime);

    //查询设备id电流传感器是否已存在
    int existsByDeviceId(Integer deviceId);

    //添加电流传感器
    void addElectricityRealTime(Integer deviceId);

    //查询电流传感器详情
    Device findElectricityDetailById(Integer deviceId);

    //分页查询历史数据
    List<ElectricityRealTime> findHistoryData(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                              @org.apache.ibatis.annotations.Param("start")LocalDateTime start,
                                              @org.apache.ibatis.annotations.Param("end")LocalDateTime end);

    //分页查询实时状态数据
    List<ElectricityRealTime> findLatestStatus(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                               @org.apache.ibatis.annotations.Param("deviceName")String deviceName,
                                               @Param("place")String place);

    //查询实时状态数据
    List<ElectricityRealTimeByMinute> getAvgElectricity(String place);

    //获取过去24小时的历史电流数据，用于折线图展示
    List<ElectricityHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史电流数据，用于饼图展示
    List<ElectricityPie> getHistory24HoursForPie(String place);
}
