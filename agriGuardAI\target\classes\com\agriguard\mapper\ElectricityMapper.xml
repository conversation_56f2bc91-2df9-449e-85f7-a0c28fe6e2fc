<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.ElectricityMapper">

    <!--    模拟华为云电流数据-->
    <!--    更新电流实时状态表-->
    <update id="updateElectricityToRealTime" parameterType="com.agriguard.pojo.ElectricityRealTime">
        UPDATE electricity_real_time
        SET
            ele = #{ele},
            update_time = #{updateTime}
            WHERE device_id = #{deviceId}
    </update>
    <!--    插入电流数据至电流缓存表-->
    <insert id="insertElectricityToDataCache" parameterType="com.agriguard.pojo.ElectricityDataCache">
        INSERT INTO electricity_data_cache (device_id, ele, update_time)
        VALUES (#{deviceId}, #{ele}, #{updateTime})
    </insert>

    <!--    分析电流缓存数据-->
    <select id="getElectricityStatsByTimeRange" resultType="com.agriguard.pojo.ElectricityDataHistory">
        SELECT
            device_id AS deviceId,
            MAX(ele) AS maxEle,
            MIN(ele) AS minEle,
            ROUND(AVG(ele), 1) AS avgEle,
            COUNT(*) AS dataCount
        FROM electricity_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY device_id
    </select>

    <!-- 插入电流历史数据 -->
    <insert id="addElectricityDateHistory" parameterType="com.agriguard.pojo.ElectricityDataHistory">
        INSERT INTO electricity_data_history (
            device_id, collect_date, collect_hour,
            max_ele, min_ele, avg_ele, data_count, update_time
        ) VALUES (
                     #{deviceId}, #{collectDate}, #{collectHour},
                     #{maxEle}, #{minEle}, #{avgEle}, #{dataCount}, #{updateTime}
                 )
    </insert>

    <!-- 清空电流缓存表 -->
    <delete id="deleteElectricityDateCacheByHour">
        DELETE FROM electricity_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
    </delete>


    <!--    查看电流设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM electricity_real_time WHERE device_id = #{deviceId}
    </select>

    <!--    增加电流传感器实时状态-->
    <insert id="addElectricityRealTime" parameterType="com.agriguard.pojo.ElectricityRealTime">
        INSERT INTO electricity_real_time
            (device_id, ele, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>

    <!--    查看电流传感器详情-->
    <select id="findElectricityDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!--    查询历史数据-->
    <select id="findHistoryData" resultType="com.agriguard.pojo.ElectricityDataHistory">
        SELECT
        device_id AS deviceId,
        collect_date AS collectDate,
        collect_hour AS collectHour,
        max_ele AS maxEle,
        min_ele AS minEle,
        avg_ele AS avgEle,
        data_count AS dataCount,
        update_time AS updateTime
        FROM electricity_data_history
        WHERE device_id = #{deviceId}
        <if test="start != null">
            AND update_time >= #{start}
        </if>
        <if test="end != null">
            AND update_time &lt; #{end}
        </if>
        ORDER BY update_time DESC
    </select>


    <!-- 查询电流传感器状态的结果映射 -->
    <resultMap id="ElectricityStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="ele" property="ele" jdbcType="DECIMAL"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询电流传感器状态 -->
    <select id="findLatestStatus" resultMap="ElectricityStatusResultMap">
        SELECT
        trt.device_id,
        d.device_name,
        d.place,
        trt.ele,
--        trt.update_time
        DATE_FORMAT(trt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        electricity_real_time trt
        JOIN
        device d ON trt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null and deviceId != ''">
            AND trt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        trt.update_time DESC
    </select>

    <!--    获取实时电流平均值-->
    <select id="getAvgElectricity" resultType="com.agriguard.entity.device.ElectricityRealTimeByMinute">
        SELECT
            CONCAT(DATE_FORMAT(tdc.update_time, '%Y-%m-%d %H:%i'), ':00') AS update_time,
            AVG(tdc.ele) AS ele
        FROM
            electricity_data_cache tdc
                JOIN
            device d ON tdc.device_id = d.device_id
        WHERE
            d.place = #{place}
          AND tdc.update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          AND tdc.update_time &lt;= NOW()
        GROUP BY
            update_time
        ORDER BY
            update_time ASC
    </select>

    <!--    获取过去24小时的电流，便于绘制折线统计图-->

    <select id="getHistory24Hours" parameterType="String" resultType="com.agriguard.entity.device.ElectricityHistory24">
        WITH hour_series AS (
        SELECT
        DATE_FORMAT(DATE_SUB(NOW(), INTERVAL n HOUR), '%Y-%m-%d') AS target_date,
        HOUR(DATE_SUB(NOW(), INTERVAL n HOUR)) AS target_hour
        FROM (
        SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION
        SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION
        SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION
        SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
        ) AS numbers
        ),
        devices_in_area AS (
        SELECT device_id
        FROM device
        <if test="place != null">
            WHERE place = #{place}
        </if>
        )
        SELECT
        hs.target_date AS collect_date,
        hs.target_hour AS collect_hour,
        COALESCE(MAX(t.max_ele), 0) AS max_ele,
        COALESCE(MIN(t.min_ele), 0) AS min_ele,
        COALESCE(ROUND(AVG(t.avg_ele), 1), 0) AS avg_ele
        FROM
        hour_series hs
        LEFT JOIN (
        SELECT
        collect_date,
        collect_hour,
        max_ele,
        min_ele,
        avg_ele
        FROM electricity_data_history
        WHERE device_id IN (SELECT device_id FROM devices_in_area)
        AND update_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ) t ON DATE_FORMAT(t.collect_date, '%Y-%m-%d') = hs.target_date
        AND t.collect_hour = hs.target_hour
        GROUP BY hs.target_date, hs.target_hour
        ORDER BY hs.target_date ASC, hs.target_hour ASC
    </select>


    <!--    饼图数据获取sql-->
    <resultMap id="ElectricityPieResultMap" type="com.agriguard.entity.device.ElectricityPie">
        <result property="fanwei" column="fanwei"/>
        <result property="EleNum" column="EleNum"/>
    </resultMap>

    <select id="getHistory24HoursForPie" resultMap="ElectricityPieResultMap">
        SELECT
            ele.fanwei,
            SUM(ele.data_count) AS EleNum
        FROM (
                 SELECT
                     tdh.data_count,
                     CASE
                         WHEN tdh.avg_ele >= 0 AND tdh.avg_ele &lt; 0.3 THEN 1
                         WHEN tdh.avg_ele >= 0.3 AND tdh.avg_ele &lt; 0.6 THEN 2
                         WHEN tdh.avg_ele >= 0.6 AND tdh.avg_ele &lt; 0.9 THEN 3
                         WHEN tdh.avg_ele >= 0.9 AND tdh.avg_ele &lt; 1.2 THEN 4
                         WHEN tdh.avg_ele >= 1.2 AND tdh.avg_ele &lt;= 1.5 THEN 5
                         ELSE 0
                         END AS fanwei
                 FROM
                     electricity_data_history tdh FORCE INDEX (idx_device_date)
            JOIN
            device d FORCE INDEX (PRIMARY) ON tdh.device_id = d.device_id
                 WHERE
                     d.place = #{place}
                   AND (
                     (tdh.collect_date = CURDATE() AND tdh.collect_hour &lt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_hour &gt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date &gt; DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_date &lt; CURDATE())
                     )
             ) AS ele
        GROUP BY ele.fanwei
        ORDER BY ele.fanwei
    </select>
</mapper>