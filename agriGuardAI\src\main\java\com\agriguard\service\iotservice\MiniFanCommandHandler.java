package com.agriguard.service.iotservice;

import com.agriguard.mapper.FanMapper;
import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class MiniFanCommandHandler implements CommandHandlerService {

    @Autowired
    private FanMapper fanMapper;

    @Override
    public boolean supports(String commandType) {
        return commandType.startsWith("Fan");
    }

    @Override
    public Result<String> handle(String commandName, String parasValue, Integer deviceId) {

        //生成一个统一的时间便于实时状态表和历史数据表统一时间
        LocalDateTime updateTime = LocalDateTime.now();
        switch (parasValue) {
            case "G1":
                fanMapper.ordercommand(deviceId, 1, 1,updateTime);
                fanMapper.addFanDataHistory(deviceId, 1, 1, updateTime);
                break;
            case "G2":
                fanMapper.ordercommand(deviceId, 1, 2,updateTime);
                fanMapper.addFanDataHistory(deviceId,  1,  2, updateTime);
                break;
            case "G3":
                fanMapper.ordercommand(deviceId,  1, 3,updateTime);
                fanMapper.addFanDataHistory(deviceId,  1,  3, updateTime);
                break;
            case "OFF":
                fanMapper.ordercommand(deviceId, 0,0,updateTime);
                fanMapper.addFanDataHistory(deviceId,  0, 0, updateTime);
                break;
            default:
                return Result.error("未知的风扇命令");
        }
        return Result.success("风扇操作成功");
    }

    private void updateFanStatus(Integer deviceId, Integer status, Integer gear) {
        // 实现同前...
    }
}
