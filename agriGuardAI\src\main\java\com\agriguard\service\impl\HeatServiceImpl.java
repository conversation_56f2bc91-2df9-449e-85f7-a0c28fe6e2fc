package com.agriguard.service.impl;

import com.agriguard.mapper.HeatMapper;
import com.agriguard.pojo.*;
import com.agriguard.service.CommandHandlerService;
import com.agriguard.service.DeviceService;
import com.agriguard.service.HeatService;
import com.agriguard.service.iotservice.HuaweiIotCommandService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class HeatServiceImpl implements HeatService {

    @Autowired
    private HeatMapper heatMapper;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private List<CommandHandlerService> commandHandlers; // Spring会自动注入所有实现

    @Autowired
    private HuaweiIotCommandService huaweiIotCommandService;

    // 检查设备ID是否存在
    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return heatMapper.existsByDeviceId(deviceId) > 0;
    }

    // 添加加热片状态的方法
    @Override
    public String addHeatRealTime(Integer deviceId) {
        // 设备存在性校验
        Device device = deviceService.findById(deviceId);
        if (device == null) return "设备不存在";

        // 设备类型校验
        if(!"加热片".equals(device.getType())) return "该设备不为加热片";

        // 重复性校验
        if(heatMapper.existsByDeviceId(deviceId) > 0) return "该加热片已存在";

        heatMapper.addHeatRealTime(deviceId);
        return "添加成功";
    }

    @Override
    public PageResult<HeatRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, Integer heatStatus, String place) {
        //创建PageResult对象
        PageResult<HeatRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<HeatRealTime> as = heatMapper.findLatestStatus(deviceId, deviceName,heatStatus, place);
        // 将查询结果转换为 Page 对象
        Page<HeatRealTime> p = (Page<HeatRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询加热片状态成功");
        return pb;
    }

    //查询加热片详情
    @Override
    public Device findHeatDetailById(Integer deviceId) {
        System.out.println("查询窗户详情成功");
        return heatMapper.findHeatDetailById(deviceId);
    }


    //修改加热片状态
    @Override
    public Result<String> updateStatus(Integer deviceId, Integer heatStatus) {
        //修改档位用华为云的命令，然后再调用通用的方法
        //对命令进行解析，换成华为云的命令
        String commandName;
        //id为11,12,73对应自己的命令,都是全部开启或者全部关闭，所以用一个命令PTC
        String parasValue;
        if(heatStatus.equals(0)){
            parasValue = "OFF";
        }else{
            parasValue = "ON";
        }
        //用通用的调用华为云方法
        try {
            // 1. 发送命令到IoT平台
            if (!huaweiIotCommandService.sendCommand("PTC", parasValue)) {
                return Result.error("命令发送失败");
            }
            System.out.println("开始配置命令并存储数据库！");

            //发送成功并返回响应后的处理
            // 2. 查找合适的处理器
            CommandHandlerService handler = commandHandlers.stream()
                    .filter(h -> h.supports("PTC"))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("不支持的命令类型"));

            // 3. 处理命令并更新数据库
            System.out.println(deviceId);
            return handler.handle("PTC", parasValue, deviceId);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("发送失败!");
        }

    }

    @Override
    public PageResult<NappeRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        PageResult<NappeRealTime> pb = new PageResult<>();
        PageHelper.startPage(pageNum, pageSize);
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        List<NappeRealTime> as = heatMapper.findHistoryData(deviceId, start, end);
        Page<NappeRealTime> p = (Page<NappeRealTime>) as;
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询加热片历史数据成功");
        return pb;
    }
}
