import axios from 'axios';
import { useAuthStore } from '@/stores/auth'

// 使用代理路径，实际请求会被代理到后端服务器
const API_BASE_URL = '/api/user/admin';

const auth = axios.create({
    baseURL: API_BASE_URL,
});


auth.interceptors.request.use(config => {
    const authStore = useAuthStore();
    const authorization = authStore.user?.token;
    if (authorization) {
        config.headers.authorization = authorization;
    }
    return config;
}, error => {
    return Promise.reject(error);
});

export default {
    /**
     * 获取用户列表（支持搜索和筛选）
     * GET /user/admin/list
     * @param {string} token - 认证令牌
     * @param {number} page - 页码（从0开始）
     * @param {number} size - 每页大小
     * @param {string} keyword - 搜索关键词（用户名或邮箱模糊匹配）
     * @param {number} roleId - 角色筛选（1-管理员，2-普通用户）
     */
    getUserList(token, page = 0, size = 10, keyword = '', roleId = null) {
        const params = { page, size }

        // 只有当关键词不为空时才添加到参数中
        if (keyword && keyword.trim()) {
            params.keyword = keyword.trim()
        }

        // 只有当角色ID有效时才添加到参数中
        if (roleId && (roleId === 1 || roleId === 2)) {
            params.roleId = roleId
        }

        return auth.get('/list', {
            params,
            headers: {
                'Authorization': token
            }
        })
    },



    /**
     * 添加用户
     * POST /user/admin/add
     */
    addUser(token, userData) {
        return auth.post('/add', userData)
    },

    /**
     * 修改用户信息
     * PUT /user/admin/update
     */
    updateUser(token, userData) {
        return auth.put('/update', userData)
    },

    /**
     * 删除用户
     * DELETE /user/admin/delete
     */
    deleteUser(token, params) {
        return auth.delete('/delete', {
            params
        })
    },

    /**
     * 修改用户角色
     * PUT /user/admin/role/update
     */
    updateUserRole(token, roleData) {
        return auth.put('/role/update', roleData)
    },

    /**
     * 检查用户是否为管理员
     * GET /user/admin/role/check/{userId}
     */
    checkUserAdmin(token, userId) {
        return auth.get(`/role/check/${userId}`)
    }
}
