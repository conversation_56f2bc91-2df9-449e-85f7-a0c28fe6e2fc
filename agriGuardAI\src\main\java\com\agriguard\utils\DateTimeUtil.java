package com.agriguard.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class DateTimeUtil {
    // 获取上一个小时的开始时间和结束时间
    public static String[] getPreviousHourRange() {
        // 获取当前时间（带时区）
        ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());

        // 计算上一个小时的开始时间（整点）
        ZonedDateTime startOfPreviousHour = now
                .minusHours(2)              // 前推1小时
                .withMinute(0)               // 分钟设为0
                .withSecond(0)               // 秒设为0
                .withNano(0);                // 纳秒设为0

        // 计算上一个小时的结束时间（整点）
        ZonedDateTime endOfPreviousHour = startOfPreviousHour.plusHours(1);

        // 格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return new String[] {
                startOfPreviousHour.format(formatter),
                endOfPreviousHour.format(formatter)
        };
    }


//    public static void main(String[] args) {
//        getPreviousHourRange();
//        String[] hourRange = getPreviousHourRange();
//        System.out.println("上一个小时范围:");
//        System.out.println("开始时间: " + hourRange[0]);
//        System.out.println("结束时间: " + hourRange[1]);
//    }
}


