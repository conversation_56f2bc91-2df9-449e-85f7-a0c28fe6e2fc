package com.agriguard.utils;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.io.Encoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.security.Key;
import java.util.Date;
import java.util.List;

@Component
public class JwtTokenUtil {
    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    // 添加密钥生成方法
    private Key getSigningKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secret);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public String generateToken(String username, List<String> roles) {
        return Jwts.builder()
                .setSubject(username)
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSigningKey(), SignatureAlgorithm.HS512) // 改用安全密钥
                .compact();
    }
    // 在类中添加密钥生成方法（仅需运行一次）
    public static void main(String[] args) {
        // 生成符合HS512要求的512位密钥
        SecretKey key = Keys.secretKeyFor(SignatureAlgorithm.HS512);
        // 转换为Base64字符串
        String base64Key = Encoders.BASE64.encode(key.getEncoded());
        System.out.println("安全密钥: " + base64Key);
    }
}