package com.agriguard.service.iotservice;

import com.agriguard.mapper.WindowMapper;
import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class WindowCommandH<PERSON>ler implements CommandHandlerService {

    @Autowired
    private WindowMapper windowMapper;
    @Override
    public boolean supports(String commandType) {
        return commandType.startsWith("Servo");
    }

    @Override
    public Result<String> handle(String commandName, String parasvalue, Integer deviceId) {
        switch (parasvalue) {
            case "ON":
                // 处理窗户开启逻辑
                windowMapper.ordercommand(deviceId, 1);
                break;
            case "OFF":
                // 处理窗户关闭逻辑
                windowMapper.ordercommand(deviceId, 0);
                break;
            default:
                return Result.error("未知的窗户命令");
        }
        return Result.success("窗户操作成功");
    }
}
