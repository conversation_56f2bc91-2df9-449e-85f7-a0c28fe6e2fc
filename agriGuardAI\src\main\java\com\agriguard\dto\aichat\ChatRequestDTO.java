package com.agriguard.dto.aichat;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * AI聊天请求DTO
 */
@Data
public class ChatRequestDTO {
    
    /**
     * 用户输入的消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 4000, message = "消息内容长度不能超过4000字符")
    private String message;
    
    /**
     * 会话ID，用于保持对话上下文
     * 可选参数，如果不提供则创建新会话
     */
    private String sessionId;
    
    /**
     * 温度参数，控制回复的随机性
     * 可选参数，如果不设置则使用配置文件中的默认值
     */
    private Double temperature;
} 