package com.agriguard.controller;
import com.agriguard.dto.*;
import com.agriguard.entity.User;
import com.agriguard.constant.RoleConstants;
import com.agriguard.exception.LoginException;
import com.agriguard.exception.RegistrationException;
import com.agriguard.mapper.UserMapper;
import com.agriguard.pojo.Result;
import com.agriguard.service.UserService;
import com.agriguard.service.code.AuthCodeService;
import com.agriguard.service.code.EmailService;
import io.micrometer.common.util.StringUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {
    private final UserService userService; // 改为接口类型
    private final AuthCodeService authCodeService; // 新增验证码服务
    private final EmailService emailService; // 新增邮件服务

    @PostMapping("/register")
    public Result<User> register(@RequestBody @Valid UserRegisterDTO dto) { // 修改返回类型为Result<User>
        try {
            if (userService.existsByEmail(dto.getEmail())) {
                throw new RegistrationException("邮箱已被注册");
            }
            if (!authCodeService.validateCode("REGISTER:" + dto.getEmail(), dto.getCode())) {
                throw new RegistrationException("验证码错误或已过期");
            }
            User registeredUser = userService.register(dto);
            return Result.success(registeredUser); // 正确返回Result包装对象
        } catch (RegistrationException e) {
            if (!authCodeService.validateCode("REGISTER:" + dto.getEmail(), dto.getCode())) {
                throw new RegistrationException("验证码错误或已过期");
            }
            if (userService.existsByEmail(dto.getEmail())) {
                throw new RegistrationException("邮箱已被注册");
            }
            if(userService.accountExists(dto.getUserName())){
                throw new RegistrationException("用户已被注册");
            }

            return Result.error(e.getMessage());  // 这里返回Result类型
        }
    }

    // 新增注册验证码发送接口
    @PostMapping("/register/send-code")
    public Result<?> sendRegisterCode(@RequestParam @Email String email) {
        try {
            if (userService.existsByEmail(email)) {
                throw new RegistrationException("邮箱已被注册");
            }

            String code = authCodeService.generateCode();
            authCodeService.saveCode("REGISTER:" + email, code); // 使用不同前缀区分
            emailService.sendAuthCode(email, "注册验证码", code);
            return Result.success("验证码已发送");
        } catch (RegistrationException e) {
            if (userService.existsByEmail(email)) {
                throw new RegistrationException("邮箱已被注册");
            }
            return Result.error(e.getMessage());
        }
    }

    // account, password
    @PostMapping("/login")
    public Result<UserInfoDTO> login(@RequestBody @Valid UserLoginDTO dto) {
        try {
            // 执行登录（会自动转换account为邮箱）
            UserInfoDTO user = userService.login(dto);
            return Result.success(user);
        } catch (LoginException e) {
            // 统一参数校验
            if (StringUtils.isBlank(dto.getAccount())) {
                throw new LoginException("账号不能为空");
            }
            if (StringUtils.isBlank(dto.getPassword())) {
                throw new LoginException("密码不能为空");
            }
            if (!userService.accountExists(dto.getAccount())) {
                throw new LoginException("账号错误");
            }
            if (!userService.verifyPassword(dto.getAccount(), dto.getPassword())) {
                throw new LoginException("密码错误");
            }
            // 其他登录错误
            return Result.error(e.getMessage());


        }
    }

    //发送邮箱验证码
    @PostMapping("/send-code")
    public Result<?> sendAuthCode(@RequestParam @Email String email) {
        try {
            userService.validateEmailRegistered(email);
            String code = authCodeService.generateCode();
            authCodeService.saveCode(email, code);
            emailService.sendAuthCode(email, "登录验证码", code);
            return Result.success("验证码已发送");
        } catch (LoginException e) {
            if (!userService.existsByEmail(email)) {
                throw new LoginException("邮箱错误");
            }return Result.error(e.getMessage());
        }
    }

    //邮箱验证码登录
    @PostMapping("/login-with-code")
    public Result<UserInfoDTO> loginWithCode(@RequestBody @Valid EmailLoginDTO dto) {
        try {
            UserInfoDTO user = userService.loginWithCode(dto.getEmail(), dto.getCode());
            return Result.success(user);
        } catch (LoginException e) {

            if (!userService.existsByEmail(dto.getEmail())) {
                throw new LoginException("邮箱未注册");
            }
            if (!authCodeService.validateCode(dto.getEmail(), dto.getCode())) {
                throw new LoginException("验证码错误或已过期");
            }
            return Result.error(e.getMessage());
        }
    }

    //重置密码发送验证码
    @PostMapping("/forget-pwd/send-code")
    public Result<?> sendForgetPwdCode(@RequestParam @Email String email) {
        try {
            userService.validateEmailRegistered(email); // 新增邮箱校验
            String code = authCodeService.generateCode();
            authCodeService.saveCode("FORGET_PWD:" + email, code); // 使用不同redis key
            emailService.sendAuthCode(email, "密码重置验证码", code);
            return Result.success("验证码已发送");
        }catch (LoginException e) {
            if (!userService.existsByEmail(email)) {
                return Result.error("邮箱错误");
            }
            return Result.error(e.getMessage());
        }

    }

    //重置密码
    @PostMapping("/forget-pwd/reset")
    public Result<?> resetPassword( @RequestBody @Valid ForgetPasswordDTO dto) {
        try {

            userService.resetPasswordByCode(dto);
            return Result.success("密码重置成功");
        } catch (LoginException e) {
            if (!userService.existsByEmail(dto.getEmail())) {
                throw new LoginException("邮箱未注册");
            }
            if (!authCodeService.validateCode("FORGET_PWD:" + dto.getEmail(), dto.getCode())) {
                throw new LoginException("验证码错误或已过期");
            }


            return Result.error(e.getMessage());

        }
    }

    @GetMapping("/is_expire")
    public Boolean is_expire(@RequestHeader String Authorization) {
        return userService.is_expire(Authorization);
    }
    // 管理员获取所有用户列表（支持搜索和筛选）
    @PreAuthorize("hasAuthority('ADMIN')")
    @GetMapping("/admin/list")
    public Result<Page<UserPageDto>> getAllUsers(
            @RequestParam int page,// 页码，从0开始
            @RequestParam int size,
            @RequestParam(required = false) String keyword, // 搜索关键词（支持用户名和邮箱模糊匹配）
            @RequestParam(required = false) Integer roleId) { // 角色筛选（1-管理员，2-普通用户）
        Pageable pageable = PageRequest.of(page, size);
        
        // 如果没有搜索条件，使用原方法
        if ((keyword == null || keyword.trim().isEmpty()) && roleId == null) {
            return Result.success(userService.findAllUsersWithRoles(pageable));
        }
        
        // 有搜索条件时使用新方法
        return Result.success(userService.findAllUsersWithRoles(pageable, keyword, roleId));
    }
    // 管理员添加用户
    @PreAuthorize("hasAuthority('ADMIN')")
    @PostMapping("/admin/add")
    public Result<User> addUserByAdmin(@Valid @RequestBody UserAddByAdminDTO dto) {
        try {
            if (userService.existsByEmail(dto.getEmail())) {
                throw new RegistrationException("邮箱已被注册");
            }
            if (userService.accountExists(dto.getUserName())) {
                throw new RegistrationException("用户名已存在");
            }

            User addedUser = userService.addUserByAdmin(dto);
            return Result.success(addedUser);
        } catch (RegistrationException e) {
            return Result.error(e.getMessage());
        }
    }
    // 管理员修改用户信息
    @PreAuthorize("hasAuthority('ADMIN')")
    @PutMapping("/admin/update")
    public Result<User> updateUserByAdmin(@Valid @RequestBody UserUpdateByAdminDTO dto) {
        try {
            // 增加用户ID存在性校验
            if (!userService.existsByUserId(dto.getUserId())) {
                throw new LoginException("用户不存在");
            }

            // 邮箱校验（排除自己）
            if (StringUtils.isNotBlank(dto.getEmail()) &&
                    userService.existsByEmailExcludeSelf(dto.getEmail(), dto.getUserId())) {
                throw new RegistrationException("邮箱已被其他用户使用");
            }

            // 用户名校验（排除自己）
            if (StringUtils.isNotBlank(dto.getUserName()) &&
                    userService.accountExistsExcludeSelf(dto.getUserName(), dto.getUserId())) {
                throw new RegistrationException("用户名已被其他用户使用");
            }

            User updatedUser = userService.updateUserByAdmin(dto);
            return Result.success(updatedUser);
        } catch (RegistrationException | LoginException e) {
            return Result.error(e.getMessage());
        }
    }

    // 管理员删除用户
    @PreAuthorize("hasAuthority('ADMIN')")
    @DeleteMapping("/admin/delete")
    public Result<?> deleteUserByAdmin(
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String email) {
        try {
            if (userId == null && StringUtils.isBlank(userName) && StringUtils.isBlank(email)) {
                throw new LoginException("至少需要提供一种用户标识");
            }

            userService.deleteUserByAdmin(userId, userName, email);
            return Result.success("用户删除成功");
        } catch (LoginException | RegistrationException e) {
            return Result.error(e.getMessage());
        }
    }

    // ========== 角色管理功能 ==========
    
    // 管理员修改用户角色（统一接口）
    @PreAuthorize("hasAuthority('ADMIN')")
    @PutMapping("/admin/role/update")
    public Result<?> updateUserRole(@Valid @RequestBody UserRoleUpdateDTO dto) {
        try {
            // 检查当前角色状态，避免重复操作
            boolean currentlyAdmin = userService.isAdmin(dto.getUserId());
            boolean targetIsAdmin = dto.getRoleId().equals(RoleConstants.ADMIN_ROLE_ID);
            
            if (currentlyAdmin && targetIsAdmin) {
                return Result.error("该用户已经是管理员");
            }
            if (!currentlyAdmin && !targetIsAdmin) {
                return Result.error("该用户已经是普通用户");
            }
            
            userService.updateUserRole(dto.getUserId(), dto.getRoleId());
            String roleName = dto.getRoleId().equals(RoleConstants.ADMIN_ROLE_ID) ? 
                RoleConstants.ADMIN_DESC : RoleConstants.USER_DESC;
            return Result.success("用户角色已更新为: " + roleName);
        } catch (LoginException e) {
            return Result.error(e.getMessage());
        }
    }
    
    // 检查用户是否为管理员
    @PreAuthorize("hasAuthority('ADMIN')")
    @GetMapping("/admin/role/check/{userId}")
    public Result<Boolean> checkIsAdmin(@PathVariable Integer userId) {
        try {
            boolean isAdmin = userService.isAdmin(userId);
            return Result.success(isAdmin);
        } catch (Exception e) {
            return Result.error("检查用户角色失败: " + e.getMessage());
        }
    }

}

