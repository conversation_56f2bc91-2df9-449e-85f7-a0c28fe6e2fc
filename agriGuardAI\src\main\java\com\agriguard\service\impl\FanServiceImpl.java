package com.agriguard.service.impl;

import com.agriguard.mapper.FanMapper;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.FanRealTime;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import com.agriguard.service.DeviceService;
import com.agriguard.service.FanService;
import com.agriguard.service.iotservice.HuaweiIotCommandService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class FanServiceImpl implements FanService {
    @Autowired
    private FanMapper fanMapper;
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private List<CommandHandlerService> commandHandlers; // Spring会自动注入所有实现

    @Autowired
    private HuaweiIotCommandService huaweiIotCommandService;

    //分页模糊查询风机状态
    @Override
    public PageResult<FanRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName,
                                                    Integer fanStatus,String place, Integer gear) {
        //创建PageResult对象
        PageResult<FanRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<FanRealTime> as = fanMapper.findLatestStatus(deviceId, deviceName,fanStatus, place,gear);
        System.out.println(as);
        // 将查询结果转换为 Page 对象
        Page<FanRealTime> p = (Page<FanRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());


        System.out.println("查询风机状态成功");
        log.info(pb.toString());
        return pb;
    }

    //添加风机状态
    @Override
    public String addFanRealTime(Integer deviceId) {
        // 校验设备类型
        Device device = deviceService.findById(deviceId);
        if (device == null) {
            return "设备不存在";
        }
        if(!"风机".equals(device.getType())) {
            return "该设备不为风机";
        }
        //判断风机实时状态表中是否已存在该风机的状态
        FanRealTime fanRealTime = fanMapper.findLatestStatusByDeviceId(deviceId);
        if (fanRealTime != null) {
            System.out.println("该风机的状态已存在");
            return  "该风机的状态已存在";
        }
        fanMapper.addFanRealTime(deviceId);
        return "添加成功";
    }

    //修改风机状态
    @Override
    public void updateStatus(Integer deviceId, Integer fanStatus) {
        //现在生成时间，方便实时状态和历史数据时间统一
        LocalDateTime updateTime = LocalDateTime.now();
        Integer gear;
        if (fanStatus == 1) {  // 默认挡位为1
            gear = 1;
        } else {
            gear = 0;  // 状态为0表示关闭
        }
        //修改风机实时状态表
        fanMapper.updateStatus(deviceId, fanStatus, gear,updateTime);
        //增加风机历史数据
        fanMapper.addFanDataHistory(deviceId, fanStatus, gear,updateTime);
    }

    //修改挡位
    @Override
    public Result<String> updateGear(Integer deviceId, Integer gear) {

        //修改档位用华为云的命令，然后再调用通用的方法
        //对命令进行解析，换成华为云的命令
        String commandName;
        //id为4,5,6,7对应自己的命令
        switch (deviceId) {
            case 4 -> {commandName = "Motor1";}
            case 5 -> {commandName = "Motor2";}
            case 6 -> {commandName = "Motor3";}
            case 7 -> {commandName = "Motor4";}
            default -> {commandName = "Fan";}
        }
        String parasValue;
        switch (gear) {
            case 1 -> {parasValue = "G1";}
            case 2 -> {parasValue = "G2";}
            case 3 -> {parasValue = "G3";}
            default -> {parasValue = "OFF";}
        }

        //用通用的调用华为云方法
        try {
//            //新增逻辑判断
//            //如果是小风机命令则先关闭大风机
//            //同时修改数据库以同步
//            LocalDateTime updateTime = LocalDateTime.now();
//            if(commandName.equals("Fan")){
//                //风机1
//                huaweiIotCommandService.sendCommand("Motor1","OFF");
//                fanMapper.ordercommand(4, 0, 0,updateTime);
//                fanMapper.addFanDataHistory(4, 0, 0, updateTime);
//                //风机2
//                huaweiIotCommandService.sendCommand("Motor2","OFF");
//                fanMapper.ordercommand(5, 0, 0,updateTime);
//                fanMapper.addFanDataHistory(5, 0, 0, updateTime);
//                //风机3
//                huaweiIotCommandService.sendCommand("Motor3","OFF");
//                fanMapper.ordercommand(6, 0, 0,updateTime);
//                fanMapper.addFanDataHistory(6, 0, 0, updateTime);
//                //风机4
//                huaweiIotCommandService.sendCommand("Motor4","OFF");
//                fanMapper.ordercommand(7, 0, 0,updateTime);
//                fanMapper.addFanDataHistory(7, 0, 0, updateTime);
//            }else {
//                //如果是大风机命令则先关闭小风机74
//                huaweiIotCommandService.sendCommand("Fan","OFF");
//                fanMapper.ordercommand(74, 0, 0,updateTime);
//                fanMapper.addFanDataHistory(74, 0, 0, updateTime);
//            }

            // 1. 发送命令到IoT平台
            if (!huaweiIotCommandService.sendCommand(commandName, parasValue)) {
                return Result.error("命令发送失败");
            }
            System.out.println("开始配置命令并存储数据库！");

            //发送成功并返回响应后的处理
            // 2. 查找合适的处理器
            CommandHandlerService handler = commandHandlers.stream()
                    .filter(h -> h.supports(commandName))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("不支持的命令类型"));

            // 3. 处理命令并更新数据库
            System.out.println(deviceId);
            return handler.handle(commandName, parasValue, deviceId);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("发送失败!");
        }
    }

    //根据风机id查询风机详细信息
    @Override
    public Device findFanDetailById(Integer deviceId) {
        return fanMapper.findFanDetailById(deviceId);
    }

    //查询历史数据
    @Override
    public PageResult<FanRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        //创建PageResult对象
        PageResult<FanRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 转换日期范围
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        // 调用 mapper 进行查询
        List<FanRealTime> as = fanMapper.findHistoryData(deviceId, start, end);
        // 将查询结果转换为 Page 对象
        Page<FanRealTime> p = (Page<FanRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询风机历史数据成功");
        return pb;
    }
}
