package com.agriguard.controller;

import com.agriguard.dto.AbnormalDevice;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/device")
public class DeviceController {
    @Autowired
    private DeviceService deviceService;

    // 修改为多条件模糊查询和分页功能
    @GetMapping("/get_all_location_of_device")
    public List<String> get_all_location_of_device(
            @RequestHeader String Authorization,
            @RequestParam String DeviceType
    ){
        try {
            return deviceService.get_all_location_of_device(DeviceType);
        } catch (Exception e) {
            return List.of();
        }
    }

    // 修改为多条件模糊查询和分页功能
    @GetMapping("/find")
    public Result<PageResult<Device>> find(
            @RequestHeader String Authorization,
            Integer pageNum,
            Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String place,
            @RequestParam(required = false) String model,
            @RequestParam(required = false) String factory,
            @RequestParam(required = false) LocalDate installTime
    ){
        try {
            PageResult<Device> pageResult = deviceService.find(pageNum, pageSize,deviceId, deviceName, type, place, model, factory, installTime);
            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 修改findall方法的返回类型
    @GetMapping("findall")
    public Result<List<Device>> findAll(@RequestHeader String Authorization) {
        try {
            List<Device> devices = deviceService.findAll();
            return devices.isEmpty() ?
                    Result.error("未找到设备信息") :
                    Result.success(devices);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/add")
    public Result<String> addDevice(@RequestHeader String Authorization, @RequestBody Device device) {
        try {
            deviceService.addDevice(device);
            return Result.success("设备添加成功");
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    @PostMapping("/update")
    public Result<String> updateDevice(@RequestHeader String Authorization, @RequestBody Device device) {
        try {
            deviceService.updateDevice(device);
            return Result.success("设备更新成功");
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete")
    public Result<String> deleteDevice(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            deviceService.deleteDevice(deviceId);
            return Result.success("设备删除成功");
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    //返货异常温度传感器数量
    @GetMapping("/abnormal")
    public Result<AbnormalDevice> getAbnormalCount(@RequestHeader String Authorization,
                                                   @RequestParam(required = false) String name,
                                                   @RequestParam(required = false) String place) {
        try {
            if (place == null) {
                return Result.error("位置不能为空");
            }
            AbnormalDevice abnormalDevice = new AbnormalDevice();
            abnormalDevice.setTotalCount(deviceService.getTotalCount(name,place));
            abnormalDevice.setAbnormalCount(deviceService.getAbnormalCount(name,place));
            abnormalDevice.setNormalCount(abnormalDevice.getTotalCount() - abnormalDevice.getAbnormalCount());
            System.out.println("查询到的异常温度传感器数量: " + abnormalDevice);
            return Result.success(abnormalDevice);  // 返回成功响应
        }
        catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
    //查询异常设备id
    @GetMapping("/abnormal/ids")
    public Result<List<Integer>> getAbnormalDeviceIds(
            @RequestHeader String Authorization,
            @RequestParam(required = false) String name,
            @RequestParam String place) {
        try {
            if (place == null) {
                return Result.error("位置不能为空");
            }
            List<Integer> ids = deviceService.getAbnormalDeviceIds(name, place);
            return Result.success(ids);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
}

