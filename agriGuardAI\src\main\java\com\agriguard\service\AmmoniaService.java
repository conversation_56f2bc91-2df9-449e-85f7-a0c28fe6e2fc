package com.agriguard.service;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.AmmoniaRealTime;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;

import java.time.LocalDate;
import java.util.List;

public interface AmmoniaService {
    //定时对氨气数据进行分析，存储历史数据，并进行数据删除
    void AmmoniaDateSys();

    // 检查设备是否已存在氨气传感器
    boolean existsByDeviceId(Integer deviceId);

    // 添加氨气传感器
    String addAmmoniaRealTime(Integer deviceId);

    // 查询氨气传感器详情
    Device findAmmoniaDetailById(Integer deviceId);

    // 分页查询历史数据
    PageResult<AmmoniaRealTime> findHistoryData(Integer pageNum, Integer pageSize,
                                                Integer deviceId, LocalDate beginData, LocalDate endData);

    // 多条件分页查询实时状态
    PageResult<AmmoniaRealTime> findLatestStatus(Integer pageNum, Integer pageSize,
                                             Integer deviceId, String deviceName, String place);

    // 获取指定位置的分钟级平均氨气
    List<AmmoniaRealTimeByMinute> getAvgAmmonia(String place);

    //插入氨气数据
    void insertTemperature();
    //获取过去24小时的历史氨气数据，用于折线图展示
    List<AmmoniaHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史氨气数据，用于饼图展示
    List<AmmoniaPie> getHistory24HoursForPie(String place);
}
