package com.agriguard.controller;

import com.agriguard.dto.SelectDevice;
import com.agriguard.pojo.Result;
import com.agriguard.service.iotservice.ThresholdAlertService;
import com.agriguard.service.iotservice.Threshold;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/threshold")
public class ThresholdController {

    @Autowired
    private Threshold threshold;
    @Autowired
    private ThresholdAlertService thresholdAlertService;

    // 更新温度阈值(小数)
    @PostMapping("/update-device")
    public Result<?> updateThresholdTypeThreshold(
            @RequestParam String thresholdType,
            @RequestParam String place,
            @RequestParam(required = false) String upValue,
            @RequestParam(required = false) String downValue,
            @RequestHeader String Authorization) {
        return threshold.updateThreshold(thresholdType, place, upValue, downValue);
    }

    @GetMapping("/select-device")
    public List<SelectDevice> selectThresholdTypeThreshold(
            @RequestParam(required = false) String place,
            @RequestHeader String Authorization) {
        return threshold.selectThresholdTypeThreshold(place);
    }

    //定时任务，每5分钟检查一次阈值
   @Scheduled(cron = "0 0/1 * * * ?")
   public void scheduledThresholdCheck() {
       thresholdAlertService.checkAllThresholds();
   }

    // 手动触发检查所有阈值
    @GetMapping("/checkThresholds")
    public String checkThresholds() {
        thresholdAlertService.checkAllThresholds();
        return "阈值检查完成";
    }



}
