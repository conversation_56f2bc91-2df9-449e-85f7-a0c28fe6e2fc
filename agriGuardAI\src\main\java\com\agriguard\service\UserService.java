package com.agriguard.service;

import com.agriguard.dto.*;
import com.agriguard.entity.User;
import com.agriguard.exception.LoginException;
import com.agriguard.exception.RegistrationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


public interface UserService {
    User register(UserRegisterDTO dto);
    UserInfoDTO login(UserLoginDTO loginDTO);
    UserInfoDTO loginWithCode(String email, String code) throws LoginException;
    void resetPasswordByCode(ForgetPasswordDTO dto) throws LoginException;
    boolean existsByEmail(String email);
    void validateEmailRegistered(String email);
    boolean accountExists(String account);
    boolean verifyPassword(String account, String password);
    Boolean is_expire(String authorization);
    // 新增：获取用户角色方法
    List<String> findRolesByUserId(Integer userId);
    //管理员功能
    Page<UserPageDto> findAllUsersWithRoles(Pageable pageable);
    Page<UserPageDto> findAllUsersWithRoles(Pageable pageable, String keyword, Integer roleId);
    User addUserByAdmin(UserAddByAdminDTO dto);
    User updateUserByAdmin(UserUpdateByAdminDTO dto) throws RegistrationException, LoginException;

    boolean existsByEmailExcludeSelf(String email, Integer userId);
    boolean accountExistsExcludeSelf(String account, Integer userId);

    boolean existsByUserId(Integer userId);

    void deleteUserByAdmin(Integer userId, String userName, String email);
    
    // 角色管理功能
    void updateUserRole(Integer userId, Integer roleId);
    boolean isAdmin(Integer userId);
}
