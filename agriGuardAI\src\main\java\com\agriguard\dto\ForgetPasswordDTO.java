package com.agriguard.dto;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ForgetPasswordDTO {
    @NotBlank
    @Email
    private String email;

    @NotBlank @Size(min = 6, max = 6)
    private String code;

    @NotBlank @Size(min = 3, max = 10)
    private String newPassword;



}
