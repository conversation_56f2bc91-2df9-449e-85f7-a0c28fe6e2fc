package com.agriguard.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class Alarm {
    private Integer alertId;  // 修改字段名与表结构一致
    private String deviceName;
    private String place;
    private String alarmTypes; // 复数形式与表alarmTypes字段对应
    private String currentValue;
    private String threshold;  // 与表threshold字段对应
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updateTime; // 修改字段名与表update_time对应
    private String processingStatus;
    private String personName; // 新增字段对应表person_name
}
