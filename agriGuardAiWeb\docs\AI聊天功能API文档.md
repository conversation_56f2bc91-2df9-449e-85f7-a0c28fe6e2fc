# AI聊天功能API文档

## 概述

本文档描述了AgriGuard AI聊天功能所需的所有API接口，包括已实现的和需要实现的接口。

## 基础信息

- **Base URL**: `/api`
- **认证方式**: Bear<PERSON>（通过Authorization头传递）
- **数据格式**: JSON
- **响应格式**: 统一JSON格式，支持 `code: 0` 或 `code: 1` 表示成功

## 通用响应格式

### 成功响应
```json
{
  "code": 0,  // 或者 1，都表示成功
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": -1,  // 非0和1的值表示错误
  "message": "错误描述",
  "data": null
}
```

## API接口列表

### 1. 发送聊天消息（普通模式）✅

**接口地址**: `POST /api/ai/chat`

**功能描述**: 发送用户消息给AI，返回完整响应

**请求参数**:
```json
{
  "message": "温室里的温度是25度，湿度是80%，这样的环境适合种植什么作物？",
  "sessionId": "可选-会话ID，用于保持对话上下文",
  "temperature": 0.7  // AI温度参数，范围0-1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "根据您提供的环境条件...",
    "sessionId": "session_123456",
    "timestamp": **********789,
    "tokenUsed": 150
  }
}
```

**状态**: ✅ 已实现

---



### 2. 获取会话列表 ✅

**接口地址**: `GET /api/ai/sessions`

**功能描述**: 获取当前用户的所有聊天会话列表

**请求参数**: 无（通过Authorization头获取用户信息）

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": "session_123456",
      "title": "温室种植咨询",
      "updateTime": **********789,
      "messageCount": 8
    },
    {
      "id": "session_789012",
      "title": "设备故障诊断",
      "updateTime": 1703120000000,
      "messageCount": 12
    }
  ]
}
```

**状态**: ✅ 已实现

---

### 3. 获取会话历史记录 ✅

**接口地址**: `GET /api/ai/session/{sessionId}`

**功能描述**: 获取指定会话的历史消息记录

**请求参数**: 
- `sessionId` (路径参数): 会话ID

**响应示例**:

**字符串格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": "用户: 温室里的温度是25度，湿度是80%\n\nAI助手: 根据您提供的环境条件..."
}
```

**状态**: ✅ 已实现

---

### 4. 删除会话 ✅

**接口地址**: `DELETE /api/ai/session/{sessionId}`

**功能描述**: 删除指定的聊天会话及其所有消息

**请求参数**: 
- `sessionId` (路径参数): 会话ID

**响应示例**:
```json
{
  "code": 0,
  "message": "会话已清空",
  "data": "会话已清空"
}
```

**状态**: ✅ 已实现

---

### 5. AI服务健康检查 ✅

**接口地址**: `GET /api/ai/health`

**功能描述**: 检查AI服务的健康状态

**请求参数**: 无

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": **********,
    "lastCheck": **********789,
    "message": "AI服务运行正常"
  }
}
```

**状态**: ✅ 已实现

---

## 可选扩展接口



### 6. 导出会话 📋

**接口地址**: `GET /api/ai/session/{sessionId}/export`

**功能描述**: 导出会话为文本或Markdown格式

**请求参数**:
- `format` (查询参数): 导出格式 (txt/markdown)

**响应**: 直接返回文件内容

**状态**: 📋 计划中

---

## 前端实现状态

### ✅ 前端已完成
- AI消息发送功能
- 会话列表显示和切换
- 会话历史加载和显示
- 会话删除功能
- 健康检查功能
- Markdown渲染和代码高亮
- 用户界面和交互逻辑

### ⚠️ 需要后端实现的API
- `POST /api/ai/chat` - 发送聊天消息
- `GET /api/ai/sessions` - 获取会话列表
- `GET /api/ai/session/{sessionId}` - 获取会话历史
- `DELETE /api/ai/session/{sessionId}` - 删除会话
- `GET /api/ai/health` - 健康检查

### 📋 未来扩展
- 会话导出
- 搜索功能
- 消息收藏

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 或 1 | 成功 |
| -1 | 一般错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |

## 缓存策略

- **Redis缓存**: 会话数据缓存30分钟
- **本地缓存**: 当前会话消息本地存储
- **列表缓存**: 会话列表缓存5分钟

## 注意事项

1. 所有接口都需要用户认证
2. 流式传输需要特殊处理SSE格式
3. 长消息可能需要分片处理
4. 考虑Token使用量统计和限制
5. 敏感信息过滤和安全检查 