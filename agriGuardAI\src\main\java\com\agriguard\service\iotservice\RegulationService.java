package com.agriguard.service.iotservice;

import com.agriguard.entity.Regulation;
import com.agriguard.mapper.AlarmMapper;
import com.agriguard.mapper.RegulationMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RegulationService {
    private final RegulationMapper regulationMapper;
    private final AlarmMapper alarmMapper;

    public void createRegulation(Regulation regulation) {
        if (regulationMapper.insert(regulation) <= 0) {
            throw new RuntimeException("创建报警规则失败");
        }
    }

    public void updateRegulation(Regulation regulation) {
        if (regulationMapper.update(regulation) <= 0) {
            throw new RuntimeException("更新报警规则失败");
        }
    }

    public void deleteRegulation(Integer regulationId) {
        if (regulationMapper.delete(regulationId) <= 0) {
            throw new RuntimeException("删除报警规则失败");
        }
    }

    public Regulation getById(Integer regulationId) {
        return regulationMapper.selectById(regulationId);
    }

    public List<Regulation> searchRegulations(Regulation condition) {
        return regulationMapper.selectByCondition(condition);
    }


    public List<String> all_admin_name() {
        return regulationMapper.all_admin_name();
    }
}
