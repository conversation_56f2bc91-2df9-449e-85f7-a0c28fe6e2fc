package com.agriguard.controller;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import com.agriguard.service.Co2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/co2")
public class Co2Controller {
    @Autowired
    private Co2Service co2Service;

    // 使用Cron表达式配置每小时执行一次的定时任务
    //每个小时执行一次
//    @Scheduled(cron = "0 0 * * * ?")
    //使用每两个小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ?  ")
    public void scheduledTask() {
        try {
            // 调用Service中的业务方法
            co2Service.Co2DateSys();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = co2Service.addCo2RealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //获取Co2传感器详情
    @GetMapping("/detail")
    public Result<Device> getHumidityDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device co2Detail = co2Service.findCo2DetailById(deviceId);
            return Result.success(co2Detail);
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //分页查询Co2历史状态
    @GetMapping("/history")
    public Result<PageResult<Co2RealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData
    ) {
        try {
            PageResult<Co2RealTime> pageResult = co2Service.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //多条件分页查询Co2传感器状态
    @GetMapping("/list")
    public Result<PageResult<Co2RealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<Co2RealTime> pageResult = co2Service.findLatestStatus(pageNum, pageSize, deviceId, deviceName, place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //获取每个分钟的平均co2用于实时湿度变化展示，根据位置来查询--某个位置的
    @GetMapping("/avg")
    public Result<List<Co2RealTimeByMinute>> getAvgHumidity(@RequestHeader String Authorization, @RequestParam String place){
        try {
            List<Co2RealTimeByMinute> avgCo2 = co2Service.getAvgCo2(place);
            return Result.success(avgCo2);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
    //获取过去24小时的历史Co2数据，用于折线图展示
    @GetMapping("/history24")
    public Result<List<Co2History24>> getHistory24Hours(@RequestHeader String Authorization, @RequestParam String place){
        try {
            List<Co2History24> history24Hours = co2Service.getHistory24Hours(place);
            return Result.success(history24Hours);  // 返回成功响应
        }
        catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史Co2数据，用于饼图范围展示
    @GetMapping("/history24pie")
    public Result<List<Co2Pie>> getHistory24HoursForPie(@RequestHeader String Authorization, @RequestParam String place){
        try {
            List<Co2Pie> Co2Pie = co2Service.getHistory24HoursForPie(place);
            return Result.success(Co2Pie);  // 返回成功响应

        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
}

