package com.agriguard.controller;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import com.agriguard.service.AmmoniaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/ammonia")
public class AmmoniaController {
    @Autowired
    private AmmoniaService ammoniaService;

    // 使用Cron表达式配置每小时执行一次的定时任务
    //每个小时执行一次
//    @Scheduled(cron = "0 0 * * * ?")
    //使用每个小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ?  ")
    public void scheduledTask() {
        try {
            // 调用Service中的业务方法
            ammoniaService.AmmoniaDateSys();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = ammoniaService.addAmmoniaRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //获取氨气传感器详情
    @GetMapping("/detail")
    public Result<Device> getAmmoniaDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device ammDetail = ammoniaService.findAmmoniaDetailById(deviceId);
            return Result.success(ammDetail);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //分页查询氨气历史状态
    @GetMapping("/history")
    public Result<PageResult<AmmoniaRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData
    ) {
        try {
            PageResult<AmmoniaRealTime> pageResult = ammoniaService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //多条件分页查询氨气传感器状态
    @GetMapping("/list")
    public Result<PageResult<AmmoniaRealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<AmmoniaRealTime> pageResult = ammoniaService.findLatestStatus(pageNum, pageSize, deviceId, deviceName, place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //获取每个分钟的平均氨气用于实时湿度变化展示，根据位置来查询--某个位置的
    @GetMapping("/avg")
    public Result<List<AmmoniaRealTimeByMinute>> getAvgAmmonia(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<AmmoniaRealTimeByMinute> avgAmm = ammoniaService.getAvgAmmonia(place);
            return Result.success(avgAmm);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史氨气数据，用于折线图展示
    @GetMapping("/history24")
    public Result<List<AmmoniaHistory24>> getHistory24Hours(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<AmmoniaHistory24> history24Hours = ammoniaService.getHistory24Hours(place);
            return Result.success(history24Hours);  // 返回成功响应
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史氨气数据，用于饼图范围展示
    @GetMapping("/history24pie")
    public Result<List<AmmoniaPie>> getHistory24HoursForPie(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<AmmoniaPie> AmmoniaPie = ammoniaService.getHistory24HoursForPie(place);
            return Result.success(AmmoniaPie);  // 返回成功响应

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
}
