package com.agriguard.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.security.core.Transient;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class User {
    private Integer userId;
    private String userName;
    private String password;
    private String realName;
    private String email;
    private String phone;
    private LocalDateTime createDate;
    private LocalDateTime updateDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime loginDate;
    private String token;
    private List<String> roles;


}
