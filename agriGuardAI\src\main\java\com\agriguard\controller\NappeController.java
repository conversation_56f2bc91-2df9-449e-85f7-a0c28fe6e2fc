package com.agriguard.controller;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.NappeRealTime;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.service.NappeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/nappe")
public class NappeController {
    @Autowired
    private NappeService nappeService;

    // 分页查询水帘状态（默认工作状态为1）
    @GetMapping("/list")
    public Result<PageResult<NappeRealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) Integer nappeStatus,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<NappeRealTime> pageResult = nappeService.findLatestStatus(pageNum, pageSize, deviceId,deviceName, nappeStatus,place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 添加水帘状态，添加水帘设备id就可以
    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = nappeService.addNappeRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    // 修改水帘状态
    @PutMapping("/updatestatus")
    public Result<String> updateStatus(@RequestHeader String Authorization, @RequestBody NappeRealTime nappeRealTime) {
        try {
            nappeService.updateStatus(nappeRealTime);
            return Result.success("修改成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("修改失败: " + e.getMessage());
        }
    }

    // 获取单台水帘详情
    @GetMapping("/detail")
    public Result<Device> getNappeDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device nappeDetail = nappeService.findNappeDetailById(deviceId);
            return Result.success(nappeDetail);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 获取水帘历史数据
    @GetMapping("/history")
    public Result<PageResult<NappeRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData) {
        try {
            PageResult<NappeRealTime> pageResult = nappeService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
