# AgriGuardAI 用户认证模块 API 文档

## 概述

AgriGuardAI 用户认证模块提供完整的用户管理和身份认证功能，包括用户注册、登录、密码重置、权限管理等核心功能。系统采用 JWT（JSON Web Token）进行身份认证，支持基于角色的访问控制（RBAC）。

## 认证机制

### JWT Token 认证
- **算法**: HS512
- **过期时间**: 24小时（86400000ms）
- **存储方式**: Redis缓存，支持token自动续期和单点登录控制
- **传输方式**: HTTP Header `Authorization: Bearer {token}`

### 验证码机制
- **类型**: 6位数字验证码
- **有效期**: 5分钟
- **存储**: Redis缓存
- **用途**: 注册、登录、密码重置

## 权限模型

### 角色系统
- **ADMIN**: 管理员角色，拥有所有权限
- **ROLE_USER**: 普通用户角色，基础访问权限

### 权限控制
- 方法级权限控制：`@PreAuthorize("hasAuthority('ADMIN')")`
- URL级权限控制：Spring Security配置
- 双重校验机制：确保安全性

## API 接口

### 1. 用户注册

#### 1.1 发送注册验证码
```http
POST /user/register/send-code
Content-Type: application/json
```

**请求参数**:
```json
{
  "email": "<EMAIL>"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": "验证码已发送"
}
```

**错误响应**:
```json
{
  "code": 1,
  "message": "邮箱已被注册",
  "data": null
}
```

#### 1.2 用户注册
```http
POST /user/register
Content-Type: application/json
```

**请求参数**:
```json
{
  "userName": "testuser",
  "password": "password123",
  "realName": "张三",
  "email": "<EMAIL>",
  "phone": "***********",
  "code": "123456"
}
```

**字段说明**:
- `userName`: 用户名，必填，不能为空
- `password`: 密码，必填，不能为空
- `realName`: 真实姓名，必填，不能为空
- `email`: 邮箱，必填，需符合邮箱格式
- `phone`: 手机号，必填，不能为空
- `code`: 验证码，必填，6位数字

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "userId": 1,
    "userName": "testuser",
    "realName": "张三",
    "email": "<EMAIL>",
    "phone": "***********",
    "createDate": "2024-01-01T10:00:00",
    "roles": ["ROLE_USER"]
  }
}
```

### 2. 用户登录

#### 2.1 账号密码登录
```http
POST /user/login
Content-Type: application/json
```

**请求参数**:
```json
{
  "account": "testuser",
  "password": "password123",
}
```

**字段说明**:
- `account`: 账号，支持用户名或邮箱，必填
- `password`: 密码，必填

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "userId": 1,
    "userName": "testuser",
    "realName": "张三",
    "email": "<EMAIL>",
    "phone": "***********",
    "loginDate": "2024-01-01T10:00:00",
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "roles": ["ROLE_USER"]
  }
}
```

#### 2.2 发送登录验证码
```http
POST /user/send-code
Content-Type: application/json
```

**请求参数**:
```json
{
  "email": "<EMAIL>"
}
```

#### 2.3 邮箱验证码登录
```http
POST /user/login-with-code
Content-Type: application/json
```

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**字段说明**:
- `email`: 邮箱，必填，需符合邮箱格式
- `code`: 验证码，必填，6位数字

### 3. 密码重置

#### 3.1 发送重置密码验证码
```http
POST /user/forget-pwd/send-code
Content-Type: application/json
```

**请求参数**:
```json
{
  "email": "<EMAIL>"
}
```

#### 3.2 重置密码
```http
POST /user/forget-pwd/reset
Content-Type: application/json
```

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "newPassword": "newpass123"
}
```

**字段说明**:
- `email`: 邮箱，必填
- `code`: 验证码，必填，6位数字
- `newPassword`: 新密码，必填，长度3-10位

### 4. Token 验证

#### 4.1 检查Token是否过期
```http
GET /user/is_expire
Authorization: Bearer {token}
```

**响应示例**:
```json
true  // true表示已过期，false表示未过期
```

## 数据模型

### User 实体
```java
{
  "userId": "Integer",           // 用户ID
  "userName": "String",          // 用户名
  "password": "String",          // 加密密码
  "realName": "String",          // 真实姓名
  "email": "String",             // 邮箱
  "phone": "String",             // 手机号
  "createDate": "LocalDateTime", // 创建时间
  "updateDate": "LocalDateTime", // 更新时间
  "loginDate": "LocalDateTime",  // 登录时间
  "token": "String",             // JWT令牌
  "roles": "List<String>"        // 用户角色列表
}
```

### Role 实体
```java
{
  "roleId": "Integer",           // 角色ID
  "roleName": "String",          // 角色名称
  "roleDescribe": "String",      // 角色描述
  "createTime": "LocalDateTime"  // 创建时间
}
```

### UserInfoDTO
```java
{
  "userId": "Integer",           // 用户ID
  "userName": "String",          // 用户名
  "realName": "String",          // 真实姓名
  "email": "String",             // 邮箱
  "phone": "String",             // 手机号
  "loginDate": "LocalDateTime",  // 登录时间
  "token": "String",             // JWT令牌
  "roles": "List<String>"        // 用户角色列表
}
```

## 错误码说明

### 通用错误码
- `0`: 操作成功
- `1`: 操作失败

### HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误（注册异常）
- `401`: 认证失败（登录异常）
- `403`: 权限不足
- `500`: 服务器内部错误

### 常见错误信息
- `"邮箱已被注册"`: 注册时邮箱已存在
- `"验证码错误或已过期"`: 验证码无效
- `"账号不存在"`: 登录时账号不存在
- `"密码错误"`: 登录时密码错误
- `"邮箱未注册"`: 邮箱验证码登录时邮箱不存在

## 安全机制

### 密码加密
- 使用 BCrypt 算法进行密码加密
- 每次加密结果不同，提高安全性

### Token 管理
- Redis 存储token，支持过期自动清理
- 支持单点登录，新登录会使旧token失效
- token与用户ID双向映射，便于管理

### 验证码安全
- 验证码有效期5分钟
- 不同用途使用不同前缀区分
- 验证后自动清理

### 权限控制
- Spring Security 配置URL级权限
- 方法级权限注解
- 双重校验确保安全

## 配置说明

### JWT 配置
```yaml
jwt:
  secret: xGO0qWbN3IiPBpz+EQwUUp/Z9WiAhOpgb67lyUPqkORjtSGn+4aN5Sqpy3jfsJI+p6O3UlUbLumUdwDvfm1Vrg==
  expiration: 86400000  # 24小时
```

### 邮件服务配置
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 465
    protocol: smtp
    username: <EMAIL>
    password: qctkhfgvotigdddj
    properties:
      mail:
        smtp:
          ssl:
            enable: true
          auth: true
    default-encoding: utf-8
```

### Redis 配置
```yaml
spring:
  data:
    redis:
      host: *************
      port: 6378
```

## 使用示例

### 完整注册流程
```javascript
// 1. 发送注册验证码
const sendCodeResponse = await fetch('/user/register/send-code', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email: '<EMAIL>' })
});

// 2. 用户注册
const registerResponse = await fetch('/user/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userName: 'testuser',
    password: 'password123',
    realName: '张三',
    email: '<EMAIL>',
    phone: '***********',
    code: '123456'
  })
});
```

### 完整登录流程
```javascript
// 账号密码登录
const loginResponse = await fetch('/user/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    account: 'testuser',
    password: 'password123',
    email: '<EMAIL>'
  })
});

const { data } = await loginResponse.json();
const token = data.token;

// 后续请求携带token
const protectedResponse = await fetch('/user/admin/list?page=0&size=10', {
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```