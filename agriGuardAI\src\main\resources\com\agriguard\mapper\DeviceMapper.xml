<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.DeviceMapper">

    <select id="find" resultType="com.agriguard.pojo.Device">
        SELECT * FROM device
        <where>
            <if test="deviceId != null and deviceId !=''">
                AND device_id = #{deviceId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                AND device_name LIKE CONCAT('%', #{deviceName}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type LIKE CONCAT('%', #{type}, '%')
            </if>
            <if test="place != null and place != ''">
                AND place LIKE CONCAT('%', #{place}, '%')
            </if>
            <if test="model != null and model != ''">
                AND model LIKE CONCAT('%', #{model}, '%')
            </if>
            <if test="factory != null and factory != ''">
                AND factory LIKE CONCAT('%', #{factory}, '%')
            </if>
            <if test="installTime != null">
                AND install_time = #{installTime}
            </if>
        </where>
    </select>

    <!-- 统计特定位置的异常温度传感器数量 -->
    <select id="getAbnormalCount" resultType="Integer">
        SELECT COUNT(*) AS abnormal_sensor_count
        FROM ${real} trt
                 JOIN device d ON trt.device_id = d.device_id
        WHERE d.place = #{place}
          AND NOT EXISTS (
            SELECT 1
            FROM (
                     SELECT
                         dc.device_id,
                         MAX(STR_TO_DATE(dc.update_time, '%d-%m-%Y %H:%i:%s')) AS latest_cache_time
                     FROM ${name} dc
                              JOIN device d2 ON dc.device_id = d2.device_id
                     WHERE d2.place = #{place}
                     GROUP BY dc.device_id
                 ) AS latest_cache
            WHERE latest_cache.device_id = trt.device_id
              AND TIMESTAMPDIFF(
                          MINUTE,
                          latest_cache.latest_cache_time,
                          STR_TO_DATE(trt.update_time, '%d-%m-%Y %H:%i:%s')
                  ) &lt;= 10
        )
    </select>
    <!-- 统计特定位置的传感器数量 -->
    <select id="getTotalCount" parameterType="String" resultType="Integer">
        SELECT
            COUNT(trt.device_id) AS sensor_count
        FROM
            ${real} trt
                JOIN
            device d ON trt.device_id = d.device_id
        WHERE
            d.place = #{place}
        GROUP BY
            d.place
    </select>
    <!-- 新增：查询指定区域的异常设备ID列表 -->
    <select id="getAbnormalDeviceIds" resultType="Integer">
        SELECT DISTINCT trt.device_id
        FROM ${real} trt
                 JOIN device d ON trt.device_id = d.device_id
        WHERE d.place = #{place}
          AND NOT EXISTS (
            SELECT 1
            FROM (
                     SELECT
                         dc.device_id,
                         MAX(STR_TO_DATE(dc.update_time, '%d-%m-%Y %H:%i:%s')) AS latest_cache_time
                     FROM ${name} dc
                              JOIN device d2 ON dc.device_id = d2.device_id
                     WHERE d2.place = #{place}
                     GROUP BY dc.device_id
                 ) AS latest_cache
            WHERE latest_cache.device_id = trt.device_id
              AND TIMESTAMPDIFF(
                      MINUTE,
                          latest_cache.latest_cache_time,
                          STR_TO_DATE(trt.update_time, '%d-%m-%Y %H:%i:%s')
                  ) &lt;= 10
        )
    </select>

    <select id="getDevicesByDeviceIds" resultType="com.agriguard.pojo.Device">
        SELECT * FROM device WHERE device_id IN
        <foreach item="deviceId" index="index" collection="deviceIds"
                 open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>
    <select id="getRealTimeData" resultType="java.util.Map">
        SELECT trt.*, d.place
        FROM ${tableName} trt
                 JOIN device d ON trt.device_id = d.device_id
        WHERE d.place = #{place}
    </select>
</mapper>
