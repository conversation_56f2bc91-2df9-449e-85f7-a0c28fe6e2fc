package com.agriguard.service.impl;

import com.agriguard.mapper.*;
import com.agriguard.service.AiCommandService;
import com.agriguard.service.CommandHandlerService;
import com.agriguard.service.MlPredictionService;
import com.agriguard.service.iotservice.HuaweiIotCommandService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import java.math.BigDecimal;
import java.util.List;


@Service
public class AiCommandServiceImpl implements AiCommandService {

    @Autowired
    private HuaweiIotCommandService huaweiIotCommandService;
    @Autowired
    private List<CommandHandlerService> commandHandlers; // Spring会自动注入所有实现
    @Autowired
    private MlPredictionService mlPredictionService;
    @Autowired
    private TemperatureMapper temperatureMapper;
    @Autowired
    private HumidityMapper humidityMapper;
    @Autowired
    private AmmoniaMapper ammoniaMapper;
    @Autowired
    private Co2Mapper co2Mapper;
    @Autowired
    private AirPressureMapper airPressureMapper;
    @Autowired
    private FanMapper fanMapper;
    @Autowired
    private NappeMapper nappeMapper ;
    @Autowired
    private WindowMapper windowMapper;
    @Autowired
    private HeatMapper heatMapper ;

    // 创建Logger实例
    private static final Logger log = LoggerFactory.getLogger(AiCommandServiceImpl.class);

    //获取给Ai的环境参数
    public BigDecimal getAiTemp() {
        return temperatureMapper.getAiTemp();
    }
    public Integer getAiHum() {
        return humidityMapper.getAiHum();
    }
    public Integer getAiAmm() {
        return ammoniaMapper.getAiAmm();
    }
    public Integer getAiCo2() {
        return co2Mapper.getAiCo2();
    }
    public BigDecimal getInAiAirPressure() {
        return airPressureMapper.getAiAirPressure(62);
    }
    public BigDecimal getOutAiAirPressure() {
        return airPressureMapper.getAiAirPressure(63);
    }



    //调用api获取python模型处理的数据，得到命令
    public Integer[] getMlPredictionCommands() {
        try {
            // 获取当前环境参数
            BigDecimal temperature = getAiTemp();
            Integer humidity = getAiHum();
            Integer ammonia = getAiAmm();
            Integer co2 = getAiCo2();
            BigDecimal internalPressure = getInAiAirPressure();
            BigDecimal externalPressure = getOutAiAirPressure();
            
            log.info("准备调用ML API预测，环境参数: 温度={}, 湿度={}, 氨气={}, 二氧化碳={}, 内气压={}, 外气压={}", 
                    temperature, humidity, ammonia, co2, internalPressure, externalPressure);
            
            // 调用ML预测服务
            Integer[] predictions = mlPredictionService.predictControlCommands(
                    temperature, humidity, ammonia, co2, internalPressure, externalPressure);
            
            log.info("ML API预测结果: 大风机1={}, 大风机2={}, 水帘={}, 加热片={}, 窗户={}, 小风扇1={}, 小风扇2={}", 
                    predictions[0], predictions[1], predictions[2], predictions[3], predictions[4], predictions[5], predictions[6]);
            
            return predictions;
            
        } catch (Exception e) {
            log.error("调用ML API预测时发生异常", e);
            // 返回默认命令（所有设备关闭）
//            return new Integer[]{0, 0, 0, 0, 0, 0, 0};
            return null;
        }
    }

    //执行Ai的命令

    public void Aicommand(){
        // 调用ML API获取预测命令
        Integer[] predictions = getMlPredictionCommands();
        
        //解析AI的命令：大风机1，大风机2，水帘状态，加热片状态，窗户状态，小风扇1，小风扇2
        Integer fan1 = predictions[0];  // 大风机1状态
        Integer fan2 = predictions[1];  // 大风机2状态
        Integer nappe = predictions[2]; // 水帘状态
        Integer heat = predictions[3];  // 加热片状态
        Integer window = predictions[4]; // 窗户状态
        Integer fan3 = predictions[5];  // 小风扇1状态
        //如果大风机要开，小风扇也要开，所以如果大风机开了，小风扇就关
        if (fan1 !=0 || fan2!=0) {
            fan3 = 0;
        }
        // Integer fan4 = predictions[6];  // 小风扇2状态（如果需要的话）
        
        // 打印解析后的命令（用于调试）
        System.out.println("=== AI预测命令解析结果 ===");
        System.out.println("大风机1状态: " + fan1 + " (" + getStatusDescription(fan1, "fan") + ")");
        System.out.println("大风机2状态: " + fan2 + " (" + getStatusDescription(fan2, "fan") + ")");
        System.out.println("水帘状态: " + nappe + " (" + getStatusDescription(nappe, "switch") + ")");
        System.out.println("加热片状态: " + heat + " (" + getStatusDescription(heat, "switch") + ")");
        System.out.println("窗户状态: " + window + " (" + getStatusDescription(window, "switch") + ")");
        System.out.println("小风扇状态: " + fan3 + " (" + getStatusDescription(fan3, "fan") + ")");
        System.out.println("========================");
        
        // 暂时不执行实际的命令，只打印结果
//        log.info("AI命令解析完成，暂时不执行实际控制命令");
        
        // TODO: 后续需要时可以启用以下代码来执行实际的设备控制命令
        //执行大风机的命令
        //fan1对应风机4,和风机6
        //判断风机1是否已经在运行
        if(!fanMapper.fanAiCommand(4).equals(fan1)){
            //不相等就执行命令,并转换命令
            command("Motor1",convertFanStatus(fan1), 4);
        }
        //判断风机3是否已经在运行
        if(!fanMapper.fanAiCommand(6).equals(fan2)){
            //不相等就执行命令,并转换命令
            command("Motor3",convertFanStatus(fan2), 6);
        }
        //fan2对应风机5,和风机7
        //判断风机1是否已经在运行
        if(!fanMapper.fanAiCommand(5).equals(fan1)){
            //不相等就执行命令,并转换命令
            command("Motor2",convertFanStatus(fan1), 5);
        }
        //判断风机3是否已经在运行
        if(!fanMapper.fanAiCommand(7).equals(fan2)){
            //不相等就执行命令,并转换命令
            command("Motor4",convertFanStatus(fan2), 7);
        }

        // TODO: 后续需要时可以启用以下代码来执行实际的水帘控制命令
        //执行水帘的命令
        //判断水帘2是否已经在运行,因为水帘开启都是两个一起开，两个一起关的，所以判断一个水帘就可以
        if(!nappeMapper.nappeAiCommand(2).equals(nappe)){
            //不相等就执行命令,并转换命令
            String nappeStr;
            if(nappe.equals(0)){
                 nappeStr = "ON";
            }else {
                 nappeStr = "OFF";
            }
            command("WP",nappeStr, 2);
            command("WP",nappeStr, 3);
        }

        //执行加热片的命令
        //判断加热片11是否已经在运行,因为加热片开启都是三个一起开，三个一起关的，所以判断一个加热片就可以
        if(!heatMapper.heatAiCommand(11).equals(heat)){
            //不相等就执行命令,并转换命令
            String heatStr;
            if(heat.equals(0)){
                heatStr = "ON";
            }else {
                heatStr = "OFF";
            }
            command("PTC",heatStr, 11);
            command("PTC",heatStr, 12);
            command("PTC",heatStr, 73);
        }

        //执行窗户命令
        String windowStr;
        if(window.equals(0)){
            windowStr = "ON";
        }else {
            windowStr = "OFF";
        }
        //判断窗户8是否打开
        if(!windowMapper.windowAiCommand(8).equals(window)){
            //不相等就执行命令,并转换命令
            command("Servo1", windowStr, 8);
        }
        //判断窗户9是否打开
        if(!windowMapper.windowAiCommand(9).equals(window)){
            //不相等就执行命令,并转换命令
            command("Servo2", windowStr, 9);
        }
        //判断窗户10是否打开
        if(!windowMapper.windowAiCommand(10).equals(window)){
            //不相等就执行命令,并转换命令
            command("Servo3", windowStr, 10);
        }
        //判断窗户72是否打开
        if(!windowMapper.windowAiCommand(72).equals(window)){
            //不相等就执行命令,并转换命令
            command("Servo4", windowStr, 72);
        }
        //执行小风扇的命令
        //fan3对应小风扇74,75,76,77
        //小风扇都是一起开关的，所以判断一个就可以，判断小风扇74是否已经在运行
        if(!fanMapper.fanAiCommand(74).equals(fan3)){
            //不相等就执行命令,并转换命令
            command("Fan",convertFanStatus(fan3), 74);
//            command("Fan",convertFanStatus(fan3), 75);
//            command("Fan",convertFanStatus(fan3), 76);
//            command("Fan",convertFanStatus(fan3), 77);
        }
    }




    //命令的执行的方法
    public void command(@RequestParam String commandName, String parasValue, Integer deviceId){
        try {
            // 1. 发送命令到IoT平台
            if (!huaweiIotCommandService.sendCommand(commandName, parasValue)) {
                log.info("Ai的命令发送失败");
                return;
            }
            System.out.println("开始配置命令并存储数据库！");

            //发送成功并返回响应后的处理
            // 2. 查找合适的处理器
            CommandHandlerService handler = commandHandlers.stream()
                    .filter(h -> h.supports(commandName))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("不支持的命令类型"));

            // 3. 处理命令并更新数据库
            System.out.println(deviceId);
            handler.handle(commandName, parasValue, deviceId);
            //日志记录Ai的操作
            log.info("Ai下发了{}命令，命令值是{}，控制了设备{}",commandName, parasValue, deviceId);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("Ai尝试控制设备失败！");
        }
    }

    //转换风机状态
    private static String convertFanStatus(Integer value) {
        if (value == null) {
            return null;
        }
        return switch (value) {
            case 0 -> "OFF";
            case 1 -> "G1";
            case 2 -> "G2";
            default -> "G3";
        };
    }
    
    //获取状态描述
    private String getStatusDescription(Integer value, String deviceType) {
        if (value == null) {
            return "未知";
        }
        
        if ("fan".equals(deviceType)) {
            return switch (value) {
                case 0 -> "关闭";
                case 1 -> "低速";
                case 2 -> "中速";
                case 3 -> "高速";
                default -> "未知状态(" + value + ")";
            };
        } else if ("switch".equals(deviceType)) {
            return switch (value) {
                case 0 -> "关闭";
                case 1 -> "开启";
                default -> "未知状态(" + value + ")";
            };
        }

        return "未知类型";
    }
}
