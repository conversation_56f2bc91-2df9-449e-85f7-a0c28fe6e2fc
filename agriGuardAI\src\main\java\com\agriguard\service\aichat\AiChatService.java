package com.agriguard.service.aichat;

import com.agriguard.dto.aichat.ChatRequestDTO;
import com.agriguard.dto.aichat.ChatResponseDTO;

/**
 * AI聊天服务接口
 * 支持OpenAI兼容格式的各种AI模型，包括Function Calling功能
 */
public interface AiChatService {

    /**
     * 发送聊天消息
     *
     * @param chatRequest 聊天请求
     * @param userId 用户ID，用于会话隔离
     * @return 聊天响应
     */
    ChatResponseDTO chat(ChatRequestDTO chatRequest, Long userId);

    /**
     * 获取会话历史
     *
     * @param sessionId 会话ID
     * @param userId 用户ID，用于权限验证
     * @return 会话历史
     */
    String getSessionHistory(String sessionId, Long userId);

    /**
     * 清空会话历史
     *
     * @param sessionId 会话ID
     * @param userId 用户ID，用于权限验证
     */
    void clearSession(String sessionId, Long userId);

    /**
     * 获取用户的会话列表
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    java.util.List<java.util.Map<String, Object>> getUserSessions(Long userId);
}