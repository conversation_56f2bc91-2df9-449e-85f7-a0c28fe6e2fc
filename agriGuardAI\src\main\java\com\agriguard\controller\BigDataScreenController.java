package com.agriguard.controller;

import com.agriguard.dto.BigDataScreen.*;
import com.agriguard.pojo.Result;
import com.agriguard.service.AiCommandService;
import com.agriguard.service.BigDataScreenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Random;

@RestController
@RequestMapping("/bigdatascreen")
public class BigDataScreenController {

    @Autowired
    private AiCommandService aiCommandService;

    boolean onoff = false;

    //测试：每分钟执行一次
//    @Scheduled(cron = "0 0/1 * * * ? ")
    @Scheduled(cron = "0/10 * * * * ?  ")
    @Async("asyncExecutor")
    public void scheduledTask() {
        try {
            if (onoff) {
                // 调用Service中的业务方法
                System.out.println("aiaiaiai开始执行定时任务...");
                aiCommandService.Aicommand();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //开启AI自动控制
    public Integer aiOrderCommandon() {
        onoff = true;
        return 1;
    }

    //关闭AI自动控制
    public Integer aiOrderCommandOff() {
        onoff = false;
        return 0;
    }

    public Integer get_status() {
        return onoff ? 1 : 0;
    }


    @Autowired
    private BigDataScreenService bigDataScreenService;

    // 设备类型分布接口
    @GetMapping("/type-distribution")
    public Result<List<DeviceTypeDistribution>> getDeviceTypeDistribution(@RequestHeader String Authorization) {
        try {
            List<DeviceTypeDistribution> distribution = bigDataScreenService.getDeviceTypeDistribution();
            return Result.success(distribution);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 分时段平均温湿度接口
    @GetMapping("/hourly-avg-temp-humidity")
    public Result<List<TempHumidityByPeriod>> getHourlyAvgTempHumidity(@RequestHeader String Authorization) {
        try {
            List<TempHumidityByPeriod> result = bigDataScreenService.getHourlyAvgTempHumidity();
            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 传感器数量接口
    @GetMapping("/sensor-counts")
    public Result<List<SensorCountDTO>> getSensorCounts(@RequestHeader String Authorization) {
        try {
            List<SensorCountDTO> counts = bigDataScreenService.getSensorCounts();
            return Result.success(counts);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //气压实时平均值
    @GetMapping("/avg-air-pressure")
    public Result<AvgAirPressureDTO> getAvgAirPressure(@RequestHeader String Authorization) {
        try {
            AvgAirPressureDTO result = bigDataScreenService.getAvgAirPressure();
            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 新增气体浓度接口
    @GetMapping("/hourly-avg-gas")
    public Result<List<GasConcentrationByPeriod>> getHourlyAvgGas(@RequestHeader String Authorization) {
        try {
            List<GasConcentrationByPeriod> result = bigDataScreenService.getHourlyAvgGasConcentration();
            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 仓库设备数量接口
    @GetMapping("/warehouse-equipment")
    public Result<List<WarehouseEquipmentCount>> getWarehouseEquipment(@RequestHeader String Authorization) {
        try {
            List<WarehouseEquipmentCount> counts = bigDataScreenService.getWarehouseEquipmentCount();
            return Result.success(counts);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/Get_ai-control")
    public Result<Integer> Get_toggleAIControl(@RequestHeader String Authorization) {
        try {
            return Result.success(get_status());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    // 新增接口：AI控制和人工控制切换接口
    @PostMapping("/Set_ai-control")
    public Result<String> Set_toggleAIControl(@RequestHeader String Authorization, @RequestParam String controlType) {
        try {
            // 调用服务层方法处理AI控制切换
            if (controlType.equals("1")) {
                aiOrderCommandon();
            } else {
                aiOrderCommandOff();
            }
            return Result.success(controlType);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("切换失败: " + e.getMessage());
        }
    }
}
