#UserController
post /user/register
{
    "userName": "ych",
    "password": "ych",
    "email": "<EMAIL>",
    "phone": "***********",
    "realName": "ych"
}

post /user/login
{
    "account": "ych",
    "password": "ych"
}

#DeviceController
Get /device/find?pageNum=1&pageSize=5

Get /device/findall

Post /device/add
{
    "deviceId": 4,
    "deviceName": "侯成",
    "type": "人类",
    "place": "S121",
    "model": "？",
    "factory": "深藏blue",
    "installTime": "2026-06-06",
    "updateTime": "2025-06-06T18:17:46"
}

Put /device/update
{
    "deviceId": 4,
    "deviceName": "侯成111",
    "type": "人类",
    "place": "S121",
    "model": "？",
    "factory": "深藏blue",
    "installTime": "2026-06-06",
    "updateTime": "2025-06-06T18:17:46"
}

Delete /device/delete?deviceId=4


