package com.agriguard.service.aichat.function;

import com.alibaba.fastjson2.JSONObject;

/**
 * 工具函数执行器接口
 * 所有工具函数都需要实现这个接口
 */
public interface FunctionExecutor {
    
    /**
     * 获取函数定义
     * 
     * @return 函数定义
     */
    FunctionDefinition getFunctionDefinition();
    
    /**
     * 执行函数
     * 
     * @param parameters 函数参数
     * @param userId 用户ID，用于权限验证
     * @return 执行结果
     * @throws Exception 执行异常
     */
    Object execute(JSONObject parameters, Long userId) throws Exception;
    
    /**
     * 验证参数
     * 
     * @param parameters 函数参数
     * @return 验证是否通过
     */
    default boolean validateParameters(JSONObject parameters) {
        return true;
    }
    
    /**
     * 检查用户权限
     * 
     * @param userId 用户ID
     * @return 是否有权限
     */
    default boolean checkPermission(Long userId) {
        return true;
    }
}
