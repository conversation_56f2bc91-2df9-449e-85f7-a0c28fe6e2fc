package com.agriguard.controller;

import com.agriguard.pojo.People;
import com.agriguard.service.PeopleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/people")
public class PeopleController {

    @Autowired
    private PeopleService peopleService;

    @GetMapping("/find")
   public People findById(@RequestHeader String Authorization, Integer id){
       return peopleService.findById(id);
   }
}
