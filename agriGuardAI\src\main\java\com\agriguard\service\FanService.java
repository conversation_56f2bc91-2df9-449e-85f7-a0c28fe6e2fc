package com.agriguard.service;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.FanRealTime;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;

import java.time.LocalDate;

public interface FanService {

    //多条件分页模糊查询风机状态
    PageResult<FanRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId,String deviceName,
                                             Integer fanStatus, String place,Integer gear);

    //添加风机状态
    String addFanRealTime(Integer deviceId);

    //修改风机状态
    void updateStatus(Integer deviceId, Integer fanStatus);

    //修改档位
//    void updateGear(Integer deviceId, Integer gear);
    Result<String> updateGear(Integer deviceId, Integer gear);

    //根据风机id查询风机详细信息
    Device findFanDetailById(Integer deviceId);

    //查询历史数据
    PageResult<FanRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData);
}
