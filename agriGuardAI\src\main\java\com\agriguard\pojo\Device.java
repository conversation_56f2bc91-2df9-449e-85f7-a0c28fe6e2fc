package com.agriguard.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class Device {
    private Integer deviceId;
    private String deviceName;
    private String type;
    private String place;
    private String model;
    private String factory;
    @JsonFormat(pattern = "yyyy-MM-dd ", timezone = "GMT+8")
    private LocalDate installTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    private String description;


    public void setUpdate_time(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
