package com.agriguard.service.iotservice;

import com.agriguard.mapper.HeatMapper;
import com.agriguard.mapper.WindowMapper;
import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class HeatCommandHandler implements CommandHandlerService {

    @Autowired
    private HeatMapper heatMapper;
    @Override
    public boolean supports(String commandType) {
        return commandType.startsWith("PTC");
    }

    @Override
    public Result<String> handle(String commandName, String parasvalue, Integer deviceId) {
        //生成一个统一的时间便于实时状态表和历史数据表统一时间
        LocalDateTime updateTime = LocalDateTime.now();
        switch (parasvalue) {
            case "ON":
                // 处理窗户开启逻辑
                heatMapper.ordercommand(deviceId, 1,updateTime);
                heatMapper.updateHeatDateHistory(deviceId, 1,updateTime);
                break;
            case "OFF":
                // 处理窗户关闭逻辑
                heatMapper.ordercommand(deviceId, 0,updateTime);
                heatMapper.updateHeatDateHistory(deviceId, 0,updateTime);
                break;
            default:
                return Result.error("未知的窗户命令");
        }
        return Result.success("窗户操作成功");
    }
}
