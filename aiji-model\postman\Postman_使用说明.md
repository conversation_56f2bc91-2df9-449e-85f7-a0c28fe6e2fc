# Postman测试集合使用说明

## 概述

本文档说明如何使用Postman测试集合来测试家禽养殖场机器学习预测API的各项功能。

## 文件说明

- **测试集合文件**: `Poultry_Farm_ML_API.postman_collection.json`
- **包含测试用例**: 8个完整的API测试用例
- **覆盖功能**: 健康检查、单次预测、批量预测、错误处理、API文档

## 导入步骤

### 1. 打开Postman
启动Postman应用程序

### 2. 导入集合
1. 点击左上角的 **Import** 按钮
2. 选择 **File** 选项卡
3. 点击 **Upload Files** 
4. 选择 `Poultry_Farm_ML_API.postman_collection.json` 文件
5. 点击 **Import** 完成导入

### 3. 验证导入
导入成功后，您应该在左侧集合列表中看到：
```
📁 家禽养殖场机器学习预测API
  ├── 健康检查
  ├── 单次预测 - 正常情况
  ├── 单次预测 - 参数超出范围
  ├── 单次预测 - 缺少参数
  ├── 批量预测 - 正常情况
  ├── 批量预测 - 超过限制
  ├── API文档
  └── 404错误测试
```

## 使用前准备

### 1. 启动API服务
在运行测试前，确保API服务已启动：
```bash
python python_ml_service.py
```

### 2. 验证服务地址
默认服务地址为 `http://localhost:5000`，如果您使用了不同的端口，需要修改环境变量。

### 3. 设置环境变量（可选）
如果API服务运行在不同的地址，可以修改集合变量：
1. 右键点击集合名称
2. 选择 **Edit**
3. 切换到 **Variables** 选项卡
4. 修改 `base_url` 的值（默认: `http://localhost:5000`）

## 测试用例说明

### 1. 健康检查
- **目的**: 验证API服务状态
- **预期结果**: 返回服务健康状态信息
- **验证项**: 状态码200、响应时间、服务状态字段

### 2. 单次预测 - 正常情况
- **目的**: 测试正常的环境数据预测
- **输入数据**: 标准环境参数
- **预期结果**: 返回7个控制系统的预测状态
- **验证项**: 状态码200、响应时间<500ms、预测结果结构

### 3. 单次预测 - 参数超出范围
- **目的**: 测试参数验证功能
- **输入数据**: 温度设为100°C（超出范围）
- **预期结果**: 返回400错误和中文错误信息
- **验证项**: 状态码400、错误信息包含"超出有效范围"

### 4. 单次预测 - 缺少参数
- **目的**: 测试必需参数检查
- **输入数据**: 只提供部分参数
- **预期结果**: 返回400错误和缺少参数提示
- **验证项**: 状态码400、错误信息包含"缺少必需参数"

### 5. 批量预测 - 正常情况
- **目的**: 测试批量预测功能
- **输入数据**: 2个有效的环境数据样本
- **预期结果**: 返回批量预测结果
- **验证项**: 状态码200、样本数量、成功预测数量

### 6. 批量预测 - 超过限制
- **目的**: 测试批量预测限制
- **输入数据**: 空样本数组
- **预期结果**: 返回400错误和样本数量限制提示
- **验证项**: 状态码400、错误信息包含"样本数量不能超过100"

### 7. API文档
- **目的**: 测试API文档接口
- **预期结果**: 返回HTML格式的API文档
- **验证项**: 状态码200、Content-Type为text/html、包含API信息

### 8. 404错误测试
- **目的**: 测试不存在接口的错误处理
- **请求**: 访问不存在的路径
- **预期结果**: 返回404错误和可用接口列表
- **验证项**: 状态码404、错误信息、可用接口列表

## 运行测试

### 单个测试
1. 点击要运行的测试用例
2. 点击右上角的 **Send** 按钮
3. 查看响应结果和测试结果

### 批量运行
1. 右键点击集合名称
2. 选择 **Run collection**
3. 在弹出的窗口中点击 **Run 家禽养殖场机器学习预测API**
4. 查看所有测试的执行结果

## 测试结果解读

### 成功指标
- ✅ **绿色勾号**: 测试通过
- **状态码**: 符合预期（200、400、404等）
- **响应时间**: 在合理范围内
- **测试脚本**: 所有断言都通过

### 失败排查
- ❌ **红色叉号**: 测试失败
- **常见原因**:
  - API服务未启动
  - 端口号不正确
  - 模型文件缺失
  - 网络连接问题

### 性能监控
- **响应时间**: 单次预测应 < 500ms
- **批量预测**: 应 < 1000ms
- **健康检查**: 应 < 1000ms

## 自定义测试

### 修改测试数据
1. 点击测试用例
2. 切换到 **Body** 选项卡
3. 修改JSON数据
4. 点击 **Send** 运行

### 添加新测试
1. 右键点击集合
2. 选择 **Add request**
3. 配置请求参数和测试脚本
4. 保存并运行

## 故障排除

### 连接失败
```
Error: connect ECONNREFUSED 127.0.0.1:5000
```
**解决方案**: 确保API服务已启动

### 测试超时
```
Error: timeout of 5000ms exceeded
```
**解决方案**: 检查服务器性能或增加超时时间

### 模型未加载
```
{
  "success": false,
  "error": "模型未加载，请检查服务状态"
}
```
**解决方案**: 运行 `python ml_training_pipeline.py` 训练模型

## 技术支持

如果在使用过程中遇到问题：
1. 检查API服务是否正常启动
2. 验证模型文件是否存在
3. 确认网络连接正常
4. 查看服务器日志文件 `logs/ml_service.log`
