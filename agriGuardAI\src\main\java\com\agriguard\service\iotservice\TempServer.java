package com.agriguard.service.iotservice;


import com.agriguard.service.IotDeviceServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.Map;


//这个不需要在写了，这个只是当时拿来做测试的，现在已经不需要了，不删除是为了下次好看，方便理清思绪

@Slf4j
@RequiredArgsConstructor
@Service
public class TempServer implements IotDeviceServer {


    @Override
    public boolean findDeviceType(String deviceType) {
        String type = "TempAndHumi";
        return type.equals(deviceType);
    }
    /**
     * 先查找当天该设备有没有记录数据，有的话修改，否则添加
     *
     */
    @Override
    public void addData(String value) {
        System.out.println("这个匹配到的温度数据：" + value);
    }
}
