package com.agriguard.mapper;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.HeatRealTime;
import com.agriguard.pojo.NappeRealTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface HeatMapper {

    // 检查设备ID是否存在
    int existsByDeviceId(Integer deviceId);

    // 添加加热片状态，添加加热片设备id就可以
    void addHeatRealTime(Integer deviceId);

    // 分页查询加热片状态（默认工作状态为1）
    List<HeatRealTime> findLatestStatus(@Param("deviceId") Integer deviceId,
                                        @Param("deviceName") String deviceName,
                                        @Param("heatStatus") Integer heatStatus,
                                        @Param("place") String place);

    //查询加热片详情
    Device findHeatDetailById(Integer deviceId);

    //修改加热片状态
    void updateHeatRealTime(@Param("deviceId") Integer deviceId,
                            @Param("heatStatus") Integer heatStatus,
                            @Param("updateTime") LocalDateTime updateTime);

    // 历史数据查询
    List<NappeRealTime> findHistoryData(@Param("deviceId") Integer deviceId,
                                        @Param("start") LocalDateTime start,
                                        @Param("end") LocalDateTime end);

    // 命令执行完成后更新状态
    void ordercommand(@Param("deviceId") Integer deviceId,
                      @Param("heatStatus") Integer heatStatus,
                      @Param("updateTime") LocalDateTime updateTime);
    //增加加热片历史数据
    void updateHeatDateHistory(@Param("deviceId")Integer deviceId,
                               @Param("heatStatus")Integer heatStatus,
                               @Param("updateTime")LocalDateTime updateTime);

    //AI命令是否已存在
    Integer heatAiCommand(Integer deviceId);
}
