package com.agriguard.mapper;

import com.agriguard.dto.UserPageDto;
import com.agriguard.entity.User;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Optional;

@Mapper
public interface UserMapper {

    @Insert("INSERT INTO user (user_name, password, real_name, email, phone, create_date) " +
            "VALUES (#{userName}, #{password}, #{realName}, #{email}, #{phone}, #{createDate})")
    @Options(useGeneratedKeys = true, keyProperty = "userId")
    int insert(User user);
//    user_id,user_name,real_name,email,phone
    @Select("SELECT * FROM user WHERE user_name = #{username}")
    User selectByUsername(String username);

    @Select("SELECT * FROM user WHERE user_name = #{account} OR email = #{account}")
    User selectByAccount(@Param("account") String account);

    @Select("SELECT * FROM user WHERE email = #{email}")
    User selectByEmail(String email);

    @Update("UPDATE user SET login_date = #{loginDate} WHERE user_id = #{userId}")
    int update(User user);

    @Select("SELECT COUNT(*) FROM user WHERE user_name = #{username}")
    boolean existsByUsername(String username);

    @Select("SELECT COUNT(*) FROM user WHERE email = #{email}")
    boolean existsByEmail(String email);

    @Insert("INSERT INTO user_role (user_id, role_id) VALUES (#{userId}, #{roleId})")
    void assignUserRole(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    @Select("SELECT r.role_name FROM role r INNER JOIN user_role ur ON r.role_id = ur.role_id WHERE ur.user_id = #{userId}")
    List<String> findRolesByUserId(@Param("userId") Integer userId);

    @Select("SELECT * FROM user WHERE email = #{email}")
    Optional<User> findByEmail(String email);

    @Update("UPDATE user SET password = #{newPassword} WHERE email = #{email}")
    void updatePassword(@Param("email") String email, @Param("newPassword") String newPassword);

    @Select("SELECT u.*, r.role_name FROM user u " +
            "LEFT JOIN user_role ur ON u.user_id = ur.user_id " +
            "LEFT JOIN role r ON ur.role_id = r.role_id " +
            "ORDER BY u.create_date DESC")
    @Results({
            @Result(property = "userId", column = "user_id"),
            @Result(property = "roles", column = "user_id",
                    javaType = List.class,
                    many = @Many(select = "findRolesByUserId"))
    })
    List<UserPageDto> selectAllUsersWithRoles();

    // 支持搜索和筛选的用户列表查询
    @Select("<script>" +
            "SELECT DISTINCT u.*, r.role_name FROM user u " +
            "LEFT JOIN user_role ur ON u.user_id = ur.user_id " +
            "LEFT JOIN role r ON ur.role_id = r.role_id " +
            "<where>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (u.user_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.email LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if>" +
            "<if test='roleId != null'>" +
            "AND ur.role_id = #{roleId}" +
            "</if>" +
            "</where>" +
            "ORDER BY u.create_date DESC" +
            "</script>")
    @Results({
            @Result(property = "userId", column = "user_id"),
            @Result(property = "roles", column = "user_id",
                    javaType = List.class,
                    many = @Many(select = "findRolesByUserId"))
    })
    List<UserPageDto> selectUsersWithRolesFiltered(@Param("keyword") String keyword, @Param("roleId") Integer roleId);


    boolean existsByEmailExcludeSelf(String email, Integer userId);

    boolean accountExistsExcludeSelf(String account, Integer userId);

    Optional<Object> selectByUserId(Integer userId);

    int updatebyuserid(User user);
    void deleteUserRoles(Integer userId);
    void deleteUser(Integer userId);

    // 角色管理相关方法
    @Delete("DELETE FROM user_role WHERE user_id = #{userId}")
    void removeUserRoles(@Param("userId") Integer userId);
    
    @Update("UPDATE user_role SET role_id = #{roleId} WHERE user_id = #{userId}")
    int updateUserRole(@Param("userId") Integer userId, @Param("roleId") Integer roleId);
    
    @Select("SELECT COUNT(*) FROM user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    boolean hasRole(@Param("userId") Integer userId, @Param("roleId") Integer roleId);

    List<String> findEmailsByUserNames(List<String> userNames);
}
