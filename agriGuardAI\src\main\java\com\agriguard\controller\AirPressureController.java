package com.agriguard.controller;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import com.agriguard.service.AirPressureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/airpressure")
public class AirPressureController {
    @Autowired
    private AirPressureService airPressureService;

    // 使用Cron表达式配置每小时执行一次的定时任务
    //每个小时执行一次
//    @Scheduled(cron = "0 0 * * * ?")
    //使用每个小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ? ")
    public void scheduledTask() {
        try {
            // 调用Service中的业务方法
            airPressureService.AirPressureDateSys();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            // 新增设备类型校验
            String result = airPressureService.addAirPressureRealTime(deviceId);
            if (result != null && !result.equals("添加成功")) {
                return Result.error(result);
            }
            return Result.success("添加成功");
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //获取气压传感器详情
    @GetMapping("/detail")
    public Result<Device> getAmmoniaDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device apreDetail = airPressureService.findAirPressureDetailById(deviceId);
            return Result.success(apreDetail);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //分页查询气压历史状态
    @GetMapping("/history")
    public Result<PageResult<AirPressureRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData
    ) {
        try {
            PageResult<AirPressureRealTime> pageResult = airPressureService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //多条件分页查询气压传感器状态
    @GetMapping("/list")
    public Result<PageResult<AirPressureRealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<AirPressureRealTime> pageResult = airPressureService.findLatestStatus(pageNum, pageSize, deviceId, deviceName, place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //获取每个分钟的平均气压用于实时气压变化展示，根据位置来查询--某个位置的
    @GetMapping("/avg")
    public Result<List<AirPressureRealTimeByMinute>> getAvgAirPressure(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<AirPressureRealTimeByMinute> avgApre = airPressureService.getAvgAirPressure(place);
            return Result.success(avgApre);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史气压数据，用于折线图展示
    @GetMapping("/history24")
    public Result<List<AirPressureHistory24>> getHistory24Hours(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<AirPressureHistory24> history24Hours = airPressureService.getHistory24Hours(place);
            return Result.success(history24Hours);  // 返回成功响应
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史气压数据，用于饼图范围展示
    @GetMapping("/history24pie")
    public Result<List<AirPressurePie>> getHistory24HoursForPie(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<AirPressurePie> AirPressurePie = airPressureService.getHistory24HoursForPie(place);
            return Result.success(AirPressurePie);  // 返回成功响应

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
}
