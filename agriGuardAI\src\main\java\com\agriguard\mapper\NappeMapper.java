package com.agriguard.mapper;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.NappeRealTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDateTime;
import java.util.List;
@Mapper
public interface NappeMapper {
    // 实时状态查询
    List<NappeRealTime> findLatestStatus(@Param("deviceId") Integer deviceId,
                                         @Param("deviceName") String deviceName,
                                         @Param("nappeStatus") Integer nappeStatus,
                                         @Param("place") String place);

    // 新增实时状态记录
    void addNappeRealTime(Integer deviceId);

    // 更新实时状态
    void updateStatus(NappeRealTime nappeRealTime);



    // 设备详情查询
    Device findNappeDetailById(@Param("deviceId") Integer deviceId);

    // 历史数据查询
    List<NappeRealTime> findHistoryData(@Param("deviceId") Integer deviceId,
                                        @Param("start") LocalDateTime start,
                                        @Param("end") LocalDateTime end);

    // 检查设备ID是否存在
    int existsByDeviceId(Integer deviceId);

    //命令执行完成后更新状态
    void ordercommand(Integer deviceId, Integer nappeStatus);
    // 添加历史记录
    void addNappeDataHistory(@Param("deviceId") Integer deviceId,
                             @Param("nappeStatus") Integer nappeStatus,
                             @Param("updateTime") LocalDateTime updateTime);

    //AI命令是否已存在
    Integer nappeAiCommand(Integer deviceId);

}
