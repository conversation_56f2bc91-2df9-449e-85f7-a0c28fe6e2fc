package com.agriguard.service.impl;

import com.agriguard.mapper.PeopleMapper;
import com.agriguard.pojo.People;
import com.agriguard.service.PeopleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PeopleServiceImpl implements PeopleService {

    @Autowired
    private PeopleMapper peopleMapper;

    @Override
    public People findById(Integer id) {
        return peopleMapper.findById(id);
    }
}
