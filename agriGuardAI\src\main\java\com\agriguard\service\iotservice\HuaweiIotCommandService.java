package com.agriguard.service.iotservice;

import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.auth.AbstractCredentials;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.exception.ConnectionException;
import com.huaweicloud.sdk.core.exception.RequestTimeoutException;
import com.huaweicloud.sdk.core.exception.ServiceResponseException;
import com.huaweicloud.sdk.core.region.Region;
import com.huaweicloud.sdk.iotda.v5.*;
import com.huaweicloud.sdk.iotda.v5.model.*;
import org.springframework.stereotype.Service;

@Service
public class HuaweiIotCommandService {


    public boolean sendCommand(String commandName, String paras) {
        // The AK and SK used for authentication are hard-coded or stored in plaintext, which has great security risks. It is recommended that the AK and SK be stored in ciphertext in configuration files or environment variables and decrypted during use to ensure security.
        // In this example, AK and SK are stored in environment variables for authentication. Before running this example, set environment variables CLOUD_SDK_AK and CLOUD_SDK_SK in the local environment
        String ak = "HPUA9IKYC9V0JAZXTFYI";
        String sk = "ZjQPyebiEXDL4jGRwDX53lrxtghjdyVqDCS7A4Jo";

        // ENDPOINT：请在控制台的"总览"界面的"平台接入地址"中查看“应用侧”的https接入地址。
        String iotdaEndpoint = "83636d7850.st1.iotda-app.cn-north-4.myhuaweicloud.com";
        String projectId = "6995f05cc2f840d8856c56b4ca2cc48a";

        ICredential auth = new BasicCredentials()
                .withProjectId(projectId)
                // 标准版/企业版需要使用衍生算法，基础版请删除配置"withDerivedPredicate";
                .withDerivedPredicate(AbstractCredentials.DEFAULT_DERIVED_PREDICATE) // Used in derivative ak/sk authentication scenarios
                .withAk(ak)
                .withSk(sk);

        IoTDAClient client = IoTDAClient.newBuilder()
                .withCredential(auth)
                // 标准版/企业版：需自行创建Region对象，基础版：请使用IoTDARegion的region对象，如"withRegion(IoTDARegion.CN_NORTH_4)"
                .withRegion(new Region("cn-north-4", iotdaEndpoint))
                .build();
        CreateCommandRequest request = new CreateCommandRequest();
        request.withDeviceId("683e55d5d582f200182d794d_AIji");

        DeviceCommandRequest body = new DeviceCommandRequest();
        System.out.println(paras);
        System.out.println(commandName);
        body.withParas("{\"value\":\"" + paras + "\"}");
        body.withCommandName(commandName);
        //body.withServiceId("reboot");
        request.withBody(body);
        try {
            CreateCommandResponse response = client.createCommand(request);
            System.out.println(response.toString());
            return response.getResponse() != null;
        } catch (
                ConnectionException e) {
            e.printStackTrace();
            return false;
        } catch (
                RequestTimeoutException e) {
            e.printStackTrace();
            return false;
        } catch (
                ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
            return false;

        }
    }
}
