package com.agriguard.service.impl;

import com.agriguard.mapper.NappeMapper;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.NappeRealTime;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import com.agriguard.service.DeviceService;
import com.agriguard.service.NappeService;
import com.agriguard.service.iotservice.HuaweiIotCommandService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class NappeServiceImpl implements NappeService {

    @Autowired
    private NappeMapper nappeMapper;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private List<CommandHandlerService> commandHandlers; // Spring会自动注入所有实现

    @Autowired
    private HuaweiIotCommandService huaweiIotCommandService;

    @Override
    public PageResult<NappeRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName,
                                                      Integer nappeStatus,String place) {
        PageResult<NappeRealTime> pb = new PageResult<>();
        PageHelper.startPage(pageNum, pageSize);
        List<NappeRealTime> as = nappeMapper.findLatestStatus(deviceId, deviceName,nappeStatus,place);
        Page<NappeRealTime> p = (Page<NappeRealTime>) as;
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询水帘实时状态成功");
        return pb;
    }

    @Override
    public String addNappeRealTime(Integer deviceId) {
        // 设备存在性校验
        Device device = deviceService.findById(deviceId);
        if (device == null) return "设备不存在";

        // 设备类型校验
        if(!"水帘".equals(device.getType())) return "该设备不为水帘";

        // 重复性校验
        if(nappeMapper.existsByDeviceId(deviceId) > 0) return "该水帘已存在";

        nappeMapper.addNappeRealTime(deviceId);
        return "添加成功";
    }

    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return nappeMapper.existsByDeviceId(deviceId) > 0;
    }

    @Override
    public Result<String> updateStatus(NappeRealTime nappeRealTime) {
        //修改档位用华为云的命令，然后再调用通用的方法
        //对命令进行解析，换成华为云的命令
        //id为2,3对应自己的命令,都是全部开启或者全部关闭，所以用一个命令WP
        String parasValue;
        if(nappeRealTime.getNappeStatus().equals(0)){
            parasValue = "OFF";
        }else{
            parasValue = "ON";
        }
        Integer deviceId = nappeRealTime.getDeviceId();
        //用通用的调用华为云方法
        try {
            // 1. 发送命令到IoT平台
            if (!huaweiIotCommandService.sendCommand("WP", parasValue)) {
                return Result.error("命令发送失败");
            }
            System.out.println("开始配置命令并存储数据库！");

            //发送成功并返回响应后的处理
            // 2. 查找合适的处理器
            CommandHandlerService handler = commandHandlers.stream()
                    .filter(h -> h.supports("WP"))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("不支持的命令类型"));

            // 3. 处理命令并更新数据库
            System.out.println(deviceId);
            return handler.handle("WP", parasValue, deviceId);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("发送失败!");
        }
    }

    @Override
    public Device findNappeDetailById(Integer deviceId) {
        Device device = nappeMapper.findNappeDetailById(deviceId);
        if (device == null || device.getDeviceId() == null) {
            throw new RuntimeException("设备不存在或设备类型不匹配");
        }
        return device;
    }

    @Override
    public PageResult<NappeRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        PageResult<NappeRealTime> pb = new PageResult<>();
        PageHelper.startPage(pageNum, pageSize);
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        List<NappeRealTime> as = nappeMapper.findHistoryData(deviceId, start, end);
        Page<NappeRealTime> p = (Page<NappeRealTime>) as;
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询水帘历史数据成功");
        return pb;
    }
}
