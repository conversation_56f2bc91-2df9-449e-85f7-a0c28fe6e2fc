package com.agriguard.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Service
public class MlPredictionService {

    private static final Logger log = LoggerFactory.getLogger(MlPredictionService.class);

    @Value("${ml.api.url:http://localhost:5000}")
    private String mlApiUrl;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public MlPredictionService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 调用ML API进行预测
     *
     * @param temperature      温度
     * @param humidity         湿度
     * @param ammonia          氨气浓度
     * @param co2              二氧化碳浓度
     * @param internalPressure 内气压
     * @param externalPressure 外气压
     * @return 预测结果数组 [大风机1状态, 大风机2状态, 水帘状态, 加热片状态, 窗户状态, 小风扇1状态, 小风扇2状态]
     */
    public Integer[] predictControlCommands(BigDecimal temperature, Integer humidity, Integer ammonia,
                                            Integer co2, BigDecimal internalPressure, BigDecimal externalPressure) {
        try {
            // 构建请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("temperature", temperature != null ? temperature.doubleValue() : 25.0);
            requestBody.put("humidity", humidity != null ? humidity.doubleValue() : 60.0);
            requestBody.put("ammonia", ammonia != null ? ammonia.doubleValue() : 15.0);
            requestBody.put("co2", co2 != null ? co2.doubleValue() : 1500.0);
            requestBody.put("internal_pressure", internalPressure != null ? internalPressure : 101.3);
            requestBody.put("external_pressure", externalPressure != null ? externalPressure : 101.5);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP请求
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            log.info("正在调用ML API预测，参数: {}", requestBody);

            // 发送POST请求
            String url = mlApiUrl + "/predict";
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("ML API响应: {}", responseBody);

                // 解析响应结果
                return parseResponse(responseBody);
            } else {
                log.error("ML API调用失败，状态码: {}", response.getStatusCode());
                return getDefaultCommands();
            }

        } catch (Exception e) {
            log.error("调用ML API时发生异常", e);
            return getDefaultCommands();
        }
    }

    /**
     * 解析ML API响应
     *
     * @param responseBody 响应体JSON字符串
     * @return 命令数组
     */
    private Integer[] parseResponse(String responseBody) {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode predictions = root.get("predictions");

            if (predictions == null) {
                log.warn("响应中未找到predictions字段");
                return getDefaultCommands();
            }

            Integer[] commands = new Integer[7];

            // 解析各个设备状态
            commands[0] = parseDeviceState(predictions, "大风机1状态");
            commands[1] = parseDeviceState(predictions, "大风机2状态");
            commands[2] = parseDeviceState(predictions, "水帘状态");
            commands[3] = parseDeviceState(predictions, "加热片状态");
            commands[4] = parseDeviceState(predictions, "窗户状态");
            commands[5] = parseDeviceState(predictions, "小风扇1状态");
            commands[6] = parseDeviceState(predictions, "小风扇2状态");

            log.info("解析后的命令: 大风机1={}, 大风机2={}, 水帘={}, 加热片={}, 窗户={}, 小风扇1={}, 小风扇2={}",
                    commands[0], commands[1], commands[2], commands[3], commands[4], commands[5], commands[6]);

            return commands;

        } catch (Exception e) {
            log.error("解析ML API响应时发生异常", e);
            return getDefaultCommands();
        }
    }

    /**
     * 解析单个设备状态
     *
     * @param predictions 预测结果JSON节点
     * @param deviceName  设备名称
     * @return 设备状态值
     */
    private Integer parseDeviceState(JsonNode predictions, String deviceName) {
        try {
            JsonNode deviceNode = predictions.get(deviceName);
            if (deviceNode != null && deviceNode.has("state")) {
                return deviceNode.get("state").asInt();
            }
            log.warn("未找到设备{}的状态信息", deviceName);
            return 0;
        } catch (Exception e) {
            log.error("解析设备{}状态时发生异常", deviceName, e);
            return 0;
        }
    }

    /**
     * 获取默认命令（当API调用失败时使用）
     *
     * @return 默认命令数组（所有设备关闭）
     */
    private Integer[] getDefaultCommands() {
//        log.info("使用默认命令（所有设备关闭）");
//        return new Integer[]{0, 0, 0, 0, 0, 0, 0};
        return null;
    }

    /**
     * 检查ML服务健康状态
     *
     * @return 服务是否健康
     */
    public boolean checkHealth() {
        try {
            String url = mlApiUrl + "/health";
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.error("检查ML服务健康状态时发生异常", e);
            return false;
        }
    }
} 