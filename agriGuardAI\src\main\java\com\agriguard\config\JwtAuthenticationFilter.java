package com.agriguard.config;

import com.agriguard.mapper.RoleMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.security.Key;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Value("${jwt.secret}")
    private String secret;

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            String token = getTokenFromRequest(request);
            log.debug("请求路径: {}, Token: {}", request.getRequestURI(), token != null ? "存在" : "不存在");
            
            if (StringUtils.hasText(token) && validateToken(token)) {
                // 从Redis验证Token有效性
                String userId = redisTemplate.opsForValue().get(token);
                log.debug("从Redis获取的userId: {}", userId);
                
                if (userId != null) {
                    // 解析Token获取用户信息
                    Claims claims = parseToken(token);
                    String username = claims.getSubject();
                    log.debug("从Token解析的用户名: {}", username);
                    
                    // 获取用户角色
                    List<String> roles = roleMapper.findRolesByUserId(Integer.valueOf(userId));
                    log.debug("用户角色: {}", roles);
                    
                    // 创建认证对象
                    List<SimpleGrantedAuthority> authorities = roles.stream()
                            .map(SimpleGrantedAuthority::new)
                            .collect(Collectors.toList());
                    
                    UsernamePasswordAuthenticationToken authentication = 
                            new UsernamePasswordAuthenticationToken(username, null, authorities);
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    
                    // 设置SecurityContext
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    
                    log.debug("用户 {} 认证成功，角色: {}", username, roles);
                } else {
                    log.warn("Token在Redis中不存在或已过期");
                }
            } else {
                log.debug("Token不存在或验证失败");
            }
        } catch (Exception e) {
            log.error("JWT认证失败: {}", e.getMessage(), e);
            SecurityContextHolder.clearContext();
        }
        
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求头中提取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken)) {
            // 支持 "Bearer <token>" 格式
            if (bearerToken.startsWith("Bearer ")) {
                return bearerToken.substring(7);
            }
            // 支持直接传token的格式
            else {
                log.debug("Authorization头不包含Bearer前缀，直接使用token值");
                return bearerToken;
            }
        }
        return null;
    }

    /**
     * 验证Token格式和签名
     */
    private boolean validateToken(String token) {
        try {
            parseToken(token);
            return true;
        } catch (Exception e) {
            log.debug("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析Token
     */
    private Claims parseToken(String token) {
        Key key = Keys.hmacShaKeyFor(Decoders.BASE64.decode(secret));
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
} 