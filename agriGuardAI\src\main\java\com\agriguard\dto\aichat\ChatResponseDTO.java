package com.agriguard.dto.aichat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI聊天响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponseDTO {
    
    /**
     * AI回复的消息内容
     */
    private String message;
    
    /**
     * 会话ID，用于保持对话上下文
     */
    private String sessionId;
    
    /**
     * 本次请求消耗的token数量
     */
    private Integer tokenUsed;
    
    /**
     * 响应时间戳
     */
    private Long timestamp;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 创建成功响应
     */
    public static ChatResponseDTO success(String message, String sessionId, Integer tokenUsed) {
        return ChatResponseDTO.builder()
                .message(message)
                .sessionId(sessionId)
                .tokenUsed(tokenUsed)
                .timestamp(System.currentTimeMillis())
                .success(true)
                .build();
    }
    
    /**
     * 创建错误响应
     */
    public static ChatResponseDTO error(String errorMessage) {
        return ChatResponseDTO.builder()
                .errorMessage(errorMessage)
                .timestamp(System.currentTimeMillis())
                .success(false)
                .build();
    }
} 