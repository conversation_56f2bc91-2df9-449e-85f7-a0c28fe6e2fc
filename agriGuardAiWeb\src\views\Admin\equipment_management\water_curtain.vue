<template>
  <div class="water-curtain-container">
    <!-- 顶部标题和操作区 -->
    <div class="header">
      <h1 class="title">
        <i class="fas fa-tint"></i> 水帘管理系统
      </h1>
      <div class="controls">
        <el-button type="primary" @click="showAddWaterCurtainDialog" icon="Plus">添加水帘</el-button>
        <el-button @click="fetchWaterCurtainData" class="refresh-btn">
          <i class="fas fa-sync"></i>刷新
        </el-button>
        <el-input v-model="searchQuery" placeholder="搜索设备ID或名称" class="search-input" clearable
                  @clear="handleSearch" @keyup.enter="handleSearch">
          <template #append>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>
    <!-- 水帘数据统计卡片 -->
    <div class="stat-card">
      <h3>系统概览</h3>
      <div class="stat-row">
        <div class="stat-item">
          <div class="stat-value">{{ stats.totalWaterCurtains }}</div>
          <div class="stat-label">总水帘数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.activeWaterCurtains }}</div>
          <div class="stat-label">运行中水帘</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.errorWaterCurtains }}</div>
          <div class="stat-label">异常水帘</div>
        </div>
      </div>
    </div>

    <!-- 状态筛选区 -->
    <div class="filter-section">
      <div class="filter-group">
        <span class="filter-label">状态筛选：</span>
        <el-radio-group v-model="statusFilter" @change="fetchWaterCurtainData">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button :label="0">关闭</el-radio-button>
          <el-radio-button :label="1">开启</el-radio-button>
          <el-radio-button :label="2">故障</el-radio-button>
          <el-radio-button :label="3">维护中</el-radio-button>
        </el-radio-group>
      </div>
      <div class="filter-group">
        <span class="filter-label">位置筛选：</span>
        <el-select v-model="locationFilter" placeholder="选择位置" clearable @change="fetchWaterCurtainData">
          <el-option v-for="location in locations" :key="location" :label="location" :value="location"/>
        </el-select>
      </div>
    </div>

    <!-- 水帘状态表格 -->
    <el-table :data="waterCurtainList" style="width: 100%" v-loading="loading" class="water-curtain-table"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
      <el-table-column prop="deviceId" label="设备ID" width="120"/>
      <el-table-column prop="deviceName" label="设备名称" width="180"/>
      <el-table-column prop="place" label="位置" width="150"/>
      <el-table-column label="状态" width="150">
        <template #default="{ row }">
          <span class="status-tag" :class="statusTagClass(row.nappeStatus)">
            {{ statusTagText(row.nappeStatus) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="180"/>
      <el-table-column label="操作" width="260" align="center">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button size="small" :type="row.nappeStatus === 1 ? 'danger' : 'success'"
                       @click="toggleWaterCurtainStatus(row)">
              {{ row.nappeStatus === 1 ? "停止" : "启动" }}
            </el-button>
            <el-button type="info" size="small" @click="viewHistory(row)" icon="Histogram">历史</el-button>
            <el-button type="primary" size="small" @click="showDetailDialog(row)" icon="View">详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页控件 -->
    <div class="pagination">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                     :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper" :total="totalItems"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </div>

    <!-- 添加水帘对话框 -->
    <el-dialog v-model="addDialogVisible" title="添加新水帘" width="500px" class="add-dialog">
      <el-form :model="newWaterCurtainForm" :rules="waterCurtainRules" ref="newWaterCurtainFormRef" label-position="top">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="newWaterCurtainForm.deviceId" placeholder="输入水帘设备ID"/>
          <div class="form-tip">只需输入水帘设备ID，系统将自动识别其他信息</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addNewWaterCurtain">确认添加</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="historyDialogVisible" :title="`水帘历史记录 - ${selectedWaterCurtain?.deviceName || ''}`" width="800px"
               class="history-dialog">
      <div class="history-filter">
        <el-date-picker v-model="historyDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                        @change="fetchHistoryData"/>
      </div>
      <el-table :data="historyData" height="400" style="width: 100%" v-loading="historyLoading">
        <el-table-column prop="updateTime" label="时间" width="180"/>
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <span class="status-tag" :class="statusTagClass(row.nappeStatus)">
              {{ statusTagText(row.nappeStatus) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination v-model:current-page="historyPage" v-model:page-size="historyPageSize"
                       :page-sizes="[10, 20, 30]" layout="total, sizes, prev, pager, next, jumper"
                       :total="historyTotalItems" @size-change="handleHistorySizeChange"
                       @current-change="handleHistoryPageChange"/>
      </div>
    </el-dialog>

    <!-- 设备详情对话框 -->
    <el-dialog v-model="detailDialogVisible" :title="`设备详情 - ${currentDeviceDetail?.device_name || ''}`" width="700px"
               class="detail-dialog">
      <div v-if="currentDeviceDetail" class="device-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>设备ID：</label>
              <span>{{ currentDeviceDetail.deviceId }}</span>
            </div>
            <div class="detail-item">
              <label>设备型号：</label>
              <span>{{ currentDeviceDetail.model }}</span>
            </div>
            <div class="detail-item">
              <label>制造商：</label>
              <span>{{ currentDeviceDetail.manufacturer }}</span>
            </div>
            <div class="detail-item">
              <label>安装日期：</label>
              <span>{{ currentDeviceDetail.installation_date }}</span>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="detail-item">
              <label>设备类型：</label>
              <span>{{ currentDeviceDetail.type }}</span>
            </div>
            <div class="detail-item">
              <label>设备名称：</label>
              <span>{{ currentDeviceDetail.device_name }}</span>
            </div>
            <div class="detail-item">
              <label>位置：</label>
              <span>{{ currentDeviceDetail.place }}</span>
            </div>
            <div class="detail-item">
              <label>上次维护：</label>
              <span>{{ currentDeviceDetail.last_maintenance }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="detail-item description">
          <label>设备描述：</label>
          <p>{{ currentDeviceDetail.description }}</p>
        </div>

        <div class="detail-item status">
          <label>当前状态：</label>
          <el-tag :type="currentDeviceDetail.status === '运行中' ? 'success' :
                        currentDeviceDetail.status === '故障' ? 'danger' :
                            currentDeviceDetail.status === '维护中' ? 'warning' : 'info'">
            {{ currentDeviceDetail.status }}
          </el-tag>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue';
import {ElMessage} from 'element-plus';
import eqAPI from "@/api/equipment_management.js";


let firstSet = true
// 水帘列表相关状态
const waterCurtainList = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const searchQuery = ref('');
const statusFilter = ref('all');
const locationFilter = ref('');
const locations = ref([]);

// 水帘统计数据
const stats = reactive({
  totalWaterCurtains: 0,
  activeWaterCurtains: 0,
  errorWaterCurtains: 0,
});

// 添加水帘对话框相关
const addDialogVisible = ref(false);
const newWaterCurtainForm = reactive({
  deviceId: ''
});
const waterCurtainRules = reactive({
  deviceId: [
    {required: true, message: '请输入设备ID', trigger: 'blur'}
  ]
});

// 历史记录对话框相关
const historyDialogVisible = ref(false);
const historyData = ref([]);
const historyLoading = ref(false);
const historyPage = ref(1);
const historyPageSize = ref(10);
const historyTotalItems = ref(0);
const historyDateRange = ref([]);
const selectedWaterCurtain = ref(null);

// 设备详情对话框相关
const detailDialogVisible = ref(false);
const currentDeviceDetail = reactive({
  deviceId: "",
  type: "",
  model: "",
  manufacturer: "",
  installation_date: "",
  last_maintenance: "",
  device_name: "",
  place: "",
  status: "",
  description: "",
});

// 状态标签的样式和文本
const statusTagClass = (status) => {
  switch (status) {
    case 1:
      return 'status-active';
    case 2:
      return 'status-error';
    case 3:
      return 'status-warning';
    default:
      return 'status-inactive';
  }
};

const statusTagText = (status) => {
  switch (status) {
    case 1:
      return '开启中';
    case 2:
      return '故障';
    case 3:
      return '维护中';
    default:
      return '已关闭';
  }
};

// 获取水帘数据
const fetchWaterCurtainData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      nappeStatus: statusFilter.value === 'all' ? null : statusFilter.value,
      place: locationFilter.value || null
    };

    if (searchQuery.value) {
      const query = searchQuery.value.trim();
      if (/^\d+$/.test(query)) {
        params.deviceId = parseInt(query);
      } else {
        params.deviceName = query;
      }
    }

    const response = await eqAPI.get_all_water_curtain(
        params.pageNum,
        params.pageSize,
        params.deviceId,
        params.deviceName,
        params.nappeStatus,
        params.place
    );

    const result = response.data.data;
    const allData = result.rows

    // 应用筛选条件
    let filteredData = [...allData];

    if (statusFilter.value !== 'all') {
      filteredData = filteredData.filter(wc => wc.nappeStatus === statusFilter.value);
    }

    if (locationFilter.value) {
      filteredData = filteredData.filter(wc => wc.place.includes(locationFilter.value));
    }

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filteredData = filteredData.filter(wc => {
        const deviceIdStr = String(wc.deviceId || '').toLowerCase();
        const deviceNameStr = (wc.deviceName || '').toLowerCase();
        return deviceIdStr.includes(query) || deviceNameStr.includes(query);
      });
    }

    // 分页处理
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    waterCurtainList.value = filteredData.slice(start, end);
    totalItems.value = filteredData.length;

    if (firstSet) {
      // 更新统计数据
      stats.totalWaterCurtains = filteredData.length;
      stats.activeWaterCurtains = filteredData.filter(wc => wc.nappeStatus === 1).length;
      stats.errorWaterCurtains = filteredData.filter(wc => wc.nappeStatus === 2 || wc.nappeStatus === 3).length;
      firstSet = false
    }

  } catch (error) {
    console.error('获取水帘数据失败:', error);
    ElMessage.error('获取水帘数据失败');
  } finally {
    loading.value = false;
  }
};

// 切换水帘状态
const toggleWaterCurtainStatus = async (waterCurtain) => {
  try {
    // 确定新状态：如果是开启状态则关闭，否则开启
    const newStatus = waterCurtain.nappeStatus === 1 ? 0 : 1;

    await eqAPI.update_water_curtain_status(waterCurtain.deviceId, newStatus);

    // 更新本地状态
    waterCurtain.nappeStatus = newStatus;

    ElMessage.success(`水帘${newStatus === 1 ? '已启动' : '已停止'}`);

    // 刷新统计数据
    stats.activeWaterCurtains = waterCurtainList.value.filter(wc => wc.nappeStatus === 1).length;
    stats.errorWaterCurtains = waterCurtainList.value.filter(wc => wc.nappeStatus === 2 || wc.nappeStatus === 3).length;
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('操作失败');
  }
};

// 显示添加水帘对话框
const showAddWaterCurtainDialog = () => {
  addDialogVisible.value = true;
  // 重置表单
  newWaterCurtainForm.deviceId = '';
};

// 添加新水帘
const addNewWaterCurtain = async () => {
  try {
    const re = await eqAPI.add_water_curtain(newWaterCurtainForm.deviceId)
    if (re.data) {
      if (re.data.data === '添加成功') {
        ElMessage.success('水帘添加成功');
        addDialogVisible.value = false;
        // 刷新水帘列表
        fetchWaterCurtainData();
      } else {
        ElMessage.error('水帘添加失败：' + re.data.message);
      }
    }
  } catch (error) {
    console.error('添加水帘失败:', error);
    ElMessage.error('添加水帘失败');
  }
};

// 查看历史记录
const viewHistory = (waterCurtain) => {
  selectedWaterCurtain.value = waterCurtain;
  historyDialogVisible.value = true;
  historyDateRange.value = [];
  historyPage.value = 1;
  fetchHistoryData();
};

// 获取历史记录数据
const fetchHistoryData = async () => {
  if (!selectedWaterCurtain.value) return;

  historyLoading.value = true;
  try {
    const response = await eqAPI.get_water_curtain_history(
        historyPage.value,
        historyPageSize.value,
        selectedWaterCurtain.value.deviceId,
        historyDateRange.value[0],
        historyDateRange.value[1]
    );

    const result = response.data.data;
    historyData.value = result.rows;
    historyTotalItems.value = result.total;
  } catch (error) {
    console.error('获取历史记录失败:', error);
    ElMessage.error('获取历史记录失败');
  } finally {
    historyLoading.value = false;
  }
};

// 显示设备详情对话框
const showDetailDialog = (waterCurtain) => {
  const result = eqAPI.get_water_curtain_detail(waterCurtain.deviceId)
  result.then(response => {
    currentDeviceDetail.deviceId = response.data.data.deviceId
    currentDeviceDetail.type = response.data.data.type
    currentDeviceDetail.model = response.data.data.model
    currentDeviceDetail.manufacturer = response.data.data.factory
    currentDeviceDetail.installation_date = response.data.data.installTime
    currentDeviceDetail.last_maintenance = response.data.data.updateTime
    currentDeviceDetail.device_name = response.data.data.deviceName
    currentDeviceDetail.place = response.data.data.place
    currentDeviceDetail.status = waterCurtain.nappeStatus === 0 ? "已关闭" :
        waterCurtain.nappeStatus === 1 ? "运行中" :
            waterCurtain.nappeStatus === 2 ? "故障" :
                waterCurtain.nappeStatus === 3 ? "维护中" : "???"
    currentDeviceDetail.description = response.data.data.description
  })
  detailDialogVisible.value = true;
};

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchWaterCurtainData();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchWaterCurtainData();
};

const handleHistorySizeChange = (size) => {
  historyPageSize.value = size;
  fetchHistoryData();
};

const handleHistoryPageChange = (page) => {
  historyPage.value = page;
  fetchHistoryData();
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchWaterCurtainData();
};

onMounted(() => {
  fetchWaterCurtainData();
  try {
    eqAPI.get_all_location_of_device('水帘').then(response => {
      locations.value = response.data
    })
  } catch (error) {
    console.error('获取位置数据失败:', error);
  }
});
</script>

<style scoped>
/* 全局样式 */
.water-curtain-container {
  padding: 20px;
  background-color: #f5f7fa;
  color: #333;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 顶部标题和操作区 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaeef5;
}

.title {
  color: #1e88e5;
  font-size: 26px;
  margin: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title i {
  font-size: 28px;
  color: #1e88e5;
}

.controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.refresh-btn {
  padding: 9px 15px;
  border: 1px solid #b6d8ff;
  border-radius: 6px;
  background-color: #f0f7ff;
  color: #1e88e5;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover {
  background-color: #1e88e5;
  color: white;
  border-color: #1e88e5;
}

.search-input {
  width: 300px;
}

.search-input :deep(.el-input__inner) {
  background-color: white;
}

/* 统计卡片 */
.stat-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
}

.stat-row {
  display: flex;
  gap: 25px;
  margin-top: 15px;
}

.stat-item {
  flex: 1;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  background: #f9fbfd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1e88e5;
  margin: 10px 0;
}

.stat-label {
  color: #78909c;
  font-size: 15px;
  letter-spacing: 0.5px;
}

/* 筛选区 */
.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 25px;
  padding: 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  color: #5a6c84;
  font-size: 14px;
  font-weight: 500;
}

/* 表格样式 */
.water-curtain-table {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

:deep(.el-table__row) {
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

:deep(.el-table__cell) {
  padding: 18px 0;
  border-bottom: 1px solid #f0f0f0 !important;
}

/* 状态标签样式 */
.status-tag {
  padding: 6px 14px;
  border-radius: 15px;
  font-size: 13px;
  font-weight: 600;
  display: inline-block;
}

.status-active {
  background: #e8f5e9;
  color: #43a047;
}

.status-inactive {
  background: #f5f5f5;
  color: #9e9e9e;
}

.status-warning {
  background: #fff8e1;
  color: #ff8f00;
}

.status-error {
  background: #ffebee;
  color: #e53935;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 分页控件 */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 25px;
  padding: 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 对话框样式 */
.add-dialog,
.history-dialog {
  border-radius: 12px;
}

:deep(.el-dialog) {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__title) {
  color: #1e88e5;
  font-weight: 600;
}

.form-tip {
  font-size: 13px;
  color: #909399;
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 历史记录筛选 */
.history-filter {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 设备详情对话框样式 */
.detail-dialog .device-detail {
  padding: 15px;
}

.detail-dialog .detail-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eaeaea;
}

.detail-dialog .detail-item label {
  font-weight: 600;
  color: #5a6c84;
  display: inline-block;
  width: 100px;
  vertical-align: top;
}

.detail-dialog .detail-item span {
  color: #333;
}

.detail-dialog .description p {
  margin-top: 8px;
  color: #666;
  line-height: 1.6;
}

.detail-dialog .status .el-tag {
  font-size: 14px;
  padding: 5px 10px;
}

/* 水帘特定图标样式 */
.title i.fas.fa-tint {
  color: #1e88e5;
  font-size: 28px;
}

/* 状态标签动画效果 */
.status-tag {
  transition: all 0.3s ease;
}

.status-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 操作按钮动画 */
.action-buttons button {
  transition: all 0.2s ease;
}

.action-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.error-card {
  cursor: pointer;
  border: 1px solid #F56C6C;
  transition: all 0.3s ease;
}

.error-card:hover {
  box-shadow: 0 2px 12px rgba(245, 108, 108, 0.3);
}

.no-abnormal {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
