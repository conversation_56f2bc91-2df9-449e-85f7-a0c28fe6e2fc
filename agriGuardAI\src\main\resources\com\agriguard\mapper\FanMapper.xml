<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.FanMapper">


    <!-- 查询风机状态的结果映射 -->
    <resultMap id="FanStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="fan_status" property="fanStatus" jdbcType="TINYINT"/>
        <result column="gear" property="gear" jdbcType="TINYINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <!-- 查询所有风机更新时间 -->
    <select id="findAllUpdateTimes" parameterType="com.agriguard.pojo.FanRealTime">
        SELECT update_time FROM fan_real_time where device_id = #{deviceId}
    </select>

    <!-- 多条件查询风机状态 -->
    <select id="findLatestStatus" resultMap="FanStatusResultMap">
        SELECT
        frt.device_id,
        d.device_name,
        d.place,
        frt.fan_status,
        frt.gear,
--         frt.update_time
        DATE_FORMAT(frt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        fan_real_time frt
        JOIN
        device d ON frt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null and deviceId != ''">
            AND frt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name = #{deviceName}
        </if>
        <if test="fanStatus != null and fanStatus != ''">
            AND frt.fan_status = #{fanStatus}
        </if>
        <if test="gear != null and gear != ''">
            AND frt.gear = #{gear}
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        frt.update_time DESC
    </select>

<!--    添加风机状态-->
    <insert id="addFanRealTime" parameterType="com.agriguard.pojo.FanRealTime">
        INSERT INTO fan_real_time
            (device_id, fan_status, gear, update_time)
        VALUES
            (#{deviceId}, 0, 0,now())
    </insert>

<!--    修改风机状态-->
<!--    修改风机实时状态表-->
    <update id="updateStatus" parameterType="com.agriguard.pojo.FanRealTime">
        UPDATE fan_real_time
        SET
        fan_status = #{fanStatus},
        gear = CASE
        WHEN #{fanStatus} = 1 THEN 1  <!-- 状态为1时档位设为1 -->
        ELSE 0                        <!-- 其他状态档位设为0 -->
        END,
        update_time = #{updateTime}
        WHERE device_id = #{deviceId}
    </update>


<!--    根据风机id查询风机实时状态-->
    <select id="findLatestStatusByDeviceId" resultType="com.agriguard.pojo.FanRealTime">
        SELECT * FROM fan_real_time WHERE device_id = #{deviceId}
    </select>

<!--    修改风机挡位-->
    <update id="updateGear" parameterType="com.agriguard.pojo.FanRealTime">
        UPDATE fan_real_time
        SET
        gear = #{gear},
        update_time = #{updateTime}
        WHERE device_id = #{deviceId}
    </update>

<!--    根据风机id查询详情信息-->
    <select id="findFanDetailById" resultType="com.agriguard.pojo.Device">
        SELECT * FROM device WHERE device_id = #{deviceId}
    </select>

    <!-- 查询风机历史数据 -->
    <select id="findHistoryData" resultType="com.agriguard.pojo.FanRealTime">
        SELECT *
        FROM fan_data_history
        WHERE device_id = #{deviceId}
        <if test="start != null">
            AND update_time >= #{start}
        </if>
        <if test="end != null">
            AND update_time &lt; #{end}
        </if>
        ORDER BY update_time DESC
    </select>

<!--   风机的命令操作-->
    <update id="ordercommand" parameterType="com.agriguard.pojo.FanRealTime">
        UPDATE fan_real_time
        SET
            fan_status = #{fanStatus},
            gear = #{gear},
            update_time = #{updateTime}
        WHERE device_id = #{deviceId}
    </update>
    <!--    增加风机历史数据表-->
    <insert id="addFanDataHistory" parameterType="com.agriguard.pojo.FanRealTime">
        INSERT INTO fan_data_history
            (device_id, fan_status, gear, update_time)
        VALUES
            (#{deviceId}, #{fanStatus}, #{gear}, #{updateTime})
    </insert>
<!--    判断Ai命令是否存在-->
    <select id="fanAiCommand" resultType="int">
            select gear from fan_real_time where device_id = #{deviceId}
    </select>
</mapper>