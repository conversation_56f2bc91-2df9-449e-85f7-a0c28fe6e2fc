# 家禽养殖场机器学习预测API

基于机器学习的家禽养殖场环境控制系统预测服务，提供HTTP API接口用于预测控制系统状态。

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 训练模型
```bash
python ml_training_pipeline.py
```

### 3. 启动服务
```bash
# 默认启动 (端口5000)
python python_ml_service.py

# 自定义端口和主机
python python_ml_service.py --port 8080 --host 127.0.0.1

# 调试模式
python python_ml_service.py --debug
```

### 4. 验证服务
```bash
# Windows PowerShell
Invoke-WebRequest -Uri http://localhost:5000/health

# Linux/Mac
curl http://localhost:5000/health
```

## API接口

### 健康检查
- **接口**: `GET /health`
- **功能**: 检查服务状态和运行统计

### 单次预测
- **接口**: `POST /predict`
- **功能**: 对单组环境数据进行预测

**请求参数**:
| 参数 | 类型 | 范围 | 描述 |
|------|------|------|------|
| temperature | float | -50~60 | 温度(°C) |
| humidity | float | 0~100 | 湿度(%) |
| ammonia | float | 0~100 | 氨气浓度 |
| co2 | float | 0~10000 | 二氧化碳浓度 |
| internal_pressure | float | 90~110 | 内气压 |
| external_pressure | float | 90~110 | 外气压 |

**请求示例**:
```bash
# PowerShell
Invoke-WebRequest -Uri http://localhost:5000/predict -Method POST -ContentType "application/json" -Body '{
  "temperature": 25.0,
  "humidity": 60.0,
  "ammonia": 15.0,
  "co2": 1500.0,
  "internal_pressure": 101.3,
  "external_pressure": 101.5
}'

# curl
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{
    "temperature": 25.0,
    "humidity": 60.0,
    "ammonia": 15.0,
    "co2": 1500.0,
    "internal_pressure": 101.3,
    "external_pressure": 101.5
  }'
```

### 批量预测
- **接口**: `POST /batch-predict`
- **功能**: 批量预测多组数据（最多100个样本）

### API文档
- **接口**: `GET /docs`
- **功能**: 获取完整的HTML格式API文档

## 控制系统状态

| 系统 | 状态值 | 描述 |
|------|--------|------|
| 大风机1/2状态 | 0-3 | 0:关闭, 1:低速, 2:中速, 3:高速 |
| 小风扇1/2状态 | 0-3 | 0:关闭, 1:低速, 2:中速, 3:高速 |
| 水帘状态 | 0-1 | 0:关闭, 1:开启 |
| 加热片状态 | 0-1 | 0:关闭, 1:开启 |
| 窗户状态 | 0-1 | 0:关闭, 1:开启 |

## 错误处理

- **400**: 请求参数错误或超出范围
- **404**: 接口不存在
- **500**: 服务器内部错误

## 性能指标

- 单次预测响应时间: < 500ms
- 支持并发请求
- 批量预测最多100个样本

## 故障排除

1. **服务启动失败**
   - 检查模型文件: `models/` 目录下应有 `.pkl` 文件
   - 安装依赖: `pip install -r requirements.txt`

2. **预测失败**
   - 检查参数范围是否正确
   - 确认请求格式为JSON

3. **性能问题**
   - 检查服务器资源使用情况
   - 考虑使用生产环境部署工具如gunicorn
