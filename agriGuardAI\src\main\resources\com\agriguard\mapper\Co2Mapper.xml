<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.Co2Mapper">

    <!--    模拟华为云co2数据-->
    <!--    更新co2实时状态表-->
    <update id="updateCo2ToRealTime" parameterType="com.agriguard.pojo.Co2RealTime">
        UPDATE co2_real_time
        SET
            co2 = #{co2},
            update_time = #{updateTime}
            WHERE device_id = #{deviceId}
    </update>
    <!--    插入co2数据至温度缓存表-->
    <insert id="insertCo2ToDataCache" parameterType="com.agriguard.pojo.Co2DataCache">
        INSERT INTO co2_data_cache (device_id, co2, update_time)
        VALUES (#{deviceId}, #{co2}, #{updateTime})
    </insert>

    <!--    分析co2缓存数据-->
    <select id="getCo2StatsByTimeRange" resultType="com.agriguard.pojo.Co2DataHistory">
        SELECT
            device_id AS deviceId,
            MAX(co2) AS maxCo2,
            MIN(co2) AS minCo2,
            ROUND(AVG(co2), 1) AS avgCo2,
            COUNT(*) AS dataCount
        FROM co2_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY device_id
    </select>

    <!-- 插入co2历史数据 -->
    <insert id="addCo2DateHistory" parameterType="com.agriguard.pojo.Co2DataHistory">
        INSERT INTO co2_data_history (
            device_id, collect_date, collect_hour,
            max_co2, min_co2, avg_co2, data_count, update_time
        ) VALUES (
                     #{deviceId}, #{collectDate}, #{collectHour},
                     #{maxCo2}, #{minCo2}, #{avgCo2}, #{dataCount}, #{updateTime}
                 )
    </insert>

    <!-- 清空co2缓存表 -->
    <delete id="deleteCo2DateCacheByHour">
        DELETE FROM co2_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
    </delete>


    <!--    查看co2设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM co2_real_time WHERE device_id = #{deviceId}
    </select>

    <!--    增加co2传感器实时状态-->
    <insert id="addCo2RealTime" parameterType="com.agriguard.pojo.Co2RealTime">
        INSERT INTO co2_real_time
            (device_id, co2, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>

    <!--    查看co2传感器详情-->
    <select id="findCo2DetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!--    查询历史数据-->
    <select id="findHistoryData" resultType="com.agriguard.pojo.Co2DataHistory">
        SELECT
        device_id AS deviceId,
        collect_date AS collectDate,
        collect_hour AS collectHour,
        max_co2 AS maxCo2,
        min_co2 AS minCo2,
        avg_co2 AS avgCo2,
        data_count AS dataCount,
        update_time AS updateTime
        FROM co2_data_history
        WHERE device_id = #{deviceId}
        <if test="start != null">
            AND update_time >= #{start}
        </if>
        <if test="end != null">
            AND update_time &lt; #{end}
        </if>
        ORDER BY update_time DESC
    </select>


    <!-- 查询co2传感器状态的结果映射 -->
    <resultMap id="Co2StatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="co2" property="co2" jdbcType="DECIMAL"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询co2传感器状态 -->
    <select id="findLatestStatus" resultMap="Co2StatusResultMap">
        SELECT
        trt.device_id,
        d.device_name,
        d.place,
        trt.co2,
--        trt.update_time
        DATE_FORMAT(trt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        co2_real_time trt
        JOIN
        device d ON trt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null and deviceId != ''">
            AND trt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        trt.update_time DESC
    </select>

    <!--    获取实时co2平均值-->
    <select id="getAvgCo2" resultType="com.agriguard.entity.device.Co2RealTimeByMinute">
        SELECT
            CONCAT(DATE_FORMAT(tdc.update_time, '%Y-%m-%d %H:%i'), ':00') AS update_time,
            AVG(tdc.co2) AS co2
        FROM
            co2_data_cache tdc
                JOIN
            device d ON tdc.device_id = d.device_id
        WHERE
            d.place = #{place}
          AND tdc.update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          AND tdc.update_time &lt;= NOW()
        GROUP BY
            update_time
        ORDER BY
            update_time ASC
    </select>
    <!--    获取过去24小时的Co2，便于绘制折线统计图-->

    <select id="getHistory24Hours" parameterType="String" resultType="com.agriguard.entity.device.Co2History24">
        WITH hour_series AS (
        SELECT
        DATE_FORMAT(DATE_SUB(NOW(), INTERVAL n HOUR), '%Y-%m-%d') AS target_date,
        HOUR(DATE_SUB(NOW(), INTERVAL n HOUR)) AS target_hour
        FROM (
        SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION
        SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION
        SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION
        SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
        ) AS numbers
        ),
        devices_in_area AS (
        SELECT device_id
        FROM device
        <if test="place != null">
            WHERE place = #{place}
        </if>
        )
        SELECT
        hs.target_date AS collect_date,
        hs.target_hour AS collect_hour,
        COALESCE(MAX(t.max_co2), 0) AS max_co2,
        COALESCE(MIN(t.min_co2), 0) AS min_co2,
        COALESCE(ROUND(AVG(t.avg_co2), 1), 0) AS avg_co2
        FROM
        hour_series hs
        LEFT JOIN (
        SELECT
        collect_date,
        collect_hour,
        max_co2,
        min_co2,
        avg_co2
        FROM co2_data_history
        WHERE device_id IN (SELECT device_id FROM devices_in_area)
        AND update_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ) t ON DATE_FORMAT(t.collect_date, '%Y-%m-%d') = hs.target_date
        AND t.collect_hour = hs.target_hour
        GROUP BY hs.target_date, hs.target_hour
        ORDER BY hs.target_date ASC, hs.target_hour ASC
    </select>

    <!--    饼图数据获取sql-->
    <resultMap id="Co2PieResultMap" type="com.agriguard.entity.device.Co2Pie">
        <result property="fanwei" column="fanwei"/>
        <result property="Co2Num" column="Co2Num"/>
    </resultMap>

    <select id="getHistory24HoursForPie" resultMap="Co2PieResultMap">
        SELECT
            co2.fanwei,
            SUM(co2.data_count) AS Co2Num
        FROM (
                 SELECT
                     tdh.data_count,
                     CASE
                         WHEN tdh.avg_co2 >= 0 AND tdh.avg_co2 &lt; 400 THEN 1
                         WHEN tdh.avg_co2 >= 400 AND tdh.avg_co2 &lt; 800 THEN 2
                         WHEN tdh.avg_co2 >= 800 AND tdh.avg_co2 &lt; 1200 THEN 3
                         WHEN tdh.avg_co2 >= 1200 AND tdh.avg_co2 &lt; 1600 THEN 4
                         WHEN tdh.avg_co2 >= 1600 AND tdh.avg_co2 &lt;= 2000 THEN 5
                         ELSE 0
                         END AS fanwei
                 FROM
                     co2_data_history tdh FORCE INDEX (idx_device_date)
            JOIN
            device d FORCE INDEX (PRIMARY) ON tdh.device_id = d.device_id
                 WHERE
                     d.place = #{place}
                   AND (
                     (tdh.collect_date = CURDATE() AND tdh.collect_hour &lt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_hour &gt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date &gt; DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_date &lt; CURDATE())
                     )
             ) AS co2
        GROUP BY co2.fanwei
        ORDER BY co2.fanwei
    </select>

    <!--    获取湿度作为Ai环境参数-->
    <select id="getAiCo2" resultType="int">
        SELECT AVG(co2) AS average_co2
        FROM co2_real_time;
    </select>
</mapper>