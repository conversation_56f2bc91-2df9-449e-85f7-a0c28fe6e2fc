package com.agriguard.mapper;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface Co2Mapper {
    //插入数据
    //更新Co2实时状态表
    void updateCo2ToRealTime(Integer deviceId, Integer co2, LocalDateTime updateTime);
    //插入Co2值到Co2缓存表
    void insertCo2ToDataCache(Integer deviceId, Integer co2, LocalDateTime updateTime);

    //分析Co2缓存数据
    List<Co2DataHistory> getCo2StatsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    //添加Co2历史数据
    void addCo2DateHistory(Co2DataHistory item);

    //删除指定时间段内的Co2缓存数据
    void deleteCo2DateCacheByHour(LocalDateTime startTime, LocalDateTime endTime);

    //查询设备idCo2传感器是否已存在
    int existsByDeviceId(Integer deviceId);

    //添加Co2传感器
    void addCo2RealTime(Integer deviceId);

    //查询Co2传感器详情
    Device findCo2DetailById(Integer deviceId);

    //分页查询历史数据
    List<Co2RealTime> findHistoryData(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                      @org.apache.ibatis.annotations.Param("start")LocalDateTime start,
                                      @org.apache.ibatis.annotations.Param("end")LocalDateTime end);

    //分页查询实时状态数据
    List<Co2RealTime> findLatestStatus(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                            @org.apache.ibatis.annotations.Param("deviceName")String deviceName,
                                            @Param("place")String place);

    //查询实时状态数据
    List<Co2RealTimeByMinute> getAvgCo2(String place);

    //获取过去24小时的历史Co2数据，用于折线图展示
    List<Co2History24> getHistory24Hours(String place);

    //获取过去24小时的历史Co2数据，用于饼图展示
    List<Co2Pie> getHistory24HoursForPie(String place);

    //获取Co2作为Ai环境参数
    Integer getAiCo2();
}
