"""
家禽养殖场环境监测机器学习训练管道

该脚本实现了一个完整的机器学习管道，用于基于环境传感器数据
预测家禽养殖场控制系统状态。

输入特征（6个环境传感器）：
- 温度 (Temperature)
- 湿度 (Humidity)
- 氨气浓度 (Ammonia concentration)
- 二氧化碳浓度 (CO2 concentration)
- 内气压 (Internal air pressure)
- 外气压 (External air pressure)

输出目标（7个控制系统状态）：
- 大风机1状态 (Large fan 1 status)
- 大风机2状态 (Large fan 2 status)
- 水帘状态 (Water curtain status)
- 加热片状态 (Heating element status)
- 窗户状态 (Window status)
- 小风扇1状态 (Small fan 1 status)
- 小风扇2状态 (Small fan 2 status)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.multioutput import MultiOutputClassifier
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.metrics import mean_squared_error, mean_absolute_error
import joblib
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PoultryFarmMLPipeline:
    """家禽养殖场环境监测完整机器学习管道"""

    def __init__(self, data_path='ai_data/poultry_farm_environment_data.csv'):
        self.data_path = data_path
        self.scaler = StandardScaler()
        self.model = None
        self.feature_columns = ['温度', '湿度', '氨气浓度', '二氧化碳浓度', '内气压', '外气压']
        self.target_columns = ['大风机1状态', '大风机2状态', '水帘状态', '加热片状态',
                              '窗户状态', '小风扇1状态', '小风扇2状态']

        # 创建结果文件夹
        os.makedirs('results', exist_ok=True)

    def load_data(self):
        """加载和检查数据集"""
        print("正在加载数据...")
        self.df = pd.read_csv(self.data_path)
        print(f"数据集形状: {self.df.shape}")
        print(f"列名: {list(self.df.columns)}")

        # 基本数据检查
        print("\n数据集信息:")
        print(self.df.info())

        print("\n前5行数据:")
        print(self.df.head())

        print("\n统计摘要:")
        print(self.df.describe())

        # 检查缺失值
        print("\n缺失值:")
        print(self.df.isnull().sum())

        return self.df
    
    def exploratory_data_analysis(self):
        """执行探索性数据分析"""
        print("\n" + "="*50)
        print("探索性数据分析")
        print("="*50)

        # 特征分布图
        plt.figure(figsize=(15, 10))
        for i, col in enumerate(self.feature_columns, 1):
            plt.subplot(2, 3, i)
            plt.hist(self.df[col], bins=50, alpha=0.7)
            plt.title(f'{col} 分布')
            plt.xlabel(col)
            plt.ylabel('频次')
        plt.tight_layout()
        plt.savefig('results/特征分布图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 目标变量分布图
        plt.figure(figsize=(15, 10))
        for i, col in enumerate(self.target_columns, 1):
            plt.subplot(3, 3, i)
            self.df[col].value_counts().plot(kind='bar')
            plt.title(f'{col} 分布')
            plt.xlabel(col)
            plt.ylabel('数量')
            plt.xticks(rotation=0)
        plt.tight_layout()
        plt.savefig('results/目标变量分布图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 相关性矩阵
        plt.figure(figsize=(12, 10))
        correlation_matrix = self.df[self.feature_columns + self.target_columns].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
        plt.title('特征-目标相关性矩阵')
        plt.tight_layout()
        plt.savefig('results/相关性矩阵.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 目标变量统计
        print("\n目标变量分布:")
        for col in self.target_columns:
            print(f"\n{col}:")
            print(self.df[col].value_counts().sort_index())
    
    def preprocess_data(self, test_size=0.2, random_state=42):
        """数据预处理并分割为训练集和测试集"""
        print("\n" + "="*50)
        print("数据预处理")
        print("="*50)

        # 分离特征和目标变量
        X = self.df[self.feature_columns].copy()
        y = self.df[self.target_columns].copy()

        print(f"特征形状: {X.shape}")
        print(f"目标变量形状: {y.shape}")

        # 分割数据
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=None
        )

        print(f"训练集大小: {self.X_train.shape[0]}")
        print(f"测试集大小: {self.X_test.shape[0]}")

        # 特征缩放
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)

        print("特征缩放完成。")

        return self.X_train_scaled, self.X_test_scaled, self.y_train, self.y_test

    def train_model(self, n_estimators=100, random_state=42):
        """训练多输出分类模型"""
        print("\n" + "="*50)
        print("模型训练")
        print("="*50)

        # 使用随机森林配合多输出分类器进行多输出分类
        base_model = RandomForestClassifier(
            n_estimators=n_estimators,
            random_state=random_state,
            n_jobs=-1,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2
        )

        self.model = MultiOutputClassifier(base_model)

        print("正在训练随机森林模型...")
        self.model.fit(self.X_train_scaled, self.y_train)
        print("模型训练完成！")

        # 特征重要性分析
        self.analyze_feature_importance()

        return self.model

    def analyze_feature_importance(self):
        """分析和可视化特征重要性"""
        print("\n正在分析特征重要性...")

        # 获取每个目标变量的特征重要性
        importances = {}
        for i, target in enumerate(self.target_columns):
            importances[target] = self.model.estimators_[i].feature_importances_

        # 创建特征重要性数据框
        importance_df = pd.DataFrame(importances, index=self.feature_columns)

        # 绘制特征重要性图
        plt.figure(figsize=(12, 8))
        sns.heatmap(importance_df, annot=True, cmap='viridis', fmt='.3f')
        plt.title('各目标变量的特征重要性')
        plt.xlabel('目标变量')
        plt.ylabel('特征')
        plt.tight_layout()
        plt.savefig('results/特征重要性.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("\n特征重要性摘要:")
        print(importance_df)

        return importance_df

    def evaluate_model(self):
        """评估训练好的模型"""
        print("\n" + "="*50)
        print("模型评估")
        print("="*50)

        # 进行预测
        y_pred_train = self.model.predict(self.X_train_scaled)
        y_pred_test = self.model.predict(self.X_test_scaled)

        # 计算每个目标变量的准确率
        print("各目标变量准确率:")
        print("-" * 40)

        train_accuracies = []
        test_accuracies = []

        for i, target in enumerate(self.target_columns):
            train_acc = accuracy_score(self.y_train.iloc[:, i], y_pred_train[:, i])
            test_acc = accuracy_score(self.y_test.iloc[:, i], y_pred_test[:, i])

            train_accuracies.append(train_acc)
            test_accuracies.append(test_acc)

            print(f"{target}:")
            print(f"  训练准确率: {train_acc:.4f}")
            print(f"  测试准确率: {test_acc:.4f}")
            print()

        # 总体准确率
        overall_train_acc = np.mean(train_accuracies)
        overall_test_acc = np.mean(test_accuracies)

        print(f"总体训练准确率: {overall_train_acc:.4f}")
        print(f"总体测试准确率: {overall_test_acc:.4f}")

        # 每个目标变量的详细分类报告
        print("\n详细分类报告:")
        print("-" * 50)

        for i, target in enumerate(self.target_columns):
            print(f"\n{target}:")
            print(classification_report(self.y_test.iloc[:, i], y_pred_test[:, i]))

        # 创建混淆矩阵
        self.plot_confusion_matrices(y_pred_test)

        return {
            'train_accuracies': train_accuracies,
            'test_accuracies': test_accuracies,
            'overall_train_acc': overall_train_acc,
            'overall_test_acc': overall_test_acc,
            'predictions': y_pred_test
        }

    def plot_confusion_matrices(self, y_pred_test):
        """绘制每个目标变量的混淆矩阵"""
        print("\n正在生成混淆矩阵...")

        n_targets = len(self.target_columns)
        fig, axes = plt.subplots(3, 3, figsize=(15, 12))
        axes = axes.flatten()

        for i, target in enumerate(self.target_columns):
            cm = confusion_matrix(self.y_test.iloc[:, i], y_pred_test[:, i])

            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i])
            axes[i].set_title(f'{target}')
            axes[i].set_xlabel('预测值')
            axes[i].set_ylabel('实际值')

        # 隐藏最后两个空的子图
        for i in range(n_targets, len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        plt.savefig('results/混淆矩阵.png', dpi=300, bbox_inches='tight')
        plt.show()

    def save_model(self, model_path='models/poultry_farm_model.pkl',
                   scaler_path='models/scaler.pkl'):
        """保存训练好的模型和缩放器"""
        print("\n" + "="*50)
        print("保存模型")
        print("="*50)

        os.makedirs('models', exist_ok=True)

        # 保存模型
        joblib.dump(self.model, model_path)
        print(f"模型已保存到: {model_path}")

        # 保存缩放器
        joblib.dump(self.scaler, scaler_path)
        print(f"缩放器已保存到: {scaler_path}")

        # 保存特征和目标列名
        metadata = {
            'feature_columns': self.feature_columns,
            'target_columns': self.target_columns,
            'model_type': 'MultiOutputClassifier with RandomForest'
        }
        joblib.dump(metadata, 'models/metadata.pkl')
        print("元数据已保存到: models/metadata.pkl")

        print("模型保存完成！")

    def load_model(self, model_path='models/poultry_farm_model.pkl',
                   scaler_path='models/scaler.pkl'):
        """加载之前训练的模型和缩放器"""
        self.model = joblib.load(model_path)
        self.scaler = joblib.load(scaler_path)
        metadata = joblib.load('models/metadata.pkl')

        self.feature_columns = metadata['feature_columns']
        self.target_columns = metadata['target_columns']

        print(f"模型已从以下位置加载: {model_path}")
        print(f"缩放器已从以下位置加载: {scaler_path}")

        return self.model, self.scaler

    def predict_new_data(self, new_data):
        """对新的环境数据进行预测"""
        if self.model is None:
            raise ValueError("模型未训练或未加载。请先训练或加载模型。")

        # 确保new_data具有正确的列
        if isinstance(new_data, dict):
            new_data = pd.DataFrame([new_data])

        # 缩放新数据
        new_data_scaled = self.scaler.transform(new_data[self.feature_columns])

        # 进行预测
        predictions = self.model.predict(new_data_scaled)

        # 创建可读的输出
        result = {}
        for i, target in enumerate(self.target_columns):
            result[target] = predictions[0][i] if len(predictions) == 1 else predictions[:, i]

        return result

    def run_complete_pipeline(self):
        """运行完整的机器学习管道"""
        print("="*60)
        print("家禽养殖场环境监测机器学习管道")
        print("="*60)

        # 加载和探索数据
        self.load_data()
        self.exploratory_data_analysis()

        # 预处理数据
        self.preprocess_data()

        # 训练模型
        self.train_model()

        # 评估模型
        evaluation_results = self.evaluate_model()

        # 保存模型
        self.save_model()

        print("\n" + "="*60)
        print("管道执行成功完成！")
        print("="*60)
        print(f"总体测试准确率: {evaluation_results['overall_test_acc']:.4f}")
        print("模型和相关文件已保存在 'models/' 目录中")
        print("图表文件已保存在 'results/' 目录中")

        return evaluation_results


def main():
    """运行机器学习管道的主函数"""
    # 初始化管道
    pipeline = PoultryFarmMLPipeline()

    # 运行完整管道
    pipeline.run_complete_pipeline()

    # 新数据预测示例
    print("\n" + "="*50)
    print("预测示例")
    print("="*50)

    # 示例环境数据
    example_data = {
        '温度': 25.0,      # 温度
        '湿度': 60.0,      # 湿度
        '氨气浓度': 15.0,   # 氨气浓度
        '二氧化碳浓度': 1500.0,  # 二氧化碳浓度
        '内气压': 101.5,    # 内气压
        '外气压': 101.8     # 外气压
    }

    print("输入环境数据:")
    for key, value in example_data.items():
        print(f"  {key}: {value}")

    predictions = pipeline.predict_new_data(example_data)

    print("\n预测的控制系统状态:")
    for target, prediction in predictions.items():
        print(f"  {target}: {prediction}")


if __name__ == "__main__":
    main()
