package com.agriguard.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Random;

public class MoniDataUtil {

    public class TemperatureGenerator {
        public static String generateRandomTemperature() {
            Random random = new Random();
            double min = 25.0;
            double max = 26.0;
            double temperature = min + (max - min) * random.nextDouble();
            DecimalFormat df = new DecimalFormat("#.#");
            return df.format(temperature);
        }
    }

    public class HumidityGenerator {
        public static int generateRandomHumidity() {
            Random random = new Random();
            return random.nextInt(5) + 65; // 21 = 70-50+1，生成0-20的随机数再加50
        }
    }

    public class CO2Generator {
        public static int generateRandomCO2() {
            Random random = new Random();
            return random.nextInt(50) + 1150; // 221 = 570-350+1
        }
    }

    public class AmmoniaGenerator {
        // 生成指定范围内的随机小数（保留yi位小数）
        public static double generateRandomAmmonia() {
            Random random = new Random();
            double min = 1.0;     // 最小值（可根据需要修改）
            double max = 2.5;     // 最大值（可根据需要修改）
            double ammonia = min + (max - min) * random.nextDouble();

            // 保留两位小数（数学方法处理）
            return Math.round(ammonia * 10.0) / 10.0;
        }
    }

    public class PressureGenerator {
        public static double generateRandomPressure() {
            Random random = new Random();
            double min = 99.1;
            double max = 100.18;
            double pressure = min + (max - min) * random.nextDouble();

            // 保留两位小数（注意：直接使用DecimalFormat会返回字符串，这里采用数学方法处理）
            return Math.round(pressure * 100.0) / 100.0;
        }
    }

    public class CurrentGenerator {
        public static double generateRandomCurrent() {
            Random random = new Random();
            double min = 0.03;
            double max = 0.179;
            double current = min + (max - min) * random.nextDouble();

            // 保留三位小数（乘以1000取整后再除以1000）
            return Math.round(current * 1000.0) / 1000.0;
        }
    }

//    public static void main(String[] args) {
//        // 生成随机温度
//        BigDecimal temperature = new BigDecimal(TemperatureGenerator.generateRandomTemperature());
//        System.out.println("温度为："+temperature);
//        // 生成随机湿度
//        Integer humidity = HumidityGenerator.generateRandomHumidity();
//        System.out.println("湿度为："+humidity);
//        // 生成随机CO2
//        Integer co2 = CO2Generator.generateRandomCO2();
//        System.out.println("二氧化碳浓度为："+co2);
//        // 生成随机氨氮
//        Integer ammonia = AmmoniaGenerator.generateRandomAmmonia();
//        System.out.println("氨气浓度为："+ammonia);
//        // 生成随机气压
//        Double pressure = PressureGenerator.generateRandomPressure();
//        System.out.println("气压为："+pressure);
//        // 生成随机电流
//        Double current = CurrentGenerator.generateRandomCurrent();
//        System.out.println("电流为："+current);
//    }
}
