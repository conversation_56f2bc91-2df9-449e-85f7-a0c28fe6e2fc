package com.agriguard.service.impl;

import com.agriguard.dto.BigDataScreen.*;
import com.agriguard.mapper.BigDataScreenMapper;
import com.agriguard.service.BigDataScreenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class BigDataScreenServiceImpl implements BigDataScreenService {
    @Autowired
    BigDataScreenMapper bigDataScreenMapper;

    @Override
    public List<DeviceTypeDistribution> getDeviceTypeDistribution() {
        return bigDataScreenMapper.countDevicesByType();
    }
    @Override
    public List<TempHumidityByPeriod> getHourlyAvgTempHumidity() {
        List<TempHumidityByPeriod> tempData = bigDataScreenMapper.selectHourlyAvgTemp();
        List<TempHumidityByPeriod> humidityData = bigDataScreenMapper.selectHourlyAvgHumidity();

        // 合并所有时间段（温度+湿度）
        Set<String> allPeriods = Stream.concat(
                tempData.stream().map(TempHumidityByPeriod::getTimePeriod),
                humidityData.stream().map(TempHumidityByPeriod::getTimePeriod)
        ).collect(Collectors.toSet());

        return allPeriods.stream().map(period -> {
            TempHumidityByPeriod item = new TempHumidityByPeriod();
            item.setTimePeriod(period);

            // 设置温度（可能为null）
            tempData.stream()
                    .filter(t -> t.getTimePeriod().equals(period))
                    .findFirst()
                    .ifPresent(t -> item.setAvgTemp(t.getAvgTemp()));

            // 设置湿度（可能为null）
            humidityData.stream()
                    .filter(h -> h.getTimePeriod().equals(period))
                    .findFirst()
                    .ifPresent(h -> item.setAvgHumidity(h.getAvgHumidity()));

            return item;
        }).collect(Collectors.toList());
    }
    @Override
    public List<SensorCountDTO> getSensorCounts() {
        return bigDataScreenMapper.selectSensorCounts();
    }
    @Override
    public AvgAirPressureDTO getAvgAirPressure() {
        return bigDataScreenMapper.selectAvgAirPressure();
    }
    @Override
    public List<GasConcentrationByPeriod> getHourlyAvgGasConcentration() {
        List<GasConcentrationByPeriod> co2Data = bigDataScreenMapper.selectHourlyAvgCo2();
        List<GasConcentrationByPeriod> ammoniaData = bigDataScreenMapper.selectHourlyAvgAmmonia();

        Set<String> allPeriods = Stream.concat(
                co2Data.stream().map(GasConcentrationByPeriod::getTimePeriod),
                ammoniaData.stream().map(GasConcentrationByPeriod::getTimePeriod)
        ).collect(Collectors.toSet());

        return allPeriods.stream().map(period -> {
            GasConcentrationByPeriod item = new GasConcentrationByPeriod();
            item.setTimePeriod(period);

            co2Data.stream()
                    .filter(c -> c.getTimePeriod().equals(period))
                    .findFirst()
                    .ifPresent(c -> item.setAvgCo2(c.getAvgCo2()));

            ammoniaData.stream()
                    .filter(a -> a.getTimePeriod().equals(period))
                    .findFirst()
                    .ifPresent(a -> item.setAvgAmm(a.getAvgAmm()));

            return item;
        }).collect(Collectors.toList());
    }
    @Override
    public List<WarehouseEquipmentCount> getWarehouseEquipmentCount() {
        return bigDataScreenMapper.countWarehouseEquipment();
    }
}
