package com.agriguard.mapper;

import com.agriguard.entity.Alarm;
import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AlarmMapper {
    void insertAlarm(Alarm record);

    @Select("SELECT email FROM user WHERE user_name = #{userName}")
    String findEmailByUserName(String userName);

    List<String> getReceiversByAlarmType(@Param("place") String place,
                                         @Param("alarmType") String alarmType);
    List<Alarm> selectAlarms(
            @Param("alarmTypes") String alarmTypes,
            @Param("place") String place,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    void HandleAlarms(String alertId);
}
