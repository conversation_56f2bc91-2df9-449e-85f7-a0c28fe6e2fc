package com.agriguard.service.aichat.function.impl;

import com.agriguard.service.aichat.function.FunctionDefinition;
import com.agriguard.service.aichat.function.FunctionExecutor;
import com.agriguard.service.*;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 获取历史数据趋势分析的工具函数
 * 支持获取指定时间范围内的环境数据历史趋势
 */
@Slf4j
@Component
public class GetHistoricalTrendsFunction implements FunctionExecutor {
    
    @Autowired
    private TemperatureService temperatureService;
    
    @Autowired
    private HumidityService humidityService;
    
    @Autowired
    private Co2Service co2Service;
    
    @Autowired
    private AmmoniaService ammoniaService;
    
    @Autowired
    private AirPressureService airPressureService;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public FunctionDefinition getFunctionDefinition() {
        // 定义参数Schema
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // 数据类型参数（必需）
        Map<String, Object> dataTypeParam = new HashMap<>();
        dataTypeParam.put("type", "string");
        dataTypeParam.put("enum", Arrays.asList("temperature", "humidity", "co2", "ammonia", "pressure"));
        dataTypeParam.put("description", "数据类型");
        properties.put("data_type", dataTypeParam);
        
        // 时间范围参数（可选）
        Map<String, Object> timeRangeParam = new HashMap<>();
        timeRangeParam.put("type", "string");
        timeRangeParam.put("enum", Arrays.asList("24h", "7d", "30d"));
        timeRangeParam.put("description", "时间范围，默认24h");
        timeRangeParam.put("default", "24h");
        properties.put("time_range", timeRangeParam);
        
        // 设备ID参数（可选）
        Map<String, Object> deviceIdParam = new HashMap<>();
        deviceIdParam.put("type", "integer");
        deviceIdParam.put("description", "设备ID，可选");
        properties.put("device_id", deviceIdParam);
        
        // 聚合方式参数（可选）
        Map<String, Object> aggregationParam = new HashMap<>();
        aggregationParam.put("type", "string");
        aggregationParam.put("enum", Arrays.asList("hourly", "daily"));
        aggregationParam.put("description", "数据聚合方式，默认hourly");
        aggregationParam.put("default", "hourly");
        properties.put("aggregation", aggregationParam);
        
        // 位置参数（可选）
        Map<String, Object> locationParam = new HashMap<>();
        locationParam.put("type", "string");
        locationParam.put("description", "监控区域位置，可选");
        properties.put("location", locationParam);
        
        parameters.put("properties", properties);
        parameters.put("required", new String[]{"data_type"});
        
        return FunctionDefinition.builder()
                .name("get_historical_trends")
                .description("获取指定时间范围内的环境数据历史趋势")
                .parameters(parameters)
                .requiresAuth(false)
                .permissionLevel("read")
                .requiresConfirmation(false)
                .build();
    }
    
    @Override
    public Object execute(JSONObject parameters, Long userId) throws Exception {
        try {
            // 获取参数
            String dataType = parameters.getString("data_type");
            String timeRange = parameters.getString("time_range");
            Integer deviceId = parameters.getInteger("device_id");
            String aggregation = parameters.getString("aggregation");
            String location = parameters.getString("location");
            
            // 处理默认值
            if (timeRange == null || timeRange.trim().isEmpty()) {
                timeRange = "24h";
            }
            if (aggregation == null || aggregation.trim().isEmpty()) {
                aggregation = "hourly";
            }
            
            log.info("获取历史趋势数据，参数: dataType={}, timeRange={}, deviceId={}, aggregation={}, location={}, userId={}", 
                    dataType, timeRange, deviceId, aggregation, location, userId);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("timestamp", LocalDateTime.now().format(FORMATTER));
            result.put("data_type", dataType);
            result.put("time_range", timeRange);
            result.put("aggregation", aggregation);
            result.put("location", location);
            result.put("device_id", deviceId);
            
            // 根据数据类型获取历史数据
            Map<String, Object> historicalData = getHistoricalDataByType(dataType, location);
            
            // 计算趋势分析
            Map<String, Object> trendAnalysis = calculateTrendAnalysis(historicalData, dataType);
            
            result.put("historical_data", historicalData);
            result.put("trend_analysis", trendAnalysis);
            result.put("data_source", "AgriGuard历史数据系统");
            result.put("status", "success");
            
            log.info("历史趋势分析完成，数据类型: {}, 数据点数量: {}", 
                    dataType, historicalData.containsKey("history_24h") ? 
                    ((List<?>) historicalData.get("history_24h")).size() : 0);
            
            return result;
            
        } catch (Exception e) {
            log.error("获取历史趋势数据失败", e);
            throw new Exception("获取历史趋势数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据数据类型获取历史数据
     */
    private Map<String, Object> getHistoricalDataByType(String dataType, String location) {
        Map<String, Object> data = new HashMap<>();
        
        try {
            switch (dataType.toLowerCase()) {
                case "temperature":
                    data.put("history_24h", temperatureService.getHistory24Hours(location));
                    data.put("pie_data", temperatureService.getHistory24HoursForPie(location));
                    data.put("unit", "°C");
                    break;
                case "humidity":
                    data.put("history_24h", humidityService.getHistory24Hours(location));
                    data.put("pie_data", humidityService.getHistory24HoursForPie(location));
                    data.put("unit", "%");
                    break;
                case "co2":
                    data.put("history_24h", co2Service.getHistory24Hours(location));
                    data.put("pie_data", co2Service.getHistory24HoursForPie(location));
                    data.put("unit", "ppm");
                    break;
                case "ammonia":
                    data.put("history_24h", ammoniaService.getHistory24Hours(location));
                    data.put("pie_data", ammoniaService.getHistory24HoursForPie(location));
                    data.put("unit", "ppm");
                    break;
                case "pressure":
                    data.put("history_24h", airPressureService.getHistory24Hours(location));
                    data.put("pie_data", airPressureService.getHistory24HoursForPie(location));
                    data.put("unit", "kPa");
                    break;
                default:
                    throw new IllegalArgumentException("不支持的数据类型: " + dataType);
            }
        } catch (Exception e) {
            log.warn("获取{}历史数据失败: {}", dataType, e.getMessage());
            data.put("error", "历史数据获取失败: " + e.getMessage());
        }
        
        return data;
    }
    
    /**
     * 计算趋势分析
     * TODO: 实现更sophisticated的趋势分析算法，如线性回归、移动平均等
     */
    private Map<String, Object> calculateTrendAnalysis(Map<String, Object> historicalData, String dataType) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            List<?> historyData = (List<?>) historicalData.get("history_24h");
            if (historyData == null || historyData.isEmpty()) {
                analysis.put("status", "no_data");
                analysis.put("message", "暂无历史数据进行趋势分析");
                return analysis;
            }
            
            // TODO: 改进数据提取逻辑，基于真实的历史数据结构
            List<Double> values = extractValuesFromHistoryData(historyData);
            
            if (values.isEmpty()) {
                analysis.put("status", "no_valid_data");
                analysis.put("message", "历史数据中无有效数值");
                return analysis;
            }
            
            // 计算统计信息
            double max = values.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            double min = values.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            double avg = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            
            // 计算趋势
            String trend = calculateTrend(values);
            
            // 计算变化率
            double changeRate = calculateChangeRate(values);
            
            analysis.put("statistics", Map.of(
                "max", roundToDecimal(max, 2),
                "min", roundToDecimal(min, 2),
                "average", roundToDecimal(avg, 2),
                "range", roundToDecimal(max - min, 2)
            ));
            
            analysis.put("trend", trend);
            analysis.put("change_rate", roundToDecimal(changeRate, 2) + "%");
            analysis.put("data_points", values.size());
            analysis.put("analysis_time", LocalDateTime.now().format(FORMATTER));
            
            // 根据数据类型提供专业建议
            analysis.put("recommendations", generateRecommendations(dataType, avg, trend));
            
            analysis.put("status", "success");
            
        } catch (Exception e) {
            log.error("趋势分析计算失败", e);
            analysis.put("status", "error");
            analysis.put("error", "趋势分析失败: " + e.getMessage());
        }
        
        return analysis;
    }
    
    /**
     * 从历史数据中提取数值
     * TODO: 实现真实的历史数据解析逻辑，替换当前的模拟数据
     */
    private List<Double> extractValuesFromHistoryData(List<?> historyData) {
        List<Double> values = new ArrayList<>();
        // TODO: 根据具体的历史数据结构来解析真实数值
        // 需要分析TempHistory24、HumidityHistory24等实体的字段结构
        for (int i = 0; i < historyData.size(); i++) {
            try {
                // TODO: 实现基于真实数据结构的数值提取
                // 例如：((TempHistory24)item).getValue() 或类似的方法
                values.add(Math.random() * 100); // 临时模拟数据，需要替换为真实数据提取
            } catch (Exception e) {
                // 忽略无法解析的数据点
            }
        }
        return values;
    }
    
    /**
     * 计算趋势
     * TODO: 实现更准确的趋势分析算法
     * - 考虑使用线性回归计算斜率
     * - 支持多种趋势模式识别（季节性、周期性等）
     * - 添加趋势强度评估
     */
    private String calculateTrend(List<Double> values) {
        if (values.size() < 2) return "insufficient_data";
        
        // TODO: 简单的首尾比较，需要改进为更robust的趋势分析
        double first = values.get(0);
        double last = values.get(values.size() - 1);
        double diff = last - first;
        
        // TODO: 阈值应该根据数据类型动态调整
        if (Math.abs(diff) < 0.1) return "stable";
        else if (diff > 0) return "increasing";
        else return "decreasing";
    }
    
    /**
     * 计算变化率
     */
    private double calculateChangeRate(List<Double> values) {
        if (values.size() < 2) return 0.0;
        
        double first = values.get(0);
        double last = values.get(values.size() - 1);
        
        if (first == 0) return 0.0;
        
        return ((last - first) / first) * 100;
    }
    
    /**
     * 根据数据类型生成建议
     * TODO: 实现更智能的建议生成系统
     * - 结合历史模式和当前趋势
     * - 考虑季节性因素
     * - 集成专家知识库
     */
    private List<String> generateRecommendations(String dataType, double avgValue, String trend) {
        List<String> recommendations = new ArrayList<>();
        
        // TODO: 当前为简化的规则引擎，需要扩展为更复杂的决策系统
        switch (dataType.toLowerCase()) {
            case "temperature":
                if (avgValue > 30) {
                    recommendations.add("温度偏高，建议开启通风设备");
                } else if (avgValue < 15) {
                    recommendations.add("温度偏低，建议开启加热设备");
                }
                if ("increasing".equals(trend)) {
                    recommendations.add("温度呈上升趋势，注意监控");
                }
                break;
            case "humidity":
                if (avgValue > 80) {
                    recommendations.add("湿度过高，建议开启除湿设备");
                } else if (avgValue < 40) {
                    recommendations.add("湿度偏低，可能需要增加湿度");
                }
                break;
            case "co2":
                if (avgValue > 2000) {
                    recommendations.add("CO2浓度过高，建议增强通风");
                }
                break;
            case "ammonia":
                if (avgValue > 25) {
                    recommendations.add("氨气浓度偏高，需要立即通风");
                }
                break;
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("当前环境参数正常，继续保持监控");
        }
        
        return recommendations;
    }
    
    /**
     * 保留指定小数位
     */
    private double roundToDecimal(double value, int places) {
        if (places < 0) throw new IllegalArgumentException();
        
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(places, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
    
    @Override
    public boolean validateParameters(JSONObject parameters) {
        try {
            String dataType = parameters.getString("data_type");
            if (dataType == null || dataType.trim().isEmpty()) {
                log.warn("data_type参数不能为空");
                return false;
            }
            
            List<String> validDataTypes = Arrays.asList("temperature", "humidity", "co2", "ammonia", "pressure");
            if (!validDataTypes.contains(dataType.toLowerCase())) {
                log.warn("无效的数据类型: {}", dataType);
                return false;
            }
            
            String timeRange = parameters.getString("time_range");
            if (timeRange != null && !timeRange.trim().isEmpty()) {
                List<String> validTimeRanges = Arrays.asList("24h", "7d", "30d");
                if (!validTimeRanges.contains(timeRange)) {
                    log.warn("无效的时间范围: {}", timeRange);
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("参数验证失败", e);
            return false;
        }
    }
    
    @Override
    public boolean checkPermission(Long userId) {
        // 历史数据查询不需要特殊权限
        return true;
    }
}