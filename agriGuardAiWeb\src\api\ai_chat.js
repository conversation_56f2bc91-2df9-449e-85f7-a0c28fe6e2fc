import axios from 'axios';
import { useAuthStore } from '@/stores/auth';

// 使用代理路径，实际请求会被代理到后端服务器
const API_BASE_URL = '/api';

const api = axios.create({
    baseURL: API_BASE_URL,
});

// 请求拦截器 - 自动添加认证token
api.interceptors.request.use(config => {
    const authStore = useAuthStore();
    const token = authStore.user?.token;
    if (token) {
        config.headers.Authorization = token;
    }
    return config;
}, error => {
    return Promise.reject(error);
});

// 响应拦截器 - 统一处理错误
api.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 401) {
            const authStore = useAuthStore();
            authStore.clearUser();
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

export default {
    /**
     * 发送聊天消息
     * @param {Object} data - 聊天数据
     * @param {string} data.message - 用户消息
     * @param {string} [data.sessionId] - 会话ID，可选
     * @param {boolean} [data.stream] - 是否流式响应，默认false
     * @param {number} [data.temperature] - AI温度参数，默认0.7
     */
    async sendMessage(data) {
        try {
            const response = await api.post('/ai/chat', {
                message: data.message,
                sessionId: data.sessionId || null,
                stream: data.stream || false,
                temperature: data.temperature || 0.7
            });
            return response.data;
        } catch (error) {
            console.error('发送AI消息失败:', error);
            throw error;
        }
    },

    /**
     * 获取会话列表
     */
    async getSessionsList() {
        try {
            const response = await api.get('/ai/sessions');
            return response.data;
        } catch (error) {
            console.error('获取会话列表失败:', error);
            throw error;
        }
    },

    /**
     * 获取会话历史记录
     * @param {string} sessionId - 会话ID
     */
    async getSessionHistory(sessionId) {
        try {
            const response = await api.get(`/ai/session/${sessionId}`);
            return response.data;
        } catch (error) {
            console.error('获取会话历史失败:', error);
            throw error;
        }
    },

    /**
     * 删除会话
     * @param {string} sessionId - 会话ID
     */
    async deleteSession(sessionId) {
        try {
            const response = await api.delete(`/ai/session/${sessionId}`);
            return response.data;
        } catch (error) {
            console.error('删除会话失败:', error);
            throw error;
        }
    },

    /**
     * AI服务健康检查
     */
    async healthCheck() {
        try {
            const response = await api.get('/ai/health');
            return response.data;
        } catch (error) {
            console.error('AI服务健康检查失败:', error);
            throw error;
        }
    },


}; 