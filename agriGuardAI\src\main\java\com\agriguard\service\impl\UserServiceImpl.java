package com.agriguard.service.impl;


import com.agriguard.dto.*;
import com.agriguard.entity.User;
import com.agriguard.constant.RoleConstants;
import com.agriguard.exception.LoginException;
import com.agriguard.exception.RegistrationException;
import com.agriguard.mapper.RoleMapper;
import com.agriguard.mapper.UserMapper;
import com.agriguard.service.UserService;
import com.agriguard.service.code.AuthCodeService;
import com.agriguard.utils.JwtTokenUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    private final UserMapper userMapper;
    private final RoleMapper roleMapper; // 新增RoleMapper依赖
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenUtil jwtTokenUtil;
    private final AuthCodeService authCodeService; // 新增验证码服务
    private final RedisTemplate<String, String> redisTemplate;

    // 新增：注册方法
    @Override
    public User register(UserRegisterDTO dto) {


        User user = new User();
        user.setUserName(dto.getUserName());
        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.setRealName(dto.getRealName());
        user.setEmail(dto.getEmail());
        user.setPhone(dto.getPhone());
        user.setCreateDate(LocalDateTime.now());

        // 新增：设置默认角色为普通用户
        userMapper.insert(user);
        // 关联默认角色（普通用户）
        userMapper.assignUserRole(user.getUserId(), RoleConstants.USER_ROLE_ID);
        user.setRoles(List.of(RoleConstants.USER_ROLE_NAME));  // 添加这行设置角色显示信息
        return user;
    }

    // 新增：登录方法
    @Override
    public UserInfoDTO login(UserLoginDTO dto) {
        // 通过账号查询用户（支持用户名/邮箱）
        User user = userMapper.selectByAccount(dto.getAccount());

        // 账户不存在校验
        if (user == null) {
            throw new LoginException("账号不存在");
        }

        // 密码校验
        if (StringUtils.isBlank(dto.getPassword()) ||
                !passwordEncoder.matches(dto.getPassword(), user.getPassword())) {
            throw new LoginException("密码错误");
        }


        user.setLoginDate(LocalDateTime.now());
        userMapper.update(user);
        // 获取用户角色（新增）
        List<String> roles = roleMapper.findRolesByUserId(user.getUserId()); // 改为使用roleMapper
        user.setRoles(roles);  // 新增设置角色字段
        user.setToken(jwtTokenUtil.generateToken(user.getUserName(), roles));

        // 通过解析旧token获取用户ID（如果存在）
        String oldToken = redisTemplate.opsForValue().get("user:" + user.getUserId());
        if (oldToken != null) {
            redisTemplate.delete(oldToken); // 删除旧token记录
        }

        // 存储新token（token为键，userId为值）
        redisTemplate.opsForValue().set(
                user.getToken(),
                user.getUserId().toString(),
                12,
                TimeUnit.HOURS
        );

        // 建立用户ID到token的反向映射
        redisTemplate.opsForValue().set(
                "user:" + user.getUserId(),
                user.getToken(),
                12,
                TimeUnit.HOURS
        );

        // 新增：返回UserInfoDTO对象
        return new UserInfoDTO(
                user.getUserId(),
                user.getUserName(),
                user.getRealName(),
                user.getEmail(),
                user.getPhone(),
                user.getLoginDate(),
                user.getToken(),
                user.getRoles()
        );
    }

    // 新增：通过邮箱登录
    @Override
    public UserInfoDTO loginWithCode(String email, String code) throws LoginException {
        // 验证码校验
        if (!authCodeService.validateCode(email, code)) {
            throw new LoginException("验证码错误或已过期");
        }

        // 查询用户信息
        User user = userMapper.findByEmail(email)
                .orElseThrow(() -> new LoginException("邮箱未注册"));

        // 更新登录时间
        user.setLoginDate(LocalDateTime.now());
        userMapper.update(user);

        // 生成JWT令牌
        List<String> roles = userMapper.findRolesByUserId(user.getUserId());
        String token = jwtTokenUtil.generateToken(user.getUserName(), roles);

        // 新增token覆盖逻辑（与login方法保持一致）
        String oldTokenKey = "user:" + user.getUserId();
        String oldToken = redisTemplate.opsForValue().get(oldTokenKey);
        if (oldToken != null) {
            redisTemplate.delete(oldToken); // 删除旧token记录
        }

        // 存储新token（token为键，userId为值）
        redisTemplate.opsForValue().set(
                token,
                user.getUserId().toString(),
                12,
                TimeUnit.HOURS
        );

        // 建立用户ID到token的反向映射
        redisTemplate.opsForValue().set(
                oldTokenKey,
                token,
                12,
                TimeUnit.HOURS
        );

        return new UserInfoDTO(
                user.getUserId(),
                user.getUserName(),
                user.getRealName(),
                user.getEmail(),
                user.getPhone(),
                user.getLoginDate(),
                token,
                roles
        );
    }

    // 新增：通过邮箱重置密码
    @Override
    public void resetPasswordByCode(ForgetPasswordDTO dto) {
        // 验证码校验
        if (!authCodeService.validateCode("FORGET_PWD:" + dto.getEmail(), dto.getCode())) {
            throw new LoginException("验证码错误或已过期");
        }

        User user = userMapper.findByEmail(dto.getEmail())
                .orElseThrow(() -> new LoginException("邮箱未注册"));

        // 加密新密码
        String encodedPwd = passwordEncoder.encode(dto.getNewPassword());

        // 调用Mapper更新密码
        userMapper.updatePassword(dto.getEmail(), encodedPwd);
    }

    // 新增：检查邮箱是否已注册
    @Override
    public boolean existsByEmail(String email) {
        // 返回布尔值而不抛出异常，将异常处理移到调用方
        return userMapper.existsByEmail(email);
    }

    // 新增：检查邮箱是否已注册
    @Override
    public void validateEmailRegistered(String email) {
        if (!existsByEmail(email)) {
            throw new RegistrationException("邮箱未注册");
        }
    }

    //账号存在性校验
    @Override
    public boolean accountExists(String account) {
        return userMapper.selectByUsername(account) != null ||
                userMapper.selectByEmail(account) != null;
    }

    //密码校验
    @Override
    public boolean verifyPassword(String account, String password) {
        // 同时校验账户和密码
        User user = userMapper.selectByAccount(account);
        return user != null && passwordEncoder.matches(password, user.getPassword());
    }

    //token校验
    @Override
    public Boolean is_expire(String token) {
        try {
            // 通过token获取用户ID
            String userId = redisTemplate.opsForValue().get(token);
            if (userId == null) return true;

            // 验证反向映射是否一致
            String currentToken = redisTemplate.opsForValue().get("user:" + userId);
            return !token.equals(currentToken);
        } catch (Exception e) {
            return true;
        }
    }
    //管理员获取所有用户列表
    @Transactional(readOnly = true)
    @Override
    public Page<UserPageDto> findAllUsersWithRoles(Pageable pageable) {
        PageHelper.startPage(pageable.getPageNumber() + 1, pageable.getPageSize());

        List<UserPageDto> users = userMapper.selectAllUsersWithRoles();
        PageInfo<UserPageDto> pageInfo = new PageInfo<>(users);

        return new PageImpl<>(
                pageInfo.getList(),
                pageable,
                pageInfo.getTotal()
        );
    }

    //管理员获取用户列表（支持搜索和筛选）
    @Transactional(readOnly = true)
    @Override
    public Page<UserPageDto> findAllUsersWithRoles(Pageable pageable, String keyword, Integer roleId) {
        PageHelper.startPage(pageable.getPageNumber() + 1, pageable.getPageSize());

        List<UserPageDto> users = userMapper.selectUsersWithRolesFiltered(keyword, roleId);
        PageInfo<UserPageDto> pageInfo = new PageInfo<>(users);

        return new PageImpl<>(
                pageInfo.getList(),
                pageable,
                pageInfo.getTotal()
        );
    }
    //管理员添加用户
    @Transactional
    @Override
    public User addUserByAdmin(UserAddByAdminDTO dto) {
        // 检查用户名和邮箱唯一性
        if (accountExists(dto.getUserName())) {
            throw new RegistrationException("用户名已存在");
        }
        if (existsByEmail(dto.getEmail())) {
            throw new RegistrationException("邮箱已被注册");
        }

        // 创建用户实体（改为传统构造方式）
        User user = new User();
        user.setUserName(dto.getUserName());
        user.setPassword(passwordEncoder.encode(dto.getPassword()));
        user.setEmail(dto.getEmail());
        user.setRealName(dto.getRealName());
        user.setPhone(dto.getPhone());
        user.setCreateDate(LocalDateTime.now());

        // 保存用户（使用userMapper代替userRepository）
        userMapper.insert(user);

        // 分配默认角色（使用现有角色分配方式）
        userMapper.assignUserRole(user.getUserId(), RoleConstants.USER_ROLE_ID); // 普通用户角色

        return user;
    }
//管理员修改用户信息
    @Transactional
    @Override
    public User updateUserByAdmin(UserUpdateByAdminDTO dto) {
        User existingUser = (User) userMapper.selectByUserId(dto.getUserId())
                .orElseThrow(() -> new LoginException("用户不存在"));

        // 更新基础信息
        existingUser.setUserName(dto.getUserName());
        existingUser.setEmail(dto.getEmail());
        existingUser.setRealName(dto.getRealName());
        existingUser.setPhone(dto.getPhone());

        // 密码更新逻辑
        if (StringUtils.isNotBlank(dto.getNewPassword())) {
            existingUser.setPassword(passwordEncoder.encode(dto.getNewPassword()));
        }

        userMapper.updatebyuserid(existingUser);
        return existingUser;
    }
//管理员删除用户
@Transactional
@Override
public void deleteUserByAdmin(Integer userId, String userName, String email) { // 修改参数类型为Integer
    User user = (User) Optional.ofNullable(userId)
            .flatMap(id -> userMapper.selectByUserId(id))
            .or(() -> {
                if (StringUtils.isNotBlank(userName)) {
                    return Optional.ofNullable(userMapper.selectByUsername(userName));
                } else if (StringUtils.isNotBlank(email)) {
                    return Optional.ofNullable(userMapper.selectByEmail(email));
                }
                return Optional.empty();
            })
            .orElseThrow(() -> new LoginException("用户不存在"));

    userMapper.deleteUserRoles(user.getUserId());
    userMapper.deleteUser(user.getUserId());
}

    @Override
    public boolean existsByEmailExcludeSelf(String email, Integer userId) {
        return userMapper.existsByEmailExcludeSelf(email, userId);
    }

    @Override
    public boolean accountExistsExcludeSelf(String account, Integer userId) {
        return userMapper.accountExistsExcludeSelf(account, userId);
    }
    @Override
    public boolean existsByUserId(Integer userId) {
        return userMapper.selectByUserId(userId).isPresent();
    }

    // 新增：获取用户角色方法实现
    @Override
    public List<String> findRolesByUserId(Integer userId) {
        return roleMapper.findRolesByUserId(userId);
    }
    
    // 角色管理功能实现
    @Transactional
    @Override
    public void updateUserRole(Integer userId, Integer roleId) {
        // 验证用户存在
        if (!existsByUserId(userId)) {
            throw new LoginException("用户不存在");
        }
        
        // 验证角色ID有效性
        if (!roleId.equals(RoleConstants.ADMIN_ROLE_ID) && !roleId.equals(RoleConstants.USER_ROLE_ID)) {
            throw new LoginException("无效的角色ID");
        }
        
        // 更新用户角色
        int updated = userMapper.updateUserRole(userId, roleId);
        if (updated == 0) {
            // 如果更新失败，可能是用户没有角色记录，先删除再插入
            userMapper.removeUserRoles(userId);
            userMapper.assignUserRole(userId, roleId);
        }
    }
    
    @Override
    public boolean isAdmin(Integer userId) {
        return userMapper.hasRole(userId, RoleConstants.ADMIN_ROLE_ID); // 检查是否有管理员角色
    }
}