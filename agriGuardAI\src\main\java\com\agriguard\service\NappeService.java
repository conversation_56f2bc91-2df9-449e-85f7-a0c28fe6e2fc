package com.agriguard.service;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.NappeRealTime;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;

import java.time.LocalDate;

public interface NappeService {

    //添加查询水帘实时状态
    String addNappeRealTime(Integer deviceId);

    //检查水帘实时状态表已有设备id
    boolean existsByDeviceId(Integer deviceId);

    // 更新查询水帘实时状态
    Result<String> updateStatus(NappeRealTime nappeRealTime);

    // 根据水帘id查询水帘详情
    Device findNappeDetailById(Integer deviceId);

    // 用于查询历史数据
    PageResult<NappeRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData);

    //用于查询最新状态
    PageResult<NappeRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName,
                                               Integer nappeStatus,String place);

}
