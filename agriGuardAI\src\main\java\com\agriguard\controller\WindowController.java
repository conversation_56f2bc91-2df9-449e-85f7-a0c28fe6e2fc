package com.agriguard.controller;

import com.agriguard.pojo.*;
import com.agriguard.service.WindowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/window")
public class WindowController {

    @Autowired
    private WindowService windowService;

    // 分页查询窗户最新状态
    @GetMapping("/list")
    public Result<PageResult<WindowRealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) Integer windowStatus,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<WindowRealTime> pageResult = windowService.findLatestStatus(pageNum, pageSize, deviceId,deviceName, windowStatus,place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!" );
        }
    }

    // 添加窗户状态，添加窗户设备id就可以
    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = windowService.addWindowRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //查询窗户详情
    @GetMapping("/detail")
    public Result<Device> getWindowDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device fanDetail = windowService.findwindowDetailById(deviceId);
            return Result.success(fanDetail);
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //修改窗户状态
    @PutMapping("/updatestatus")
    public Result<String> updateStatus(@RequestHeader String Authorization,
                                       @RequestParam Integer deviceId,
                                       @RequestParam Integer windowStatus
    ) {
        try {
            windowService.updateStatus(deviceId, windowStatus);
            return Result.success("修改成功");
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("修改失败!" );
        }
    }

}
