package com.agriguard.service.code;

import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.Date;
import java.util.Map;

@Service
@Slf4j
public class EmailService {
    @Autowired
    private JavaMailSender mailSender;
    @Autowired  // 新增模板引擎注入
    private TemplateEngine templateEngine;

    public void sendAuthCode(String toEmail, String subject, String code) {
        try {
            // 使用模板引擎渲染HTML
            Context context = new Context();
            context.setVariable("code", code);
            String htmlContent = templateEngine.process("verification-code", context);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(new InternetAddress("<EMAIL>", "AgriGuardAI 监控系统"));
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(htmlContent, true); // 使用模板生成的HTML

            mailSender.send(message);
        } catch (Exception e) {
            log.error("验证码邮件发送失败: {}", e.getMessage());
        }
    }


    public void sendAlertEmail(String to, String subject, String htmlContent) {
        // 添加发送日志
        System.out.println("正在发送邮件给: " + to);
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(new InternetAddress("<EMAIL>", "AgriGuardAI 监控系统"));
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true); // 第二个参数true表示HTML格式

            mailSender.send(message);
        } catch (Exception e) {
            log.error("报警邮件发送失败: {}", e.getMessage());
        }
    }
}