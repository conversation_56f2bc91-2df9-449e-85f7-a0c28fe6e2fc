package com.agriguard.service.impl;

import com.agriguard.mapper.WindowMapper;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.pojo.WindowRealTime;
import com.agriguard.service.CommandHandlerService;
import com.agriguard.service.DeviceService;
import com.agriguard.service.WindowService;
import com.agriguard.service.iotservice.HuaweiIotCommandService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WindowServiceImpl implements WindowService {


    @Autowired
    private WindowMapper windowMapper;
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private List<CommandHandlerService> commandHandlers; // Spring会自动注入所有实现

    @Autowired
    private HuaweiIotCommandService huaweiIotCommandService;

    // 实现分页查询窗户最新状态的方法
    @Override
    public PageResult<WindowRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, Integer windowStatus, String place) {
        //创建PageResult对象
        PageResult<WindowRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<WindowRealTime> as = windowMapper.findLatestStatus(deviceId, deviceName,windowStatus, place);
        // 将查询结果转换为 Page 对象
        Page<WindowRealTime> p = (Page<WindowRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询窗户状态成功");
        return pb;
    }

    // 判断窗户设备ID是否存在的方法
    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return windowMapper.existsByDeviceId(deviceId) > 0;
    }

    // 添加窗户状态的方法
    @Override
    public String addWindowRealTime(Integer deviceId) {
        // 设备存在性校验
        Device device = deviceService.findById(deviceId);
        if (device == null) return "设备不存在";

        // 设备类型校验
        if(!"窗户".equals(device.getType())) return "该设备不为窗户";

        // 重复性校验
        if(windowMapper.existsByDeviceId(deviceId) > 0) return "该窗户已存在";

        windowMapper.addWindowRealTime(deviceId);
        return "添加成功";
    }

    // 查询窗户详情的方法
    @Override
    public Device findwindowDetailById(Integer deviceId) {
        System.out.println("查询窗户详情成功");
        return windowMapper.findwindowDetailById(deviceId);
    }

    //修改窗户状态的方法
    @Override
    public Result<String> updateStatus(Integer deviceId, Integer windowStatus) {
        //修改档位用华为云的命令，然后再调用通用的方法
        //对命令进行解析，换成华为云的命令
        String commandName;
        //id为8,9,10,72对应自己的命令
        switch (deviceId) {
            case 8 -> {commandName = "Servo1";}
            case 9 -> {commandName = "Servo2";}
            case 10 -> {commandName = "Servo3";}
            default -> {commandName = "Servo4";}
        }
        String parasValue;
        if(windowStatus.equals(0)){
            parasValue = "OFF";
        }else{
            parasValue = "ON";
        }
        //用通用的调用华为云方法
        try {
            // 1. 发送命令到IoT平台
            if (!huaweiIotCommandService.sendCommand(commandName, parasValue)) {
                return Result.error("命令发送失败");
            }
            System.out.println("开始配置命令并存储数据库！");

            //发送成功并返回响应后的处理
            // 2. 查找合适的处理器
            CommandHandlerService handler = commandHandlers.stream()
                    .filter(h -> h.supports(commandName))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("不支持的命令类型"));

            // 3. 处理命令并更新数据库
            System.out.println(deviceId);
            return handler.handle(commandName, parasValue, deviceId);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("发送失败!");
        }
    }
}
