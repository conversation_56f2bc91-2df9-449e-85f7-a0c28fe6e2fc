<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.RegulationMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="regulationId">
        INSERT INTO regulation
            (place, alarmTypes, person_name, notice_way)
        VALUES (#{place}, #{alarmTypes}, #{personName}, #{noticeWay})
    </insert>

    <update id="update">
        UPDATE regulation
        <set>
            <if test="place != null">place = #{place},</if>
            <if test="alarmTypes != null">alarmTypes = #{alarmTypes},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="noticeWay != null">notice_way = #{noticeWay}</if>
        </set>
        WHERE regulation_id = #{regulationId}
    </update>

    <select id="selectById" resultType="com.agriguard.entity.Regulation">
        SELECT * FROM regulation WHERE regulation_id = #{regulationId}
    </select>

    <select id="selectByCondition" resultType="com.agriguard.entity.Regulation">
        SELECT * FROM regulation
        <where>
            <if test="place != null">AND place = #{place}</if>
            <if test="alarmTypes != null">AND alarmTypes = #{alarmTypes}</if>
            <if test="personName != null">AND person_name = #{personName}</if>
        </where>
    </select>
    <!-- 添加delete语句 -->
    <delete id="delete">
        DELETE FROM regulation WHERE regulation_id = #{regulationId}
    </delete>

    <select id="all_admin_name">
        SELECT
            user.user_name
        FROM
            user
                INNER JOIN
            user_role ON user.user_id = user_role.user_id
                INNER JOIN
            role ON user_role.role_id = role.role_id
        WHERE
            role.role_name = 'ADMIN';
    </select>

    <select id="getRegulationByParams" resultType="com.agriguard.entity.Regulation">
        SELECT * FROM regulation
        WHERE place = #{place}
          AND alarmTypes = #{thresholdType}
            LIMIT 1
    </select>
</mapper>
