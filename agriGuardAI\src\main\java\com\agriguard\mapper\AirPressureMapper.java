package com.agriguard.mapper;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AirPressureMapper {
    //插入气压数据
    //更新气压实时状态表
    void updateAirPressureToRealTime(Integer deviceId, BigDecimal apre, LocalDateTime updateTime);
    //插入气压值到温度缓存表
    void insertAirPressureToDataCache(Integer deviceId, BigDecimal apre, LocalDateTime updateTime);

    //分析气压缓存数据
    List<AirPressureDataHistory> getAirPressureStatsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    //添加气压历史数据
    void addAirPressureDateHistory(AirPressureDataHistory item);

    //删除指定时间段内的气压缓存数据
    void deleteAirPressureDateCacheByHour(LocalDateTime startTime, LocalDateTime endTime);

    //查询设备id气压传感器是否已存在
    int existsByDeviceId(Integer deviceId);

    //添加气压传感器
    void addAirPressureRealTime(Integer deviceId);

    //查询气压传感器详情
    Device findAirPressureDetailById(Integer deviceId);

    //分页查询历史数据
    List<AirPressureRealTime> findHistoryData(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                              @org.apache.ibatis.annotations.Param("start")LocalDateTime start,
                                              @org.apache.ibatis.annotations.Param("end")LocalDateTime end);

    //分页查询实时状态数据
    List<AirPressureRealTime> findLatestStatus(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                           @org.apache.ibatis.annotations.Param("deviceName")String deviceName,
                                           @Param("place")String place);

    //查询实时状态数据
    List<AirPressureRealTimeByMinute> getAvgAirPressure(String place);

    //获取过去24小时的历史气压数据，用于折线图展示
    List<AirPressureHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史气压数据，用于饼图展示
    List<AirPressurePie> getHistory24HoursForPie(String place);

    List<AirPressureDataCache> getAllAirPressureData();

    //获取AI预测的气压值
    BigDecimal getAiAirPressure(Integer deviceId);
}
