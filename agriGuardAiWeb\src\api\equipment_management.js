import axios from 'axios';
import {useAuthStore} from '@/stores/auth'

// 使用代理路径，实际请求会被代理到后端服务器
const API_BASE_URL = '/api';

const auth = axios.create({
    baseURL: API_BASE_URL,
});


auth.interceptors.request.use(config => {
    const authStore = useAuthStore();
    const Authorization = authStore.user?.token;
    if (Authorization) {
        config.headers.Authorization = Authorization;
    }
    return config;
}, error => {
    return Promise.reject(error);
});

export default {
    // 设备管理
    get_all_location_of_device(device_type) {
        return auth.get(`/device/get_all_location_of_device`, {
            params: {
                DeviceType: device_type
            }

        })
    },

    // 分页查询设备详情
    async find_device(pageNum, pageSize, deviceId, deviceName, type, place, model, factory, installTime) {
        return auth.get(`/device/find`, {
            params: {
                pageNum: pageNum,
                pageSize: pageSize,
                deviceId: deviceId,
                deviceName: deviceName,
                type: type,
                place: place,
                model: model,
                factory: factory,
                installTime: installTime
            }
        })
    },

    // 分页查询设备详情
    find_all_device() {
        return auth.get(`/device/findall`)
    },

    // 分页查询设备详情
    async add_device(deviceId, deviceName, type, place, model, factory, installTime, description) {
        return auth.post(`/device/add`,
            { // 将参数放在请求体中作为JSON对象
                deviceId: deviceId,
                deviceName: deviceName,
                type: type,
                place: place,
                model: model,
                factory: factory,
                installTime: installTime,
                description: description
            })
    },

    // 分页查询设备详情
    update_device(deviceId, deviceName, type, place, model, factory, installTime, description) {
        return auth.post(`/device/update`,
            {
                deviceId: deviceId,
                deviceName: deviceName,
                type: type,
                place: place,
                model: model,
                factory: factory,
                installTime: installTime,
                description: description
            })
    },

    // 分页查询设备详情
    delete_device(deviceId) {
        return auth.delete(`/device/delete`, {
            params: {
                deviceId: deviceId
            }
        })
    },


    // 风机-增
    add_fan(deviceId) {
        return auth.post(`/fan/add`,
            null,
            {
                params: {
                    deviceId: deviceId
                }
            }
        )
    },
    // 风机-查
    get_all_fan(pageNum, pageSize, deviceId, deviceName, fanStatus, place, gear) {
        return auth.get(`/fan/list`,
            {
                params: {
                    pageNum: pageNum,
                    pageSize: pageSize,
                    deviceId: deviceId,
                    deviceName: deviceName,
                    fanStatus: fanStatus,
                    place: place,
                    gear: gear
                }
            }
        )
    },
    // 风机-改
    update_fan_status(deviceId, fanStatus) {
        return auth.put(`/fan/updatestatus`,
            null,
            {
                params: {
                    deviceId: deviceId,
                    fanStatus: fanStatus,
                }
            }
        )
    },
    // 风机-改
    update_fan_gear(deviceId, gear) {
        return auth.put(`/fan/updategear`,
            null,
            {
                params: {
                    deviceId: deviceId,
                    gear: gear,
                }
            }
        )
    },
    // 风机-查
    get_fan_history(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/fan/history`,
            {
                params: {
                    pageNum: pageNum,
                    pageSize: pageSize,
                    deviceId: deviceId,
                    beginData: beginData,
                    endData: endData
                }
            }
        )
    },
    // 风机-查
    get_fan_detail(deviceId) {
        return auth.get(`/fan/detail`,
            {
                params: {
                    deviceId: deviceId,
                }
            }
        )
    },

    // 水帘-增
    add_water_curtain(deviceId) {
        return auth.post(`/nappe/add`,
            null,
            {
                params: {
                    deviceId: deviceId
                }
            }
        )
    },
// 水帘-查
    get_all_water_curtain(pageNum, pageSize, deviceId, deviceName, nappeStatus, place) {
        return auth.get(`/nappe/list`,
            {
                params: {
                    pageNum: pageNum,
                    pageSize: pageSize,
                    deviceId: deviceId,
                    deviceName: deviceName,
                    nappeStatus: nappeStatus,
                    place: place
                }
            }
        )
    },
// 水帘-改
    update_water_curtain_status(deviceId, nappeStatus) {
        return auth.put(`/nappe/updatestatus`,
            {
                deviceId: deviceId,
                nappeStatus: nappeStatus
            }
        )
    },
// 水帘-查
    get_water_curtain_history(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/nappe/history`,
            {
                params: {
                    pageNum: pageNum,
                    pageSize: pageSize,
                    deviceId: deviceId,
                    beginData: beginData,
                    endData: endData
                }
            }
        )
    },
// 水帘-查
    get_water_curtain_detail(deviceId) {
        return auth.get(`/nappe/detail`,
            {
                params: {
                    deviceId: deviceId,
                }
            }
        )
    },
    // 窗户-增
    add_window(deviceId) {
        return auth.post(`/window/add`,
            null,
            {
                params: {
                    deviceId: deviceId
                }
            }
        )
    },

// 窗户-查
    get_all_window(pageNum, pageSize, deviceId, deviceName, windowStatus, place) {
        return auth.get(`/window/list`,
            {
                params: {
                    pageNum: pageNum,
                    pageSize: pageSize,
                    deviceId: deviceId,
                    deviceName: deviceName,
                    windowStatus: windowStatus,
                    place: place
                }
            }
        )
    },

// 窗户-改
    update_window_status(deviceId, windowStatus) {
        return auth.put(`/window/updatestatus`,
            null,
            {
                params: {
                    deviceId: deviceId,
                    windowStatus: windowStatus,
                }
            }
        )
    },

// 窗户-查
    get_window_detail(deviceId) {
        return auth.get(`/window/detail`,
            {
                params: {
                    deviceId: deviceId,
                }
            }
        )
    },

    // 加热片-增
    add_heating(deviceId) {
        return auth.post(`/heat/add`,
            null,
            {
                params: {
                    deviceId: deviceId
                }
            }
        )
    },

    // 加热片-查
    get_all_heating(pageNum, pageSize, deviceId, deviceName, heatStatus, place) {
        return auth.get(`/heat/list`,{
                params: {
                    pageNum: pageNum,
                    pageSize: pageSize,
                    deviceId: deviceId,
                    deviceName: deviceName,
                    heatStatus: heatStatus,
                    place: place
                }
            }
        )
    },

    // 加热片-改
    update_heating_status(deviceId, heatStatus) {
        return auth.put(`/heat/updatestatus`,
            null,
            {
                params: {
                    deviceId: deviceId,
                    heatStatus: heatStatus,
                }
            }
        )
    },

    // 加热片-查历史
    get_heating_history(pageNum, pageSize, deviceId, beginData, endData) {
        return auth.get(`/heat/history`, {

                params: {
                    pageNum: pageNum,
                    pageSize: pageSize,
                    deviceId: deviceId,
                    beginData: beginData,
                    endData: endData
                }
            }
        )
    },

    // 加热片-查详情
    get_heating_detail(deviceId) {
        return auth.get(`/heat/detail`, {
                params: {
                    deviceId: deviceId,
                }
            }
        )
    },
//获取异常设备列表接口
    async abnormal(name, place) {
        return auth.get(`/device/abnormal`, {
            params: {
                name: name,
                place: place
            }
        })
    },
// 获取异常设备ID列表接口
    async abnormal_list(name, place) {
        return auth.get(`/device/abnormal/ids`, {
            params: {
                name: name,
                place: place
            }
        })
    },
}
