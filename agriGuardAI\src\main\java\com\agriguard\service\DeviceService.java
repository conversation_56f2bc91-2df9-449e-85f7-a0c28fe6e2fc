package com.agriguard.service;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;

import java.time.LocalDate;
import java.util.List;

public interface DeviceService {
    Device findById(Integer id);

    PageResult<Device> find(Integer pageNum, Integer pageSize,Integer deviceId, String deviceName,
                            String type, String place, String model, String factory, LocalDate installTime);

    List<Device> findAll();

    void addDevice(Device device);

    void updateDevice(Device device);

    void deleteDevice(Integer deviceId);

    List<String> get_all_location_of_device(String deviceType);

    //查新地方的设备总数
    Integer getTotalCount(String name,String place);
    //查询异常设备数量
    Integer getAbnormalCount(String name,String place);
    //查询异常设备id
    List<Integer> getAbnormalDeviceIds(String name, String place);
}
