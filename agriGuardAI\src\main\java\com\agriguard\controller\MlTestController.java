package com.agriguard.controller;

import com.agriguard.service.impl.AiCommandServiceImpl;
import com.agriguard.service.MlPredictionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/ml")
public class MlTestController {

    @Autowired
    private AiCommandServiceImpl aiCommandService;
    
    @Autowired
    private MlPredictionService mlPredictionService;

    /**
     * 测试ML预测服务健康状态
     * @return 健康状态信息
     */
    @GetMapping("/health")
    public Map<String, Object> checkMlHealth() {
        Map<String, Object> result = new HashMap<>();
        
        boolean isHealthy = mlPredictionService.checkHealth();
        result.put("ml_service_healthy", isHealthy);
        result.put("timestamp", System.currentTimeMillis());
        
        if (isHealthy) {
            result.put("message", "ML服务运行正常");
        } else {
            result.put("message", "ML服务不可用，请检查Python服务是否启动");
        }
        
        return result;
    }

    /**
     * 测试AI命令预测和解析
     * @return 预测结果
     */
    @GetMapping("/test-prediction")
    public Map<String, Object> testPrediction() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 调用AI命令服务进行预测
            aiCommandService.Aicommand();
            
            result.put("success", true);
            result.put("message", "AI预测执行成功，请查看控制台输出");
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "AI预测执行失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    /**
     * 获取当前环境参数
     * @return 环境参数
     */
    @GetMapping("/env-params")
    public Map<String, Object> getEnvironmentParams() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("temperature", aiCommandService.getAiTemp());
            result.put("humidity", aiCommandService.getAiHum());
            result.put("ammonia", aiCommandService.getAiAmm());
            result.put("co2", aiCommandService.getAiCo2());
            result.put("internal_pressure", 101.3); // 默认值
            result.put("external_pressure", 101.5); // 默认值
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            result.put("error", "获取环境参数失败: " + e.getMessage());
        }
        
        return result;
    }
} 