<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.HumidityMapper">

    <!--    模拟华为云湿度数据-->
    <!--    更新湿度实时状态表-->
    <update id="updateHumToRealTime" parameterType="com.agriguard.pojo.HumidityRealTime">
        UPDATE humidity_real_time
        SET
            hum = #{hum},
            update_time = #{updateTime}
            WHERE device_id = #{deviceId}
    </update>
    <!--    插入湿度数据至温度缓存表-->
    <insert id="insertHumToDataCache" parameterType="com.agriguard.pojo.HumidityDataCache">
        INSERT INTO humidity_data_cache (device_id, hum, update_time)
        VALUES (#{deviceId}, #{hum}, #{updateTime})
    </insert>

    <!--    分析湿度缓存数据-->
    <select id="getHumStatsByTimeRange" resultType="com.agriguard.pojo.HumidityDataHistory">
        SELECT
            device_id AS deviceId,
            MAX(hum) AS maxHum,
            MIN(hum) AS minHum,
            ROUND(AVG(hum), 1) AS avgHum,
            COUNT(*) AS dataCount
        FROM humidity_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY device_id
    </select>

    <!-- 插入湿度历史数据 -->
    <insert id="addHumDateHistory" parameterType="com.agriguard.pojo.HumidityDataHistory">
        INSERT INTO humidity_data_history (
            device_id, collect_date, collect_hour,
            max_hum, min_hum, avg_hum, data_count, update_time
        ) VALUES (
                     #{deviceId}, #{collectDate}, #{collectHour},
                     #{maxHum}, #{minHum}, #{avgHum}, #{dataCount}, #{updateTime}
                 )
    </insert>

    <!-- 清空湿度缓存表 -->
    <delete id="deleteHumDateCacheByHour">
        DELETE FROM humidity_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
    </delete>


    <!--    查看湿度设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM humidity_real_time WHERE device_id = #{deviceId}
    </select>

    <!--    增加湿度传感器实时状态-->
    <insert id="addHumRealTime" parameterType="com.agriguard.pojo.HumidityRealTime">
        INSERT INTO humidity_real_time
            (device_id, hum, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>

    <!--    查看湿度传感器详情-->
    <select id="findHumDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!--    查询历史数据-->
    <select id="findHistoryData" resultType="com.agriguard.pojo.HumidityDataHistory">
        SELECT
        device_id AS deviceId,
        collect_date AS collectDate,
        collect_hour AS collectHour,
        max_hum AS maxHum,
        min_hum AS minHum,
        avg_hum AS avgHum,
        data_count AS dataCount,
        update_time AS updateTime
        FROM humidity_data_history
        WHERE device_id = #{deviceId}
        <if test="start != null">
            AND update_time >= #{start}
        </if>
        <if test="end != null">
            AND update_time &lt; #{end}
        </if>
        ORDER BY update_time DESC
    </select>


    <!-- 查询湿度传感器状态的结果映射 -->
    <resultMap id="HumStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="hum" property="hum" jdbcType="DECIMAL"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询湿度传感器状态 -->
    <select id="findLatestStatus" resultMap="HumStatusResultMap">
        SELECT
        trt.device_id,
        d.device_name,
        d.place,
        trt.hum,
--        trt.update_time
        DATE_FORMAT(trt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        humidity_real_time trt
        JOIN
        device d ON trt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null and deviceId != ''">
            AND trt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        trt.update_time DESC
    </select>

    <!--    获取实时湿度平均值-->
    <select id="getAvgHumidity" resultType="com.agriguard.entity.device.HumidityRealTimeByMinute">
        SELECT
            CONCAT(DATE_FORMAT(tdc.update_time, '%Y-%m-%d %H:%i'), ':00') AS update_time,
            AVG(tdc.hum) AS hum
        FROM
            humidity_data_cache tdc
                JOIN
            device d ON tdc.device_id = d.device_id
        WHERE
            d.place = #{place}
          AND tdc.update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          AND tdc.update_time &lt;= NOW()
        GROUP BY
            update_time
        ORDER BY
            update_time ASC
    </select>

    <!--    获取过去24小时的温度，便于绘制折线统计图-->

    <select id="getHistory24Hours" parameterType="String" resultType="com.agriguard.entity.device.HumidityHistory24">
        WITH hour_series AS (
        SELECT
        DATE_FORMAT(DATE_SUB(NOW(), INTERVAL n HOUR), '%Y-%m-%d') AS target_date,
        HOUR(DATE_SUB(NOW(), INTERVAL n HOUR)) AS target_hour
        FROM (
        SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION
        SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION
        SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION
        SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
        ) AS numbers
        ),
        devices_in_area AS (
        SELECT device_id
        FROM device
        <if test="place != null">
            WHERE place = #{place}
        </if>
        )
        SELECT
        hs.target_date AS collect_date,
        hs.target_hour AS collect_hour,
        COALESCE(MAX(t.max_hum), 0) AS max_hum,
        COALESCE(MIN(t.min_hum), 0) AS min_hum,
        COALESCE(ROUND(AVG(t.avg_hum), 1), 0) AS avg_hum
        FROM
        hour_series hs
        LEFT JOIN (
        SELECT
        collect_date,
        collect_hour,
        max_hum,
        min_hum,
        avg_hum
        FROM humidity_data_history
        WHERE device_id IN (SELECT device_id FROM devices_in_area)
        AND update_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ) t ON DATE_FORMAT(t.collect_date, '%Y-%m-%d') = hs.target_date
        AND t.collect_hour = hs.target_hour
        GROUP BY hs.target_date, hs.target_hour
        ORDER BY hs.target_date ASC, hs.target_hour ASC
    </select>

    <!--    饼图数据获取sql-->
    <resultMap id="HumidityPieResultMap" type="com.agriguard.entity.device.HumidityPie">
        <result property="fanwei" column="fanwei"/>
        <result property="HumNum" column="HumNum"/>
    </resultMap>

    <select id="getHistory24HoursForPie" resultMap="HumidityPieResultMap">
        SELECT
            hum.fanwei,
            SUM(hum.data_count) AS HumNum
        FROM (
                 SELECT
                     hdh.data_count,
                     CASE
                         WHEN hdh.avg_hum >= 0 AND hdh.avg_hum &lt; 20 THEN 1
                         WHEN hdh.avg_hum >= 20 AND hdh.avg_hum &lt; 40 THEN 2
                         WHEN hdh.avg_hum >= 40 AND hdh.avg_hum &lt; 60 THEN 3
                         WHEN hdh.avg_hum >= 60 AND hdh.avg_hum &lt; 80 THEN 4
                         WHEN hdh.avg_hum >= 80 AND hdh.avg_hum &lt;= 100 THEN 5
                         ELSE 0
                         END AS fanwei
                 FROM
                     humidity_data_history hdh FORCE INDEX (idx_device_date)
            JOIN
            device d FORCE INDEX (PRIMARY) ON hdh.device_id = d.device_id
                 WHERE
                1=1
                <if test="place != null">
                    AND d.place = #{place}
                </if>
                   AND (
                     (hdh.collect_date = CURDATE() AND hdh.collect_hour &lt;= HOUR(NOW()))
                    OR
                     (hdh.collect_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND hdh.collect_hour &gt;= HOUR(NOW()))
                    OR
                     (hdh.collect_date &gt; DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND hdh.collect_date &lt; CURDATE())
                     )
             ) AS hum
        GROUP BY hum.fanwei
        ORDER BY hum.fanwei
    </select>

<!--    获取湿度作为Ai环境参数-->
    <select id="getAiHum" resultType="int">
    SELECT AVG(hum) AS average_humidity
    FROM humidity_real_time;
</select>

</mapper>