"""
家禽养殖场控制系统实时预测脚本

该脚本加载训练好的模型，并提供对新环境传感器数据
进行预测的接口。
"""

import pandas as pd
import numpy as np
from ml_training_pipeline import PoultryFarmMLPipeline
import json

class PoultryFarmPredictor:
    """家禽养殖场控制系统实时预测器"""

    def __init__(self, model_path='models/poultry_farm_model.pkl'):
        """使用训练好的模型初始化预测器"""
        self.pipeline = PoultryFarmMLPipeline()
        self.pipeline.load_model()
        print("✓ 模型加载成功！")

        # 控制系统状态描述
        self.state_descriptions = {
            '大风机1状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'},
            '大风机2状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'},
            '水帘状态': {0: '关闭', 1: '开启'},
            '加热片状态': {0: '关闭', 1: '开启'},
            '窗户状态': {0: '关闭', 1: '开启'},
            '小风扇1状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'},
            '小风扇2状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'}
        }
    
    def predict_single(self, temperature, humidity, ammonia, co2,
                      internal_pressure, external_pressure):
        """
        对单组环境读数进行预测

        参数:
            temperature: 温度（摄氏度）
            humidity: 湿度百分比
            ammonia: 氨气浓度
            co2: 二氧化碳浓度
            internal_pressure: 内气压
            external_pressure: 外气压

        返回:
            dict: 预测的控制系统状态及描述
        """
        # 准备输入数据
        input_data = {
            '温度': temperature,
            '湿度': humidity,
            '氨气浓度': ammonia,
            '二氧化碳浓度': co2,
            '内气压': internal_pressure,
            '外气压': external_pressure
        }

        # 进行预测
        predictions = self.pipeline.predict_new_data(input_data)

        # 添加描述
        result = {}
        for system, state in predictions.items():
            state_value = int(state)
            description = self.state_descriptions[system].get(state_value, '未知')
            result[system] = {
                'state': state_value,
                'description': description
            }

        return result
    
    def predict_batch(self, data_file):
        """
        对CSV文件中的批量环境读数进行预测

        参数:
            data_file: 包含环境数据的CSV文件路径

        返回:
            pandas.DataFrame: 包含预测结果的数据框
        """
        # 加载数据
        df = pd.read_csv(data_file)

        # 进行预测
        predictions = self.pipeline.predict_new_data(df)

        # 创建结果数据框
        results_df = df.copy()
        for system, states in predictions.items():
            results_df[f'{system}_预测'] = states

        return results_df

    def interactive_prediction(self):
        """交互式预测模式"""
        print("\n" + "="*60)
        print("家禽养殖场控制系统交互式预测")
        print("="*60)
        print("输入环境传感器读数以获取控制系统预测")
        print("输入 'quit' 退出\n")

        while True:
            try:
                print("请输入环境读数:")

                # 获取用户输入
                temp = input("温度 (°C): ")
                if temp.lower() == 'quit':
                    break
                temp = float(temp)

                humidity = float(input("湿度 (%): "))
                ammonia = float(input("氨气浓度: "))
                co2 = float(input("二氧化碳浓度: "))
                internal_p = float(input("内气压: "))
                external_p = float(input("外气压: "))

                # 进行预测
                predictions = self.predict_single(
                    temp, humidity, ammonia, co2, internal_p, external_p
                )

                # 显示结果
                print("\n" + "-"*50)
                print("预测的控制系统状态:")
                print("-"*50)

                for system, info in predictions.items():
                    print(f"{system}: {info['state']} ({info['description']})")

                print("-"*50 + "\n")

            except ValueError:
                print("❌ 输入无效。请输入数字值。")
            except KeyboardInterrupt:
                print("\n\n正在退出...")
                break
            except Exception as e:
                print(f"❌ 错误: {str(e)}")

def demo_predictions():
    """使用示例场景演示预测器"""
    predictor = PoultryFarmPredictor()

    print("\n" + "="*60)
    print("演示: 不同环境场景")
    print("="*60)

    scenarios = [
        {
            'name': '炎热夏日',
            'data': (32.0, 75.0, 20.0, 2200.0, 101.2, 101.5),
            'description': '高温高湿，氨气浓度升高'
        },
        {
            'name': '寒冷冬晨',
            'data': (15.0, 55.0, 8.0, 1200.0, 101.8, 102.0),
            'description': '低温，中等湿度，气体浓度较低'
        },
        {
            'name': '最佳条件',
            'data': (22.0, 60.0, 10.0, 1500.0, 101.5, 101.7),
            'description': '理想的温度和湿度水平'
        },
        {
            'name': '氨气高浓度警报',
            'data': (25.0, 65.0, 30.0, 1800.0, 101.3, 101.6),
            'description': '危险的氨气浓度，需要立即采取行动'
        }
    ]

    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"描述: {scenario['description']}")
        print(f"环境读数: 温度={scenario['data'][0]}°C, 湿度={scenario['data'][1]}%, "
              f"氨气={scenario['data'][2]}, 二氧化碳={scenario['data'][3]}")

        predictions = predictor.predict_single(*scenario['data'])

        print("预测的控制动作:")
        for system, info in predictions.items():
            print(f"  {system}: {info['description']} (等级 {info['state']})")
        print("-" * 60)

def main():
    """主函数"""
    print("家禽养殖场控制系统预测器")
    print("========================")

    try:
        # 运行演示
        demo_predictions()

        # 询问用户是否要使用交互模式
        choice = input("\n您想尝试交互式预测模式吗？(y/n): ")
        if choice.lower() in ['y', 'yes', '是', 'Y']:
            predictor = PoultryFarmPredictor()
            predictor.interactive_prediction()

        print("\n感谢使用家禽养殖场控制系统预测器！")

    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        print("请确保首先运行以下命令训练模型: python ml_training_pipeline.py")

if __name__ == "__main__":
    main()
