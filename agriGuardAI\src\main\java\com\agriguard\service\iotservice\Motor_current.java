package com.agriguard.service.iotservice;

import com.agriguard.mapper.ElectricityMapper;
import com.agriguard.service.IotDeviceServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

@Slf4j
@RequiredArgsConstructor
@Service
public class Motor_current implements IotDeviceServer {
    private final ElectricityMapper electricityMapper;

    /**
     * 查找设备类型
     * @param deviceType 设备类型
     * @return 是否存在
     */
    @Override
    public boolean findDeviceType(String deviceType) {
        String type = "Motor_current";
        return type.equals(deviceType);
    }

    /**
     * 先查找当天该设备有没有记录数据，有的话修改，否则添加
     * @param
     */
    @Override
    public void addData(String value){
        System.out.println("大风机电流是："+value);
        //  String testvalue = "0C1002098609970988";

        // 新增解析逻辑
        String currentData = value.replace("0C", ""); // 去除标识符
        LocalDateTime updateTime = LocalDateTime.now();

        // 分割电流值（每4位一组）
        String[] currentValues = {
                currentData.substring(0, 4),  // 1002 -> 设备64
                currentData.substring(4, 8),  // 0986 -> 设备65
                currentData.substring(8, 12), // 0970 -> 设备66
                currentData.substring(12, 16) // 988 -> 设备67（自动补全到4位）
        };

        // 设备ID映射
        int[] deviceIds = {64, 65, 66, 67};

        try {
            for (int i = 0; i < currentValues.length; i++) {
                // 转换实际电流值：字符串转BigDecimal并除以1000
                BigDecimal current = new BigDecimal(currentValues[i])
                        .divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP);

                // 更新实时表（假设ElectricityMapper已注入）
                electricityMapper.updateElectricityToRealTime(deviceIds[i], current, updateTime);
                // 插入缓存表
                electricityMapper.insertElectricityToDataCache(deviceIds[i], current, updateTime);

                log.debug("设备{}电流值: {}", deviceIds[i], current);
            }
        } catch (NumberFormatException e) {
            log.error("电流数据格式错误: {}", value, e);
            throw new IllegalArgumentException("无效的电流数据格式");
        }
    }
    }

