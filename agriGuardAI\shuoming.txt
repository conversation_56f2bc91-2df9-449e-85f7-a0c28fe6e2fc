src/main/java/com/example/iotdemo/
├── config/                // 配置类
│   └── MqttConfig.java    // MQTT 配置
├── controller/            // 控制器
│   └── TemperatureController.java // 温度数据API
├── dto/                   // 数据传输对象
│   └── TemperatureDTO.java // 温度数据DTO
├── mqtt/                  // MQTT相关
│   ├── MqttMessageListener.java // MQTT消息监听器
│   └── MqttService.java   // MQTT服务
├── service/               // 业务服务
│   └── TemperatureService.java // 温度数据处理服务

└── IotDemoApplication.java // 启动类