<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.AlarmMapper">
    <insert id="insertAlarm">
        INSERT INTO alarm (
        device_name, place, alarmTypes,
        current_value, threshold, update_time,
        processing_status, person_name
        ) VALUES (
        #{deviceName}, #{place}, #{alarmTypes},
        #{currentValue}, #{threshold}, #{updateTime},
        #{processingStatus}, #{personName}
        )
    </insert>

    <select id="getReceiversByAlarmType" resultType="java.lang.String">
        SELECT u.email
        FROM user u
                 JOIN regulation r ON u.user_name = r.person_name
        WHERE r.place = #{place}
          AND r.alarmTypes = #{alarmType}
    </select>

    <select id="selectAlarms" resultType="com.agriguard.entity.Alarm">
        SELECT * FROM alarm
        <where>
            1 = 1
            <if test="alarmTypes != null">
                AND alarmTypes = #{alarmTypes}
            </if>
            <if test="place != null">
                AND place LIKE CONCAT('%', #{place}, '%')
            </if>
            <if test="startTime != null and endTime != null">
                <!-- 移除 T 并转换为日期时间格式 -->
                AND update_time BETWEEN
                STR_TO_DATE(REPLACE(#{startTime}, 'T', ' '), '%Y-%m-%d %H:%i:%s')
                AND
                STR_TO_DATE(REPLACE(#{endTime}, 'T', ' '), '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
        ORDER BY update_time DESC
    </select>


    <update id="HandleAlarms">
        UPDATE alarm SET processing_status = '已处理' WHERE alert_id = #{alertId}
    </update>
</mapper>
