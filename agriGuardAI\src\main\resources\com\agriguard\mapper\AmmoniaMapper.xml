<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.AmmoniaMapper">

    <!--    模拟华为云氨气数据-->
    <!--    更新氨气实时状态表-->
    <update id="updateAmmoniaToRealTime" parameterType="com.agriguard.pojo.AmmoniaRealTime">
        UPDATE ammonia_real_time
        SET
            amm = #{amm},
            update_time = #{updateTime}
            WHERE device_id = #{deviceId}
    </update>
    <!--    插入氨气数据至温度缓存表-->
    <insert id="insertAmmoniaToDataCache" parameterType="com.agriguard.pojo.AmmoniaDataCache">
        INSERT INTO ammonia_data_cache (device_id, amm, update_time)
        VALUES (#{deviceId}, #{amm}, #{updateTime})
    </insert>

    <!--    分析氨气缓存数据-->
    <select id="getAmmoniaStatsByTimeRange" resultType="com.agriguard.pojo.AmmoniaDataHistory">
        SELECT
            device_id AS deviceId,
            MAX(amm) AS maxAmm,
            MIN(amm) AS minAmm,
            ROUND(AVG(amm), 1) AS avgAmm,
            COUNT(*) AS dataCount
        FROM ammonia_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY device_id
    </select>

    <!-- 插入氨气历史数据 -->
    <insert id="addAmmoniaDateHistory" parameterType="com.agriguard.pojo.AmmoniaDataHistory">
        INSERT INTO ammonia_data_history (
            device_id, collect_date, collect_hour,
            max_amm, min_amm, avg_amm, data_count, update_time
        ) VALUES (
                     #{deviceId}, #{collectDate}, #{collectHour},
                     #{maxAmm}, #{minAmm}, #{avgAmm}, #{dataCount}, #{updateTime}
                 )
    </insert>

    <!-- 清空氨气缓存表 -->
    <delete id="deleteAmmoniaDateCacheByHour">
        DELETE FROM ammonia_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
    </delete>


    <!--    查看氨气设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM ammonia_real_time WHERE device_id = #{deviceId}
    </select>

    <!--    增加氨气传感器实时状态-->
    <insert id="addAmmoniaRealTime" parameterType="com.agriguard.pojo.AmmoniaRealTime">
        INSERT INTO ammonia_real_time
            (device_id, amm, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>

    <!--    查看氨气传感器详情-->
    <select id="findAmmoniaDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!--    查询历史数据-->
    <select id="findHistoryData" resultType="com.agriguard.pojo.AmmoniaDataHistory">
        SELECT
        device_id AS deviceId,
        collect_date AS collectDate,
        collect_hour AS collectHour,
        max_amm AS maxAmm,
        min_amm AS minAmm,
        avg_amm AS avgAmm,
        data_count AS dataCount,
        update_time AS updateTime
        FROM ammonia_data_history
        WHERE device_id = #{deviceId}
        <if test="start != null">
            AND update_time >= #{start}
        </if>
        <if test="end != null">
            AND update_time &lt; #{end}
        </if>
        ORDER BY update_time DESC
    </select>


    <!-- 查询氨气传感器状态的结果映射 -->
    <resultMap id="AmmoniaStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="amm" property="amm" jdbcType="DECIMAL"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询氨气传感器状态 -->
    <select id="findLatestStatus" resultMap="AmmoniaStatusResultMap">
        SELECT
        trt.device_id,
        d.device_name,
        d.place,
        trt.amm,
--        trt.update_time
        DATE_FORMAT(trt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        ammonia_real_time trt
        JOIN
        device d ON trt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null and deviceId != ''">
            AND trt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        trt.update_time DESC
    </select>

    <!--    获取实时氨气平均值-->
    <select id="getAvgAmmonia" resultType="com.agriguard.entity.device.AmmoniaRealTimeByMinute">
        SELECT
            CONCAT(DATE_FORMAT(tdc.update_time, '%Y-%m-%d %H:%i'), ':00') AS update_time,
            AVG(tdc.amm) AS amm
        FROM
            ammonia_data_cache tdc
                JOIN
            device d ON tdc.device_id = d.device_id
        WHERE
            d.place = #{place}
          AND tdc.update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          AND tdc.update_time &lt;= NOW()
        GROUP BY
            update_time
        ORDER BY
            update_time ASC
    </select>
    <!--    获取过去24小时的Co2，便于绘制折线统计图-->

    <select id="getHistory24Hours" parameterType="String" resultType="com.agriguard.entity.device.AmmoniaHistory24">
        WITH hour_series AS (
        SELECT
        DATE_FORMAT(DATE_SUB(NOW(), INTERVAL n HOUR), '%Y-%m-%d') AS target_date,
        HOUR(DATE_SUB(NOW(), INTERVAL n HOUR)) AS target_hour
        FROM (
        SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION
        SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION
        SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION
        SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
        ) AS numbers
        ),
        devices_in_area AS (
        SELECT device_id
        FROM device
        <if test="place != null">
            WHERE place = #{place}
        </if>
        )
        SELECT
        hs.target_date AS collect_date,
        hs.target_hour AS collect_hour,
        COALESCE(MAX(t.max_amm), 0) AS max_amm,
        COALESCE(MIN(t.min_amm), 0) AS min_amm,
        COALESCE(ROUND(AVG(t.avg_amm), 1), 0) AS avg_amm
        FROM
        hour_series hs
        LEFT JOIN (
        SELECT
        collect_date,
        collect_hour,
        max_amm,
        min_amm,
        avg_amm
        FROM ammonia_data_history
        WHERE device_id IN (SELECT device_id FROM devices_in_area)
        AND update_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ) t ON DATE_FORMAT(t.collect_date, '%Y-%m-%d') = hs.target_date
        AND t.collect_hour = hs.target_hour
        GROUP BY hs.target_date, hs.target_hour
        ORDER BY hs.target_date ASC, hs.target_hour ASC
    </select>


    <!--    饼图数据获取sql-->
    <resultMap id="AmmPieResultMap" type="com.agriguard.entity.device.AmmoniaPie">
        <result property="fanwei" column="fanwei"/>
        <result property="AmmNum" column="AmmNum"/>
    </resultMap>

    <select id="getHistory24HoursForPie" resultMap="AmmPieResultMap">
        SELECT
            amm.fanwei,
            SUM(amm.data_count) AS AmmNum
        FROM (
                 SELECT
                     tdh.data_count,
                     CASE
                         WHEN tdh.avg_amm >= 0 AND tdh.avg_amm &lt; 5 THEN 1
                         WHEN tdh.avg_amm >= 5 AND tdh.avg_amm &lt; 10 THEN 2
                         WHEN tdh.avg_amm >= 10 AND tdh.avg_amm &lt; 20 THEN 3
                         WHEN tdh.avg_amm >= 20 AND tdh.avg_amm &lt; 30 THEN 4
                         WHEN tdh.avg_amm >= 30  THEN 5
                         ELSE 0
                         END AS fanwei
                 FROM
                     ammonia_data_history tdh FORCE INDEX (idx_device_date)
            JOIN
            device d FORCE INDEX (PRIMARY) ON tdh.device_id = d.device_id
                 WHERE
                     d.place = #{place}
                   AND (
                     (tdh.collect_date = CURDATE() AND tdh.collect_hour &lt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_hour &gt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date &gt; DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_date &lt; CURDATE())
                     )
             ) AS amm
        GROUP BY amm.fanwei
        ORDER BY amm.fanwei
    </select>

    <!--    获取氨气浓度作为Ai环境参数-->
    <select id="getAiAmm" resultType="int">
        SELECT AVG(amm) AS average_ammonia
        FROM ammonia_real_time;
    </select>
</mapper>