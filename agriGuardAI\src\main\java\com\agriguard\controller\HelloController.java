package com.agriguard.controller;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.security.Key;
import java.util.Date;

@RestController
public class HelloController {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Value("${jwt.secret}")
    private String secret;
    
    @GetMapping("/hello")
    public String hello() {
        return "Hello World!";
    }
    
    @GetMapping("/debug/auth")
    public String debugAuth(@RequestHeader(value = "Authorization", required = false) String authorization) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return String.format("Authorization Header: %s, Authentication: %s, Principal: %s, Authorities: %s", 
                authorization, 
                auth != null ? auth.getClass().getSimpleName() : "null",
                auth != null ? auth.getPrincipal() : "null",
                auth != null ? auth.getAuthorities() : "null");
    }
    
    @GetMapping("/debug/token")
    public String debugToken(@RequestHeader(value = "Authorization", required = false) String authorization) {
        if (authorization == null || authorization.trim().isEmpty()) {
            return "No Authorization header provided";
        }
        
        try {
            // 提取token
            String token = authorization;
            if (authorization.startsWith("Bearer ")) {
                token = authorization.substring(7);
            }
            
            // 解析token
            Key key = Keys.hmacShaKeyFor(Decoders.BASE64.decode(secret));
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            // 检查Redis中是否存在
            String userId = redisTemplate.opsForValue().get(token);
            
            return String.format("Token解析成功 - 用户: %s, 过期时间: %s, 当前时间: %s, 是否过期: %s, Redis中userId: %s", 
                    claims.getSubject(),
                    claims.getExpiration(),
                    new Date(),
                    claims.getExpiration().before(new Date()),
                    userId);
                    
        } catch (Exception e) {
            return "Token解析失败: " + e.getMessage();
        }
    }
}
