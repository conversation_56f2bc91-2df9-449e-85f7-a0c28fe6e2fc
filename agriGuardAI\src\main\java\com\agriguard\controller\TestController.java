package com.agriguard.controller;

import com.agriguard.pojo.Result;
import com.agriguard.service.iotservice.Fan_current;
import com.agriguard.service.iotservice.Gar;
import com.agriguard.service.iotservice.Motor_current;
import com.agriguard.service.iotservice.TempAndHumi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private TempAndHumi tempAndHumi;
    @Autowired
    private Gar gar;
    @Autowired
    private Motor_current motor_current;
    @Autowired
    private Fan_current fan_current;

    @PostMapping("/temp-humi")
    public Result<String> tempAndHumiAddData(@RequestParam String value) {
        try {
            tempAndHumi.addData(value);
            return Result.success("测试数据添加成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    @PostMapping("/gar")
    public Result<String> garAddData(@RequestParam String value) {
        try {
            gar.addData(value);
            return Result.success("测试数据添加成功");
        }catch (Exception e) {
            e.printStackTrace();
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    @PostMapping("/motor-current")
    public Result<String> motorCurrentAddData(@RequestParam String value) {
        try {
            motor_current.addData(value);
            return Result.success("测试数据添加成功");
        }catch (Exception e) {
            e.printStackTrace();
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    @PostMapping("/fan-current")
    public Result<String> fanCurrentAddData(@RequestParam String value) {
        try {
            fan_current.addData(value);
            return Result.success("测试数据添加成功");
        }catch (Exception e) {
            e.printStackTrace();
            return Result.error("测试失败: " + e.getMessage());
        }
    }
}