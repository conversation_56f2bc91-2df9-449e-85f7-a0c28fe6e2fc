package com.agriguard.utils;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TemperatureGenerator {
    private static final Random random = new Random();
    private static final int MIN_TEMP = 0;     // 最低温度0°C
    private static final int MAX_TEMP = 50;    // 最高温度50°C

    public static void main(String[] args) {
        startTemperatureGeneration();
    }

    /**
     * 开始每隔30秒生成一个随机温度值
     */
    public static void startTemperatureGeneration() {
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();

        // 立即执行一次，然后每隔30秒执行一次
        executor.scheduleAtFixedRate(() -> {
            int temperature = generateTemperature();
            System.out.println("生成的温度: " + temperature);
            // 这里可以添加你的业务逻辑，例如将温度发送到某个服务
        }, 0, 30, TimeUnit.SECONDS);

        // 注意：在实际应用中，应该在适当的时机调用executor.shutdown()来停止定时任务
    }

    /**
     * 生成一个随机温度值，格式为整数表示的温度值（例如23.5°C表示为235）
     * @return 整数表示的温度值
     */
    public static int generateTemperature() {
        // 生成0.0到50.0之间的随机温度
        double temp = MIN_TEMP + random.nextDouble() * (MAX_TEMP - MIN_TEMP);

        // 将温度乘以10并转换为整数，去掉小数部分
        int intTemp = (int) (temp * 10);
//        System.out.println(intTemp);

        return intTemp;
    }
}
