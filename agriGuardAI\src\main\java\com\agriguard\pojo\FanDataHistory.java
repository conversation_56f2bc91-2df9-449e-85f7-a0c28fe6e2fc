package com.agriguard.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigInteger;
import java.time.LocalDateTime;

@Data
public class FanDataHistory {
    private BigInteger id;
    private Integer deviceId;
    private Integer fanStatus;
    private Byte gear;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
