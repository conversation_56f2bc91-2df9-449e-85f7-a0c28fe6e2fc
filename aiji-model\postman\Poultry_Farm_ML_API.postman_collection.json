{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "家禽养殖场机器学习预测API", "description": "基于机器学习的家禽养殖场环境控制系统预测服务API测试集合\n\n包含以下测试用例：\n- 健康检查\n- 单次预测（正常情况）\n- 单次预测（参数错误）\n- 批量预测\n- 错误处理测试\n\n使用前请确保API服务已启动在 http://localhost:5000", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "健康检查", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 验证响应时间", "pm.test(\"响应时间应小于1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 验证响应内容", "pm.test(\"响应应包含健康状态信息\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('service');", "    pm.expect(jsonData).to.have.property('version');", "    pm.expect(jsonData).to.have.property('model_loaded');", "    pm.expect(jsonData.status).to.eql('healthy');", "    pm.expect(jsonData.service).to.eql('poultry-farm-ml-service');", "    pm.expect(jsonData.model_loaded).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "单次预测 - 正常情况", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 验证响应时间", "pm.test(\"响应时间应小于500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});", "", "// 验证响应内容", "pm.test(\"响应应包含预测结果\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('timestamp');", "    pm.expect(jsonData).to.have.property('processing_time_ms');", "    pm.expect(jsonData).to.have.property('predictions');", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.predictions).to.be.an('object');", "});", "", "// 验证预测结果结构", "pm.test(\"预测结果应包含7个控制系统\", function () {", "    var jsonData = pm.response.json();", "    var predictions = jsonData.predictions;", "    pm.expect(Object.keys(predictions)).to.have.lengthOf(7);", "    ", "    // 验证每个预测结果的结构", "    Object.values(predictions).forEach(function(prediction) {", "        pm.expect(prediction).to.have.property('state');", "        pm.expect(prediction).to.have.property('description');", "        pm.expect(prediction.state).to.be.a('number');", "        pm.expect(prediction.description).to.be.a('string');", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"temperature\": 25.0,\n    \"humidity\": 60.0,\n    \"ammonia\": 15.0,\n    \"co2\": 1500.0,\n    \"internal_pressure\": 101.3,\n    \"external_pressure\": 101.5\n}"}, "url": {"raw": "{{base_url}}/predict", "host": ["{{base_url}}"], "path": ["predict"]}}, "response": []}, {"name": "单次预测 - 参数超出范围", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为400\", function () {", "    pm.response.to.have.status(400);", "});", "", "// 验证错误响应内容", "pm.test(\"响应应包含错误信息\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('error');", "    pm.expect(jsonData.success).to.be.false;", "    pm.expect(jsonData.error).to.be.a('string');", "    pm.expect(jsonData.error).to.include('超出有效范围');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"temperature\": 100.0,\n    \"humidity\": 60.0,\n    \"ammonia\": 15.0,\n    \"co2\": 1500.0,\n    \"internal_pressure\": 101.3,\n    \"external_pressure\": 101.5\n}"}, "url": {"raw": "{{base_url}}/predict", "host": ["{{base_url}}"], "path": ["predict"]}}, "response": []}, {"name": "单次预测 - 缺少参数", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为400\", function () {", "    pm.response.to.have.status(400);", "});", "", "// 验证错误响应内容", "pm.test(\"响应应包含缺少参数的错误信息\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('error');", "    pm.expect(jsonData.success).to.be.false;", "    pm.expect(jsonData.error).to.be.a('string');", "    pm.expect(jsonData.error).to.include('缺少必需参数');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"temperature\": 25.0,\n    \"humidity\": 60.0\n}"}, "url": {"raw": "{{base_url}}/predict", "host": ["{{base_url}}"], "path": ["predict"]}}, "response": []}, {"name": "批量预测 - 正常情况", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 验证响应时间", "pm.test(\"响应时间应小于1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 验证批量预测响应内容", "pm.test(\"响应应包含批量预测结果\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('results');", "    pm.expect(jsonData).to.have.property('total_samples');", "    pm.expect(jsonData).to.have.property('successful_predictions');", "    pm.expect(jsonData).to.have.property('failed_predictions');", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.total_samples).to.eql(2);", "    pm.expect(jsonData.successful_predictions).to.eql(2);", "    pm.expect(jsonData.failed_predictions).to.eql(0);", "});", "", "// 验证批量结果结构", "pm.test(\"批量结果应包含正确的预测数据\", function () {", "    var jsonData = pm.response.json();", "    var results = jsonData.results;", "    pm.expect(results).to.be.an('array');", "    pm.expect(results).to.have.lengthOf(2);", "    ", "    results.forEach(function(result, index) {", "        pm.expect(result).to.have.property('success');", "        pm.expect(result).to.have.property('sample_index');", "        pm.expect(result).to.have.property('predictions');", "        pm.expect(result.success).to.be.true;", "        pm.expect(result.sample_index).to.eql(index);", "        pm.expect(Object.keys(result.predictions)).to.have.lengthOf(7);", "    });", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"samples\": [\n        {\n            \"temperature\": 25.0,\n            \"humidity\": 60.0,\n            \"ammonia\": 15.0,\n            \"co2\": 1500.0,\n            \"internal_pressure\": 101.3,\n            \"external_pressure\": 101.5\n        },\n        {\n            \"temperature\": 30.0,\n            \"humidity\": 70.0,\n            \"ammonia\": 20.0,\n            \"co2\": 2000.0,\n            \"internal_pressure\": 101.0,\n            \"external_pressure\": 101.2\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/batch-predict", "host": ["{{base_url}}"], "path": ["batch-predict"]}}, "response": []}, {"name": "批量预测 - 超过限制", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为400\", function () {", "    pm.response.to.have.status(400);", "});", "", "// 验证错误响应内容", "pm.test(\"响应应包含样本数量限制错误信息\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('error');", "    pm.expect(jsonData.success).to.be.false;", "    pm.expect(jsonData.error).to.include('样本数量不能超过100');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"samples\": []\n}"}, "url": {"raw": "{{base_url}}/batch-predict", "host": ["{{base_url}}"], "path": ["batch-predict"]}}, "response": []}, {"name": "API文档", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 验证响应内容类型", "pm.test(\"响应应为HTML格式\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('text/html');", "});", "", "// 验证HTML内容", "pm.test(\"HTML应包含API文档内容\", function () {", "    var responseText = pm.response.text();", "    pm.expect(responseText).to.include('家禽养殖场机器学习预测API');", "    pm.expect(responseText).to.include('/health');", "    pm.expect(responseText).to.include('/predict');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/docs", "host": ["{{base_url}}"], "path": ["docs"]}}, "response": []}, {"name": "404错误测试", "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态码", "pm.test(\"状态码应为404\", function () {", "    pm.response.to.have.status(404);", "});", "", "// 验证错误响应内容", "pm.test(\"响应应包含404错误信息\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('error');", "    pm.expect(jsonData).to.have.property('available_endpoints');", "    pm.expect(jsonData.success).to.be.false;", "    pm.expect(jsonData.error).to.eql('接口不存在');", "    pm.expect(jsonData.available_endpoints).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/nonexistent", "host": ["{{base_url}}"], "path": ["nonexistent"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}]}