package com.agriguard.service.impl;

import com.agriguard.entity.device.*;
import com.agriguard.mapper.ElectricityMapper;
import com.agriguard.pojo.*;
import com.agriguard.service.DeviceService;
import com.agriguard.service.ElectricityService;
import com.agriguard.utils.DateTimeUtil;
import com.agriguard.utils.TemperatureGenerator;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ElectricityServiceImpl implements ElectricityService {
    @Autowired
    private ElectricityMapper electricityMapper;
    @Autowired
    private DeviceService deviceService;

    @Override
    public void insertTemperature() {
        //默认电流传感器设备编号为1
        int deviceId = 1;
        //模拟生成华为云电流数据
        BigDecimal ele = BigDecimal.valueOf((TemperatureGenerator.generateTemperature())/10);
        System.out.println("生成的电流: " + ele);
        //生成数据更新时间
        LocalDateTime updateTime = java.time.LocalDateTime.now();
        //将电流实时状态表进行更新,实时状态表就不进行时间更新了，数据库会自己进行更新
        electricityMapper.updateElectricityToRealTime(deviceId,ele,updateTime);
        //将电流数据插入温度缓存表
        electricityMapper.insertElectricityToDataCache(deviceId,ele,updateTime);
    }

    //定时对电流数据进行分析，存储历史数据，并进行数据删除
    @Override
    public void ElectricityDateSys() {

        //获取当前时间
        String[] hourRange = DateTimeUtil.getPreviousHourRange();
        System.out.println("上一个小时范围:");
        System.out.println("开始时间: " + hourRange[0]);
        System.out.println("结束时间: " + hourRange[1]);
        //将String类型的时间转换为LocalDateTime类型
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将字符串转换为LocalDateTime
        LocalDateTime startTime = LocalDateTime.parse(hourRange[0], formatter);
        LocalDateTime endTime = LocalDateTime.parse(hourRange[1], formatter);
        // 调用Mapper方法获取数据
        List<ElectricityDataHistory> result = electricityMapper.getElectricityStatsByTimeRange(startTime, endTime);

        //h获取小时数
        int hour = startTime.getHour();
        // 设置插入时间为当前时间
        LocalDateTime now = LocalDateTime.now();
//        // 从now获取日期部分，精确到天
//        LocalDate currentDate = now.toLocalDate();
        // 从startTime获取日期部分，精确到天
        LocalDate currentDate = startTime.toLocalDate();
        for (ElectricityDataHistory item : result) {
            item.setCollectHour(hour);
            item.setUpdateTime(now);
            item.setCollectDate(currentDate);
        }
        System.out.println("执行了电流缓存表的定时任务！");

        // 插入数据到历史表
        for (ElectricityDataHistory item : result) {
            electricityMapper.addElectricityDateHistory(item);
        }

        //删除缓存表中的数据
        electricityMapper.deleteElectricityDateCacheByHour(startTime,endTime);
    }

    //查询设备id电流传感器是否已存在
    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return electricityMapper.existsByDeviceId(deviceId) > 0;
    }

    @Override
    public String addElectricityRealTime(Integer deviceId) {
        // 设备存在性校验
        Device device = deviceService.findById(deviceId);
        if (device == null) return "设备不存在";

        // 设备类型校验
        if(!"电流传感器".equals(device.getType())) return "该设备不为电流传感器";

        // 重复性校验
        if(electricityMapper.existsByDeviceId(deviceId) > 0) return "该电流传感器已存在";

        electricityMapper.addElectricityRealTime(deviceId);
        return "添加成功";
    }

    //查询电流传感器详情
    @Override
    public Device findElectricityDetailById(Integer deviceId) {
        System.out.println("查询电流传感器详情成功");
        return electricityMapper.findElectricityDetailById(deviceId);
    }

    public PageResult<ElectricityRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        //创建PageResult对象
        PageResult<ElectricityRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 转换日期范围
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        // 调用 mapper 进行查询
        List<ElectricityRealTime> as = electricityMapper.findHistoryData(deviceId, start, end);
        // 将查询结果转换为 Page 对象
        Page<ElectricityRealTime> p = (Page<ElectricityRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询电流历史数据成功");
        return pb;
    }


    //分页查询实时状态数据
    @Override
    public PageResult<ElectricityRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, String place) {
        //创建PageResult对象
        PageResult<ElectricityRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<ElectricityRealTime> as = electricityMapper.findLatestStatus(deviceId, deviceName, place);
        // 将查询结果转换为 Page 对象
        Page<ElectricityRealTime> p = (Page<ElectricityRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询电流状态成功");
        return pb;
    }

    //查询实时状态数据
    @Override
    public List<ElectricityRealTimeByMinute> getAvgElectricity(String place) {
        System.out.println(place);
        return electricityMapper.getAvgElectricity(place);
    }

    //获取过去24小时的历史电流数据，用于折线图展示
    @Override
    public List<ElectricityHistory24> getHistory24Hours(String place) {
        return electricityMapper.getHistory24Hours(place);
    }

    //获取过去24小时的历史电流数据，用于饼图展示
    @Override
    public List<ElectricityPie> getHistory24HoursForPie(String place) {
        return electricityMapper.getHistory24HoursForPie(place);
    }
}
