package com.agriguard.mapper;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.WindowRealTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WindowMapper {

    // 分页查询窗户最新状态
    List<WindowRealTime> findLatestStatus(@Param("deviceId") Integer deviceId,
                                          @Param("deviceName") String deviceName,
                                          @Param("windowStatus") Integer windowStatus,
                                          @Param("place") String place);

    //判断窗户设备ID是否存在
    int existsByDeviceId(Integer deviceId);

    // 添加窗户状态，添加窗户设备id就可以
    void addWindowRealTime(Integer deviceId);

    //查询窗户详情
    Device findwindowDetailById(Integer deviceId);

    //修改窗户状态
    void updateStatus(Integer deviceId, Integer windowStatus);

    //命令操作
    void ordercommand(Integer deviceId, Integer windowStatus);

    //判断Ai命令是否已存在
    Integer windowAiCommand(Integer deviceId);
}
