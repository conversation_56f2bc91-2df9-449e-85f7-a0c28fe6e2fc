package com.agriguard.controller;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import com.agriguard.service.ElectricityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/electricity")
public class ElectricityController {
    @Autowired
    private ElectricityService electricityService;

    // 使用Cron表达式配置每小时执行一次的定时任务
    //每个小时执行一次
//    @Scheduled(cron = "0 0 * * * ?")
    //使用每两个小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ?  ")
    public void scheduledTask() {
        try {
            // 调用Service中的业务方法
            electricityService.ElectricityDateSys();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = electricityService.addElectricityRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //获取电流传感器详情
    @GetMapping("/detail")
    public Result<Device> getAmmoniaDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device eleDetail = electricityService.findElectricityDetailById(deviceId);
            return Result.success(eleDetail);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //分页查询电流历史状态
    @GetMapping("/history")
    public Result<PageResult<ElectricityRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData
    ) {
        try {
            PageResult<ElectricityRealTime> pageResult = electricityService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //多条件分页查询电流传感器状态
    @GetMapping("/list")
    public Result<PageResult<ElectricityRealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<ElectricityRealTime> pageResult = electricityService.findLatestStatus(pageNum, pageSize, deviceId, deviceName, place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //获取每个分钟的平均电流用于实时电流变化展示，根据位置来查询--某个位置的
    @GetMapping("/avg")
    public Result<List<ElectricityRealTimeByMinute>> getAvgAirPressure(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<ElectricityRealTimeByMinute> avgEle = electricityService.getAvgElectricity(place);
            return Result.success(avgEle);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
    //获取过去24小时的历史电流数据，用于折线图展示
    @GetMapping("/history24")
    public Result<List<ElectricityHistory24>> getHistory24Hours(@RequestHeader String Authorization, @RequestParam String place){
        try {
            List<ElectricityHistory24> history24Hours = electricityService.getHistory24Hours(place);
            return Result.success(history24Hours);  // 返回成功响应
        }
        catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史电流数据，用于饼图范围展示
    @GetMapping("/history24pie")
    public Result<List<ElectricityPie>> getHistory24HoursForPie(@RequestHeader String Authorization, @RequestParam String place){
        try {
            List<ElectricityPie> ElectricityPie = electricityService.getHistory24HoursForPie(place);
            return Result.success(ElectricityPie);  // 返回成功响应

        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
}
