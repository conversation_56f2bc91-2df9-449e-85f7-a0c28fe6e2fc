package com.agriguard.service.iotservice;

import com.agriguard.entity.Alarm;
import com.agriguard.mapper.AlarmMapper;
import com.agriguard.pojo.PageResult;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.apache.tomcat.util.http.parser.Authorization;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestHeader;

import java.time.LocalDateTime;


@Service
@RequiredArgsConstructor
public class AlarmService {
    private final AlarmMapper alarmMapper;
    public PageResult<Alarm> getAlarms(String alarmTypes, String place, LocalDateTime startTime,
                                       LocalDateTime endTime, Pageable pageable) {
        // 使用PageHelper启动分页（页码+1适配PageHelper从1开始的特性）
        Page<Alarm> page = PageHelper.startPage(pageable.getPageNumber() + 1, pageable.getPageSize())
                .doSelectPage(() -> alarmMapper.selectAlarms(
                        alarmTypes,
                        place,
                        startTime != null ? startTime.toString() : null,
                        endTime != null ? endTime.toString() : null
                ));
        alarmMapper.selectAlarms(alarmTypes,place,null,null);

        // 根据PageResult现有结构设置返回值
        PageResult<Alarm> result = new PageResult<>();
        result.setRows(page.getResult());  // 设置当前页数据
        result.setTotal(page.getTotal());  // 设置总记录数
        return result;
    }

    public String HandleAlarms(String alertId) {
        alarmMapper.HandleAlarms(alertId);
        return "alertId被处理"+alertId;
    }
}
