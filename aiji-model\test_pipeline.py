"""
家禽养殖场机器学习管道测试脚本

该脚本使用较小的数据子集测试机器学习管道的基本功能，
以便快速验证。
"""

import pandas as pd
import numpy as np
from ml_training_pipeline import PoultryFarmMLPipeline

def test_pipeline_basic():
    """测试管道基本功能"""
    print("正在测试家禽养殖场机器学习管道...")

    # 初始化管道
    pipeline = PoultryFarmMLPipeline()

    # 加载数据
    df = pipeline.load_data()
    print(f"✓ 数据加载成功: {df.shape}")

    # 使用较小的子集进行快速测试
    pipeline.df = df.head(1000)  # 仅使用前1000行进行测试
    print(f"✓ 使用数据子集: {pipeline.df.shape}")

    # 预处理数据
    X_train, X_test, y_train, y_test = pipeline.preprocess_data()
    print(f"✓ 数据预处理完成")
    print(f"  训练集: {X_train.shape}")
    print(f"  测试集: {X_test.shape}")

    # 训练模型
    model = pipeline.train_model(n_estimators=10)  # 使用较少的树以加快测试速度
    print(f"✓ 模型训练完成")

    # 评估模型
    results = pipeline.evaluate_model()
    print(f"✓ 模型评估完成")
    print(f"  总体测试准确率: {results['overall_test_acc']:.4f}")

    # 测试新数据预测
    example_data = {
        '温度': 25.0,
        '湿度': 60.0,
        '氨气浓度': 15.0,
        '二氧化碳浓度': 1500.0,
        '内气压': 101.5,
        '外气压': 101.8
    }

    predictions = pipeline.predict_new_data(example_data)
    print(f"✓ 预测测试完成")
    print("  示例预测:", predictions)

    # 保存模型
    pipeline.save_model()
    print(f"✓ 模型保存成功")

    print("\n🎉 所有测试通过！管道运行正常。")
    return True

def test_model_loading():
    """测试加载保存的模型"""
    print("\n正在测试模型加载...")

    # 创建新的管道实例
    new_pipeline = PoultryFarmMLPipeline()

    # 加载保存的模型
    model, scaler = new_pipeline.load_model()
    print("✓ 模型加载成功")

    # 使用加载的模型测试预测
    example_data = {
        '温度': 20.0,
        '湿度': 55.0,
        '氨气浓度': 12.0,
        '二氧化碳浓度': 1200.0,
        '内气压': 101.0,
        '外气压': 101.3
    }

    predictions = new_pipeline.predict_new_data(example_data)
    print("✓ 使用加载的模型预测成功")
    print("  预测结果:", predictions)

    return True

if __name__ == "__main__":
    try:
        # 运行基本管道测试
        test_pipeline_basic()

        # 测试模型加载
        test_model_loading()

        print("\n✅ 所有测试成功完成！")

    except Exception as e:
        print(f"\n❌ 测试失败，错误: {str(e)}")
        import traceback
        traceback.print_exc()
