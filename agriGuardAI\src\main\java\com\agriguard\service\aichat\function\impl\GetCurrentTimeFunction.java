package com.agriguard.service.aichat.function.impl;

import com.agriguard.service.aichat.function.FunctionDefinition;
import com.agriguard.service.aichat.function.FunctionExecutor;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取当前时间的工具函数
 * 示例工具函数，用于测试Function Calling功能
 */
@Slf4j
@Component
public class GetCurrentTimeFunction implements FunctionExecutor {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public FunctionDefinition getFunctionDefinition() {
        // 定义参数Schema
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // 时区参数（可选）
        Map<String, Object> timezoneParam = new HashMap<>();
        timezoneParam.put("type", "string");
        timezoneParam.put("description", "时区，例如：Asia/Shanghai, UTC等，默认为系统时区");
        timezoneParam.put("default", "Asia/Shanghai");
        properties.put("timezone", timezoneParam);
        
        // 格式参数（可选）
        Map<String, Object> formatParam = new HashMap<>();
        formatParam.put("type", "string");
        formatParam.put("description", "时间格式，例如：yyyy-MM-dd HH:mm:ss");
        formatParam.put("default", "yyyy-MM-dd HH:mm:ss");
        properties.put("format", formatParam);
        
        parameters.put("properties", properties);
        parameters.put("required", new String[]{}); // 没有必需参数
        
        return FunctionDefinition.builder()
                .name("get_current_time")
                .description("获取当前系统时间，可以指定时区和格式")
                .parameters(parameters)
                .requiresAuth(false)
                .permissionLevel("read")
                .requiresConfirmation(false)
                .build();
    }
    
    @Override
    public Object execute(JSONObject parameters, Long userId) throws Exception {
        try {
            // 获取参数
            String timezone = parameters.getString("timezone");
            String format = parameters.getString("format");
            
            // 使用默认值
            if (timezone == null || timezone.trim().isEmpty()) {
                timezone = "Asia/Shanghai";
            }
            if (format == null || format.trim().isEmpty()) {
                format = "yyyy-MM-dd HH:mm:ss";
            }
            
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            
            // 格式化时间
            DateTimeFormatter formatter;
            try {
                formatter = DateTimeFormatter.ofPattern(format);
            } catch (Exception e) {
                // 如果格式无效，使用默认格式
                formatter = FORMATTER;
                format = "yyyy-MM-dd HH:mm:ss";
            }
            
            String formattedTime = now.format(formatter);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("current_time", formattedTime);
            result.put("timezone", timezone);
            result.put("format", format);
            result.put("timestamp", System.currentTimeMillis());
            result.put("iso_format", now.toString());
            
            log.info("获取当前时间: {} (用户ID: {})", formattedTime, userId);
            
            return result;
            
        } catch (Exception e) {
            log.error("获取当前时间失败", e);
            throw new Exception("获取当前时间失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean validateParameters(JSONObject parameters) {
        // 参数验证（这个函数的参数都是可选的，所以总是返回true）
        return true;
    }
    
    @Override
    public boolean checkPermission(Long userId) {
        // 获取时间不需要特殊权限
        return true;
    }
}
