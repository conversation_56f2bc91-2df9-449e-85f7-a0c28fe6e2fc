package com.agriguard.mapper;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AmmoniaMapper {
    //插入氨气数据
    //更新氨气实时状态表
    void updateAmmoniaToRealTime(Integer deviceId, Integer amm,LocalDateTime updateTime);
    //插入氨气值到温度缓存表
    void insertAmmoniaToDataCache(Integer deviceId, Integer amm, LocalDateTime updateTime);

    //分析氨气缓存数据
    List<AmmoniaDataHistory> getAmmoniaStatsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    //添加氨气历史数据
    void addAmmoniaDateHistory(AmmoniaDataHistory item);

    //删除指定时间段内的氨气缓存数据
    void deleteAmmoniaDateCacheByHour(LocalDateTime startTime, LocalDateTime endTime);

    //查询设备id氨气传感器是否已存在
    int existsByDeviceId(Integer deviceId);

    //添加氨气传感器
    void addAmmoniaRealTime(Integer deviceId);

    //查询氨气传感器详情
    Device findAmmoniaDetailById(Integer deviceId);

    //分页查询历史数据
    List<AmmoniaRealTime> findHistoryData(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                          @org.apache.ibatis.annotations.Param("start")LocalDateTime start,
                                          @org.apache.ibatis.annotations.Param("end")LocalDateTime end);

    //分页查询实时状态数据
    List<AmmoniaRealTime> findLatestStatus(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                       @org.apache.ibatis.annotations.Param("deviceName")String deviceName,
                                       @Param("place")String place);

    //查询实时状态数据
    List<AmmoniaRealTimeByMinute> getAvgAmmonia(String place);

    //获取过去24小时的历史氨气数据，用于折线图展示
    List<AmmoniaHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史氨气数据，用于饼图展示
    List<AmmoniaPie> getHistory24HoursForPie(String place);

    List<AmmoniaDataCache> getAllAmmoniaData();

    //获取氨气作为Ai环境参数
    Integer getAiAmm();
}
