<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.NappeMapper">

<!--    &lt;!&ndash; 实时状态查询 &ndash;&gt;-->
<!--    <select id="findLatestStatus" resultType="com.agriguard.pojo.NappeRealTime">-->
<!--        SELECT * FROM nappe_real_time-->
<!--        <where>-->
<!--            <if test="deviceId != null">AND device_id = #{deviceId}</if>-->
<!--            <if test="nappeStatus != null">AND nappe_status = #{nappeStatus}</if>-->
<!--        </where>-->
<!--        ORDER BY update_time DESC   &lt;!&ndash; 按更新时间降序排序 &ndash;&gt;-->
<!--    </select>-->

    <!-- 查询水帘状态的结果映射 -->
    <resultMap id="NappeStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="nappe_status" property="nappeStatus" jdbcType="TINYINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询水帘状态 -->
    <select id="findLatestStatus" resultMap="NappeStatusResultMap">
        SELECT
        nrt.device_id,
        d.device_name,
        d.place,
        nrt.nappe_status,
     --   nrt.update_time
        DATE_FORMAT(nrt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        nappe_real_time nrt
        JOIN
        device d ON nrt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null">
            AND nrt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name = #{deviceName}
        </if>
        <if test="nappeStatus != null">
            AND nrt.nappe_status = #{nappeStatus}
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        nrt.update_time DESC
    </select>

    <!-- 新增实时状态 -->
    <insert id="addNappeRealTime">
        INSERT INTO nappe_real_time
            (device_id, nappe_status)
        VALUES
            (#{deviceId}, 0)
    </insert>

<!--    判断设备是否已存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM nappe_real_time WHERE device_id = #{deviceId}
    </select>

<!--更新实时状态-->
    <update id="updateStatus">
        UPDATE nappe_real_time
        SET
            nappe_status = #{nappeStatus},
            update_time = #{updateTime}
        WHERE device_id = #{deviceId}
    </update>

    <!-- 添加历史记录 -->
    <insert id="addNappeDataHistory">
        INSERT INTO nappe_data_history
            (device_id, nappe_status,update_time)
        VALUES
            (#{deviceId}, #{nappeStatus}, #{updateTime})
    </insert>

    <!-- 设备详情查询 -->
    <select id="findNappeDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!-- 历史数据查询 -->
    <select id="findHistoryData" resultType="com.agriguard.pojo.NappeRealTime">
        SELECT
        h.device_id AS deviceId,
        h.nappe_status AS nappeStatus,
        h.update_time AS updateTime
        FROM nappe_data_history h
        INNER JOIN device d ON h.device_id = d.device_id
        WHERE h.device_id = #{deviceId}
        <if test="start != null and end != null">
            AND h.update_time BETWEEN #{start} AND #{end}
        </if>
        ORDER BY h.update_time DESC
    </select>

<!--    Ai命令是否已存在-->
    <select id="nappeAiCommand" resultType="int">
            select nappe_status from nappe_real_time where device_id = #{deviceId}
    </select>

    <update id="ordercommand" parameterType="com.agriguard.pojo.NappeRealTime">
        UPDATE nappe_real_time
        SET
            nappe_status = #{nappeStatus}
        WHERE device_id = #{deviceId}
    </update>
</mapper>
