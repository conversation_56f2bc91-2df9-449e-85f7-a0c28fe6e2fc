{"name": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@kjgl77/datav-vue3": "^1.7.4", "axios": "^1.9.0", "chart": "^0.1.2", "echarts": "^5.6.0", "element-plus": "^2.10.1", "highlight.js": "^11.11.1", "marked": "^16.0.0", "pinia": "^3.0.3", "sweetalert2": "^11.22.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}