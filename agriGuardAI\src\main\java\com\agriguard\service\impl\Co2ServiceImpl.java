package com.agriguard.service.impl;

import com.agriguard.entity.device.*;
import com.agriguard.mapper.Co2Mapper;
import com.agriguard.pojo.*;
import com.agriguard.service.Co2Service;
import com.agriguard.service.DeviceService;
import com.agriguard.utils.DateTimeUtil;
import com.agriguard.utils.TemperatureGenerator;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class Co2ServiceImpl implements Co2Service {
    @Autowired
    private Co2Mapper co2Mapper;
    @Autowired
    private DeviceService deviceService;

    @Override
    public void insertTemperature() {
        //默认Co2传感器设备编号为1
        int deviceId = 1;
        //模拟生成华为云Co2数据
        Integer co2 = Integer.valueOf((TemperatureGenerator.generateTemperature())/10);
        System.out.println("生成的Co2: " + co2);
        //生成数据更新时间
        LocalDateTime updateTime = java.time.LocalDateTime.now();
        //将Co2实时状态表进行更新,实时状态表就不进行时间更新了，数据库会自己进行更新
        co2Mapper.updateCo2ToRealTime(deviceId,co2,updateTime);
        //将Co2数据插入温度缓存表
        co2Mapper.insertCo2ToDataCache(deviceId,co2,updateTime);
    }

    //定时对Co2数据进行分析，存储历史数据，并进行数据删除
    @Override
    public void Co2DateSys() {

        //获取当前时间
        String[] hourRange = DateTimeUtil.getPreviousHourRange();
        System.out.println("上一个小时范围:");
        System.out.println("开始时间: " + hourRange[0]);
        System.out.println("结束时间: " + hourRange[1]);
        //将String类型的时间转换为LocalDateTime类型
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将字符串转换为LocalDateTime
        LocalDateTime startTime = LocalDateTime.parse(hourRange[0], formatter);
        LocalDateTime endTime = LocalDateTime.parse(hourRange[1], formatter);
        // 调用Mapper方法获取数据
        List<Co2DataHistory> result = co2Mapper.getCo2StatsByTimeRange(startTime, endTime);

        //h获取小时数
        int hour = startTime.getHour();
        // 设置插入时间为当前时间
        LocalDateTime now = LocalDateTime.now();
//        // 从now获取日期部分，精确到天
//        LocalDate currentDate = now.toLocalDate();
        // 从startTime获取日期部分，精确到天
        LocalDate currentDate = startTime.toLocalDate();
        for (Co2DataHistory item : result) {
            item.setCollectHour(hour);
            item.setUpdateTime(now);
            item.setCollectDate(currentDate);
        }
        System.out.println("执行了Co2缓存表的定时任务！");

        // 插入数据到历史表
        for (Co2DataHistory item : result) {
            co2Mapper.addCo2DateHistory(item);
        }

        //删除缓存表中的数据
        co2Mapper.deleteCo2DateCacheByHour(startTime,endTime);
    }

    //查询设备id Co2传感器是否已存在
    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return co2Mapper.existsByDeviceId(deviceId) > 0;
    }

    @Override
    public String addCo2RealTime(Integer deviceId) {
        // 设备存在性校验
        Device device = deviceService.findById(deviceId);
        if (device == null) return "设备不存在";

        // 设备类型校验
        if(!"Co2传感器".equals(device.getType())) return "该设备不为Co2传感器";

        // 重复性校验
        if(co2Mapper.existsByDeviceId(deviceId) > 0) return "该Co2传感器已存在";

        co2Mapper.addCo2RealTime(deviceId);
        return "添加成功";
    }

    //查询Co2传感器详情
    @Override
    public Device findCo2DetailById(Integer deviceId) {
        System.out.println("查询Co2传感器详情成功");
        return co2Mapper.findCo2DetailById(deviceId);
    }

    public PageResult<Co2RealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        //创建PageResult对象
        PageResult<Co2RealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 转换日期范围
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        // 调用 mapper 进行查询
        List<Co2RealTime> as = co2Mapper.findHistoryData(deviceId, start, end);
        // 将查询结果转换为 Page 对象
        Page<Co2RealTime> p = (Page<Co2RealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询Co2历史数据成功");
        return pb;
    }


    //分页查询实时状态数据
    @Override
    public PageResult<Co2RealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, String place) {
        //创建PageResult对象
        PageResult<Co2RealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<Co2RealTime> as = co2Mapper.findLatestStatus(deviceId, deviceName, place);
        // 将查询结果转换为 Page 对象
        Page<Co2RealTime> p = (Page<Co2RealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询Co2状态成功");
        return pb;
    }

    //查询实时状态数据
    @Override
    public List<Co2RealTimeByMinute> getAvgCo2(String place) {
        System.out.println(place);
        return co2Mapper.getAvgCo2(place);
    }

    //获取过去24小时的历史Co2数据，用于折线图展示
    @Override
    public List<Co2History24> getHistory24Hours(String place) {
        return co2Mapper.getHistory24Hours(place);
    }

    //获取过去24小时的历史Co2数据，用于饼图展示
    @Override
    public List<Co2Pie> getHistory24HoursForPie(String place) {
        return co2Mapper.getHistory24HoursForPie(place);
    }
}
