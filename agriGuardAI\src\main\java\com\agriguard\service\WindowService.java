package com.agriguard.service;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.pojo.WindowRealTime;

public interface WindowService {

    // 分页查询窗户最新状态
    PageResult<WindowRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, Integer windowStatus, String place);

    // 检查设备ID是否存在
    boolean existsByDeviceId(Integer deviceId);

    // 添加窗户状态，添加窗户设备id就可以
    String addWindowRealTime(Integer deviceId);

    //查询窗户详情
    Device findwindowDetailById(Integer deviceId);

    //修改窗户状态
    Result<String> updateStatus(Integer deviceId, Integer windowStatus);
}
