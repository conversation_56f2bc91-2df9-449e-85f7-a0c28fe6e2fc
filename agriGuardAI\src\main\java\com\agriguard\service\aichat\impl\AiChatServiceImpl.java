package com.agriguard.service.aichat.impl;

import com.agriguard.config.AiConfig;
import com.agriguard.dto.aichat.ChatRequestDTO;
import com.agriguard.dto.aichat.ChatResponseDTO;
import com.agriguard.entity.aichat.ChatMessage;
import com.agriguard.entity.aichat.ChatSession;
import com.agriguard.service.aichat.AiChatService;
import com.agriguard.service.aichat.function.FunctionService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;

/**
 * AI聊天服务实现
 */
@Slf4j
@Service
public class AiChatServiceImpl implements AiChatService {
    
    @Autowired
    private AiConfig aiConfig;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private FunctionService functionService;
    
    private final OkHttpClient httpClient;
    private static final String CHAT_COMPLETIONS_URL = "/v1/chat/completions";
    private static final String SESSION_PREFIX = "chat_session:user:";
    private static final int SESSION_EXPIRE_HOURS = 24;
    
    public AiChatServiceImpl() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }
    
    @Override
    public ChatResponseDTO chat(ChatRequestDTO chatRequest, Long userId) {
        try {
            // 获取或创建会话
            String sessionId = getOrCreateSessionId(chatRequest.getSessionId());
            ChatSession session = getOrCreateSession(sessionId, userId);

            // 构建消息列表
            List<JSONObject> messages = buildMessages(session, chatRequest);

            // 构建请求体（包含工具函数定义）
            JSONObject requestBody = buildRequestBody(messages, chatRequest);

            // 发送请求并处理可能的工具调用
            ChatResponseDTO result = sendRequestWithFunctionCalling(requestBody, session, userId);

            return result;

        } catch (Exception e) {
            log.error("AI聊天服务异常", e);
            return ChatResponseDTO.error("AI服务暂时不可用，请稍后再试");
        }
    }
    

    
    @Override
    public String getSessionHistory(String sessionId, Long userId) {
        if (!StringUtils.hasText(sessionId)) {
            return "会话不存在";
        }
        
        ChatSession session = (ChatSession) redisTemplate.opsForValue()
                .get(buildSessionKey(sessionId, userId));
        
        if (session == null || session.getMessages() == null) {
            return "会话历史为空";
        }
        
        // 验证会话所有权
        if (!userId.equals(session.getUserId())) {
            throw new SecurityException("无权访问此会话");
        }
        
        StringBuilder history = new StringBuilder();
        for (ChatMessage message : session.getMessages()) {
            if ("user".equals(message.getRole())) {
                history.append("用户: ").append(message.getContent()).append("\n\n");
            } else if ("assistant".equals(message.getRole())) {
                history.append("AI助手: ").append(message.getContent()).append("\n\n");
            }
        }
        
        return history.toString();
    }
    
    @Override
    public void clearSession(String sessionId, Long userId) {
        if (StringUtils.hasText(sessionId)) {
            // 先验证会话所有权
            ChatSession session = (ChatSession) redisTemplate.opsForValue()
                    .get(buildSessionKey(sessionId, userId));
            
            if (session != null && userId.equals(session.getUserId())) {
                redisTemplate.delete(buildSessionKey(sessionId, userId));
            } else {
                throw new SecurityException("无权操作此会话");
            }
        }
    }
    
    @Override
    public List<Map<String, Object>> getUserSessions(Long userId) {
        List<Map<String, Object>> sessionList = new ArrayList<>();
        
        try {
            // 构建用户会话前缀
            String userSessionPrefix = SESSION_PREFIX + userId + ":*";
            
            // 获取用户的所有会话key
            Set<String> sessionKeys = redisTemplate.keys(userSessionPrefix);
            
            if (sessionKeys != null) {
                for (String key : sessionKeys) {
                    ChatSession session = (ChatSession) redisTemplate.opsForValue().get(key);
                    if (session != null) {
                        Map<String, Object> sessionInfo = new HashMap<>();
                        sessionInfo.put("id", session.getSessionId());
                        
                        // 生成会话标题
                        String title = generateSessionTitle(session);
                        sessionInfo.put("title", title);
                        sessionInfo.put("updateTime", session.getUpdateTime() != null ? 
                                session.getUpdateTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() : System.currentTimeMillis());
                        sessionInfo.put("messageCount", session.getMessages() != null ? session.getMessages().size() : 0);
                        
                        sessionList.add(sessionInfo);
                    }
                }
            }
            
            // 按更新时间倒序排列
            sessionList.sort((a, b) -> {
                Long timeA = (Long) a.get("updateTime");
                Long timeB = (Long) b.get("updateTime");
                return timeB.compareTo(timeA);
            });
            
        } catch (Exception e) {
            log.error("获取用户会话列表失败，用户ID: {}", userId, e);
        }
        
        return sessionList;
    }
    
    /**
     * 生成会话标题
     */
    private String generateSessionTitle(ChatSession session) {
        if (session.getTitle() != null && !session.getTitle().isEmpty()) {
            return session.getTitle();
        }
        
        // 使用第一条用户消息作为标题
        if (session.getMessages() != null && !session.getMessages().isEmpty()) {
            for (ChatMessage message : session.getMessages()) {
                if ("user".equals(message.getRole()) && message.getContent() != null) {
                    String content = message.getContent().trim();
                    if (content.length() > 20) {
                        return content.substring(0, 20) + "...";
                    } else {
                        return content;
                    }
                }
            }
        }
        
        return "新会话";
    }
    
    /**
     * 获取或创建会话ID
     */
    private String getOrCreateSessionId(String sessionId) {
        if (StringUtils.hasText(sessionId)) {
            return sessionId;
        }
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 构建会话键，包含用户隔离
     */
    private String buildSessionKey(String sessionId, Long userId) {
        return SESSION_PREFIX + userId + ":" + sessionId;
    }
    
    /**
     * 获取或创建会话
     */
    private ChatSession getOrCreateSession(String sessionId, Long userId) {
        ChatSession session = (ChatSession) redisTemplate.opsForValue()
                .get(buildSessionKey(sessionId, userId));
        
        if (session == null) {
            session = ChatSession.builder()
                    .sessionId(sessionId)
                    .messages(new ArrayList<>())
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .status(1)
                    .totalTokens(0)
                    .userId(userId)
                    .build();
        }
        
        return session;
    }
    
    /**
     * 构建消息列表
     */
    private List<JSONObject> buildMessages(ChatSession session, ChatRequestDTO chatRequest) {
        List<JSONObject> messages = new ArrayList<>();
        
        // 添加系统提示词
        String systemPrompt = aiConfig.getSystemPrompt();
        if (StringUtils.hasText(systemPrompt)) {
            JSONObject systemMessage = new JSONObject();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);
        }
        
        // 添加历史消息
        if (session.getMessages() != null) {
            int startIndex = Math.max(0, session.getMessages().size() - 20);
            for (int i = startIndex; i < session.getMessages().size(); i++) {
                ChatMessage msg = session.getMessages().get(i);
                if (!"system".equals(msg.getRole())) {
                    JSONObject message = new JSONObject();
                    message.put("role", msg.getRole());
                    message.put("content", msg.getContent());
                    messages.add(message);
                }
            }
        }
        
        // 添加当前用户消息
        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", chatRequest.getMessage());
        messages.add(userMessage);
        
        return messages;
    }
    
    /**
     * 构建请求体（包含工具函数定义）
     */
    private JSONObject buildRequestBody(List<JSONObject> messages, ChatRequestDTO chatRequest) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("model", aiConfig.getModel());
        requestBody.put("messages", messages);
        requestBody.put("stream", false);
        requestBody.put("max_tokens", aiConfig.getMaxTokens());

        // 设置温度参数
        double temperature = chatRequest.getTemperature() != null ?
                chatRequest.getTemperature() : aiConfig.getTemperature();
        requestBody.put("temperature", temperature);

        // 添加工具函数定义
        JSONArray tools = functionService.getFunctionDefinitions();
        if (!tools.isEmpty()) {
            requestBody.put("tools", tools);
            requestBody.put("tool_choice", "auto"); // 让AI自动决定是否调用工具
        }

        return requestBody;
    }
    
    /**
     * 发送请求并处理工具调用
     */
    private ChatResponseDTO sendRequestWithFunctionCalling(JSONObject requestBody, ChatSession session, Long userId) throws Exception {
        int maxIterations = 5; // 最大迭代次数，防止无限循环
        int iteration = 0;

        // 保存用户消息
        JSONArray messages = requestBody.getJSONArray("messages");
        JSONObject lastUserMessage = messages.getJSONObject(messages.size() - 1);
        saveMessage(session, "user", lastUserMessage.getString("content"), 0);

        while (iteration < maxIterations) {
            iteration++;

            // 发送请求
            String responseJson = sendRequest(requestBody);
            JSONObject response = JSON.parseObject(responseJson);

            JSONArray choices = response.getJSONArray("choices");
            if (choices == null || choices.isEmpty()) {
                throw new RuntimeException("AI响应格式错误");
            }

            JSONObject choice = choices.getJSONObject(0);
            JSONObject message = choice.getJSONObject("message");

            // 检查是否需要调用工具
            JSONArray toolCalls = message.getJSONArray("tool_calls");
            if (toolCalls == null || toolCalls.isEmpty()) {
                // 没有工具调用，直接返回结果
                String assistantMessage = message.getString("content");
                Integer tokenUsed = extractTokenUsage(response);

                saveMessage(session, "assistant", assistantMessage, tokenUsed);
                saveSession(session, userId);

                return ChatResponseDTO.success(assistantMessage, session.getSessionId(), tokenUsed);
            }

            // 处理工具调用
            messages.add(message); // 添加助手的工具调用消息

            for (int i = 0; i < toolCalls.size(); i++) {
                JSONObject toolCall = toolCalls.getJSONObject(i);
                String toolCallId = toolCall.getString("id");
                JSONObject function = toolCall.getJSONObject("function");
                String functionName = function.getString("name");
                String argumentsStr = function.getString("arguments");

                log.info("AI请求调用工具: {} 参数: {}", functionName, argumentsStr);

                // 执行工具函数
                Object result;
                try {
                    JSONObject arguments = JSON.parseObject(argumentsStr);
                    result = functionService.executeFunction(functionName, arguments, userId);
                } catch (Exception e) {
                    log.error("工具函数执行失败: {}", functionName, e);
                    result = Map.of("error", "工具执行失败: " + e.getMessage());
                }

                // 构建工具调用结果消息
                JSONObject toolMessage = new JSONObject();
                toolMessage.put("role", "tool");
                toolMessage.put("tool_call_id", toolCallId);
                toolMessage.put("content", JSON.toJSONString(result));

                messages.add(toolMessage);
            }

            // 更新请求体，继续对话
            requestBody.put("messages", messages);
        }

        throw new RuntimeException("工具调用迭代次数超限");
    }

    /**
     * 发送HTTP请求
     */
    private String sendRequest(JSONObject requestBody) throws IOException {
        String url = aiConfig.getBaseUrl() + CHAT_COMPLETIONS_URL;
        
        RequestBody body = RequestBody.create(
                requestBody.toJSONString(), 
                MediaType.parse("application/json")
        );
        
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + aiConfig.getApiKey())
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("HTTP请求失败: " + response.code() + " " + response.message());
            }
            
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new IOException("响应体为空");
            }
            
            return responseBody.string();
        }
    }
    
    /**
     * 提取AI响应消息
     */
    private String extractAssistantMessage(JSONObject response) {
        JSONArray choices = response.getJSONArray("choices");
        if (choices != null && !choices.isEmpty()) {
            JSONObject choice = choices.getJSONObject(0);
            JSONObject message = choice.getJSONObject("message");
            if (message != null) {
                return message.getString("content");
            }
        }
        throw new RuntimeException("无法解析AI响应消息");
    }
    
    /**
     * 提取token使用量
     */
    private Integer extractTokenUsage(JSONObject response) {
        JSONObject usage = response.getJSONObject("usage");
        if (usage != null) {
            return usage.getInteger("total_tokens");
        }
        return 0;
    }
    
    /**
     * 保存消息到会话
     */
    private void saveMessage(ChatSession session, String role, String content, Integer tokenCount) {
        ChatMessage message = ChatMessage.builder()
                .messageId(UUID.randomUUID().toString().replace("-", ""))
                .sessionId(session.getSessionId())
                .role(role)
                .content(content)
                .createTime(LocalDateTime.now())
                .tokenCount(tokenCount)
                .build();
        
        session.getMessages().add(message);
        session.setUpdateTime(LocalDateTime.now());
        session.setTotalTokens(session.getTotalTokens() + tokenCount);
    }
    
    /**
     * 保存会话到Redis
     */
    private void saveSession(ChatSession session, Long userId) {
        redisTemplate.opsForValue().set(
                buildSessionKey(session.getSessionId(), userId), 
                session, 
                SESSION_EXPIRE_HOURS, 
                TimeUnit.HOURS
        );
    }
} 