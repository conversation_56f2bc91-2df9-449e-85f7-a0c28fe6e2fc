package com.agriguard.service.impl;

import com.agriguard.entity.device.*;
import com.agriguard.mapper.AirPressureMapper;
import com.agriguard.pojo.*;
import com.agriguard.service.AirPressureService;
import com.agriguard.service.DeviceService;
import com.agriguard.utils.DateTimeUtil;
import com.agriguard.utils.TemperatureGenerator;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class AirPressureServiceImpl implements AirPressureService {
    @Autowired
    private AirPressureMapper airPressureMapper;
    @Autowired
    private DeviceService deviceService;

    @Override
    public void insertTemperature() {
        //默认气压传感器设备编号为1
        int deviceId = 1;
        //模拟生成华为云气压数据
        BigDecimal apre = BigDecimal.valueOf((TemperatureGenerator.generateTemperature())/10);
        System.out.println("生成的气压: " + apre);
        //生成数据更新时间
        LocalDateTime updateTime = java.time.LocalDateTime.now();
        //将气压实时状态表进行更新,实时状态表就不进行时间更新了，数据库会自己进行更新
        airPressureMapper.updateAirPressureToRealTime(deviceId,apre,updateTime);
        //将气压数据插入气压缓存表
        airPressureMapper.insertAirPressureToDataCache(deviceId,apre,updateTime);
    }

    //定时对气压数据进行分析，存储历史数据，并进行数据删除
    @Override
    public void AirPressureDateSys() {

        //获取当前时间
        String[] hourRange = DateTimeUtil.getPreviousHourRange();
        System.out.println("上一个小时范围:");
        System.out.println("开始时间: " + hourRange[0]);
        System.out.println("结束时间: " + hourRange[1]);
        //将String类型的时间转换为LocalDateTime类型
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将字符串转换为LocalDateTime
        LocalDateTime startTime = LocalDateTime.parse(hourRange[0], formatter);
        LocalDateTime endTime = LocalDateTime.parse(hourRange[1], formatter);
        // 调用Mapper方法获取数据
        List<AirPressureDataHistory> result = airPressureMapper.getAirPressureStatsByTimeRange(startTime, endTime);

        //h获取小时数
        int hour = startTime.getHour();
        // 设置插入时间为当前时间
        LocalDateTime now = LocalDateTime.now();
//        // 从now获取日期部分，精确到天
//        LocalDate currentDate = now.toLocalDate();
        // 从startTime获取日期部分，精确到天
        LocalDate currentDate = startTime.toLocalDate();
        for (AirPressureDataHistory item : result) {
            item.setCollectHour(hour);
            item.setUpdateTime(now);
            item.setCollectDate(currentDate);
        }
        System.out.println("执行了气压缓存表的定时任务！");

        // 插入数据到历史表
        for (AirPressureDataHistory item : result) {
            airPressureMapper.addAirPressureDateHistory(item);
        }

        //删除缓存表中的数据
        airPressureMapper.deleteAirPressureDateCacheByHour(startTime,endTime);
    }

    //查询设备id气压传感器是否已存在
    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return airPressureMapper.existsByDeviceId(deviceId) > 0;
    }

    @Override
    public String addAirPressureRealTime(Integer deviceId) {
        // 校验设备类型
        Device device = deviceService.findById(deviceId);
        if (device == null) {
            return "设备不存在";
        }
        if(!"气压传感器".equals(device.getType())) { // 修改校验类型为气压传感器
            return "该设备不为气压传感器";
        }
        // 新增重复性校验
        if (airPressureMapper.existsByDeviceId(deviceId) > 0) {
            return "该气压传感器已存在";
        }
        // 原有重复性校验（根据AirPressureController中的逻辑，应在Controller层校验）
        // 添加参数校验
        if (deviceId == null) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        airPressureMapper.addAirPressureRealTime(deviceId);
        return "添加成功"; // 添加明确返回值
    }

    //查询气压传感器详情
    @Override
    public Device findAirPressureDetailById(Integer deviceId) {
        System.out.println("查询气压传感器详情成功");
        return airPressureMapper.findAirPressureDetailById(deviceId);
    }

    public PageResult<AirPressureRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        //创建PageResult对象
        PageResult<AirPressureRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 转换日期范围
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        // 调用 mapper 进行查询
        List<AirPressureRealTime> as = airPressureMapper.findHistoryData(deviceId, start, end);
        // 将查询结果转换为 Page 对象
        Page<AirPressureRealTime> p = (Page<AirPressureRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询气压历史数据成功");
        return pb;
    }


    //分页查询实时状态数据
    @Override
    public PageResult<AirPressureRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, String place) {
        //创建PageResult对象
        PageResult<AirPressureRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<AirPressureRealTime> as = airPressureMapper.findLatestStatus(deviceId, deviceName, place);
        // 将查询结果转换为 Page 对象
        Page<AirPressureRealTime> p = (Page<AirPressureRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询气压状态成功");
        return pb;
    }

    //查询实时状态数据
    @Override
    public List<AirPressureRealTimeByMinute> getAvgAirPressure(String place) {
        System.out.println(place);
        return airPressureMapper.getAvgAirPressure(place);
    }
    //获取过去24小时的历史气压数据，用于折线图展示
    @Override
    public List<AirPressureHistory24> getHistory24Hours(String place) {
        return airPressureMapper.getHistory24Hours(place);
    }

    //获取过去24小时的历史气压数据，用于饼图展示
    @Override
    public List<AirPressurePie> getHistory24HoursForPie(String place) {
        return airPressureMapper.getHistory24HoursForPie(place);
    }
}
