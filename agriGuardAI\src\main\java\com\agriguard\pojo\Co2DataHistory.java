package com.agriguard.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class Co2DataHistory {
    private BigInteger id;
    private Integer deviceId;
    private LocalDate collectDate;
    private Integer collectHour;
    private BigDecimal maxCo2;
    private  BigDecimal minCo2;
    private  BigDecimal avgCo2;
    private  Integer dataCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
