import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import App from './App.vue'
import router from './router'
import {useAuthStore} from "@/stores/auth.js";
import DataVVue3 from '@kjgl77/datav-vue3'

const app = createApp(App)
const pinia = createPinia()

app.use(ElementPlus, {
  locale: zhCn,
})
app.use(pinia)
app.use(router)
app.use(DataVVue3)

// 在挂载前加载用户数据
const authStore = useAuthStore()
authStore.loadUserFromStorage()

app.mount('#app')
