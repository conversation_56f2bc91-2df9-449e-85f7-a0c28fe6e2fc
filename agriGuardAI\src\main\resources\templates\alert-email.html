<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域报警通知</title>
    <style>
        body, table {
            margin: 0;
            padding: 0;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            width: 100%;
            border-collapse: collapse;
        }
        .container {
            max-width: 1000px;
            margin: 10px auto;
            border: 1px solid #eee;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 0 5px rgba(0,0,0,0.1);
        }
        .header {
            background: #f8f8f8;
            padding: 12px 20px;
            border-bottom: 1px solid #eee;
        }
        .header h2 {
            margin: 0;
            font-size: 18px;
            color: #d9534f;
            display: flex;
            align-items: center;
        }
        .header h2 img {
            width: 20px;
            height: 20px;
            margin-right: 6px;
        }
        .info-row {
            padding: 10px 20px;
            color: #666;
            border-bottom: 1px solid #eee;
        }
        .info-row span {
            display: inline-block;
            width: 48%;
            margin: 2px 0;
        }
        .alert-type {
            padding: 12px 20px;
            font-size: 16px;
            color: #d9534f;
            border-bottom: 1px solid #eee;
            background: #fff8f8;
        }
        .table-section {
            width: 100%;
            border-bottom: 1px solid #eee;
        }
        .table-section thead {
            background: #f8f8f8;
        }
        .table-section th,
        .table-section td {
            padding: 8px 10px;
            text-align: left;
            border: 1px solid #eee;
            font-size: 14px;
        }
        .table-section th {
            font-weight: bold;
            color: #333;
        }
        .table-section tbody tr:nth-child(odd) {
            background: #fcfcfc;
        }
        .current-value {
            color: #d9534f;
            font-weight: bold;
        }
        .action-row {
            padding: 12px 20px;
            background: #eaf9e6;
            color: #5cb85c;
            font-size: 14px;
        }
        .footer {
            padding: 10px 20px;
            text-align: center;
            color: #999;
            font-size: 12px;
            border-top: 1px solid #eee;
        }

        /* 手机端适配 */
        @media (max-width: 600px) {
            .info-row span {
                width: 100%;
            }
            .table-section th,
            .table-section td {
                font-size: 12px;
                padding: 6px 8px;
            }
            .header h2 {
                font-size: 16px;
            }
            .alert-type {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <!-- 头部区域 -->
    <div class="header">
        <h2>
            <img src="https://cdn-icons-png.flaticon.com/512/25/25694.png" alt="警告图标">
            区域报警通知
        </h2>
    </div>

    <!-- 基础信息 -->
    <div class="info-row">
        <span><strong>监控区域：</strong><span th:text="${place}"></span></span>
        <span><strong>报警时间：</strong><span th:text="${#temporals.format(updateTime, 'yyyy-MM-dd HH:mm')}"></span></span>
    </div>

    <!-- 循环显示每种参数类型的报警 -->
    <div th:each="alertEntry : ${combinedAlerts}">
        <!-- 报警类型标题 -->
        <div class="alert-type">
            <img th:src="@{'https://cdn-icons-png.flaticon.com/512/1160/1160646.png'}"
                 alt="参数图标"
                 style="width:16px;height:16px;margin-right:4px;">
            <span th:text="${alertEntry.key}"></span>
        </div>

        <!-- 数据表格 -->
        <table class="table-section">
            <thead>
            <tr>
                <th>设备名称</th>
                <th>设备ID</th>
                <th>当前值</th>
                <th>阈值范围</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="alert : ${alertEntry.value}">
                <td th:text="${alert.deviceName}"></td>
                <td th:text="${alert.deviceId}"></td>
                <td class="current-value" th:text="${alert.currentValue}"></td>
                <td th:text="${alert.thresholdRange}"></td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- 操作提示 -->
    <div class="action-row">
        <img src="https://cdn-icons-png.flaticon.com/512/25/25694.png" alt="操作图标" style="width:16px;height:16px;margin-right:4px;">
        请及时登录 <a href="http://www.agriguard.com" style="color:#5cb85c;text-decoration:underline;">AgriGuardAI监控平台</a> 处理报警！
    </div>

    <!-- 页脚 -->
    <div class="footer">
        <p style="margin:2px 0;">系统自动发送，请勿直接回复</p>
        <p style="margin:2px 0;">© 2025 AgriGuardAI 监控系统</p>
    </div>
</div>
</body>
</html>