package com.agriguard.service;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.HumidityRealTime;
import com.agriguard.pojo.PageResult;

import java.time.LocalDate;
import java.util.List;

public interface HumidityService {


    //定时对湿度数据进行分析，存储历史数据，并进行数据删除
    void HumidityDateSys();

    // 检查设备是否已存在湿度传感器
    boolean existsByDeviceId(Integer deviceId);

    // 添加湿度传感器
    String addHumidityRealTime(Integer deviceId);

    // 查询湿度传感器详情
    Device findHumidityDetailById(Integer deviceId);

    // 分页查询历史数据
    PageResult<HumidityRealTime> findHistoryData(Integer pageNum, Integer pageSize,
                                                 Integer deviceId, LocalDate beginData, LocalDate endData);

    // 多条件分页查询实时状态
    PageResult<HumidityRealTime> findLatestStatus(Integer pageNum, Integer pageSize,
                                                  Integer deviceId, String deviceName, String place);

    // 获取指定位置的分钟级平均湿度
    List<HumidityRealTimeByMinute> getAvgHumidity(String place);

    //插入湿度数据
    void insertTemperature();

    //获取过去24小时的历史湿度数据，用于折线图展示
    List<HumidityHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史湿度数据，用于饼图展示
    List<HumidityPie> getHistory24HoursForPie(String place);


}
