# AgriGuard AI 工具调用功能实施计划

## 概述

基于已成功实现的Function Calling基础框架，本文档详细规划AgriGuard AI系统中农业数据工具调用功能的分阶段实施方案。

## 基础框架状态

✅ **已完成**：
- Function Calling基础架构
- 工具函数定义和执行框架
- 示例工具函数（get_current_time）
- 与DeepSeek API的完整集成
- 测试验证通过

## 实施进度状态

✅ **阶段一（优先级1）- 已完成**：
- get_current_environment_data - 获取当前环境监控数据
- get_device_status - 获取设备运行状态
- get_ml_prediction_result - 获取AI预测结果和建议

✅ **阶段二（优先级2）- 已完成**：
- get_historical_trends - 获取历史数据趋势分析
- analyze_environment_patterns - 分析环境数据模式和异常

🚧 **阶段三（优先级3）- 待实施**：
- control_device - 控制农业设备开关

## 优先级分级工具函数清单

### 优先级1：核心环境数据查询（立即实施）

#### 1.1 get_current_environment_data
**功能描述**：获取当前环境监控数据

**JSON Schema**：
```json
{
  "name": "get_current_environment_data",
  "description": "获取指定设备或区域的当前环境监控数据，包括温度、湿度、CO2、氨气、气压等",
  "parameters": {
    "type": "object",
    "properties": {
      "device_id": {
        "type": "integer",
        "description": "设备ID，可选，不指定则返回所有设备数据"
      },
      "data_types": {
        "type": "array",
        "items": {
          "type": "string",
          "enum": ["temperature", "humidity", "co2", "ammonia", "pressure", "all"]
        },
        "description": "要获取的数据类型，默认为all"
      },
      "location": {
        "type": "string",
        "description": "监控区域名称，可选"
      }
    },
    "required": []
  }
}
```

**返回值格式**：
```json
{
  "timestamp": "2025-07-16T20:01:25",
  "devices": [
    {
      "device_id": 50,
      "device_name": "温室1号温度传感器",
      "location": "温室1",
      "data": {
        "temperature": 25.5,
        "humidity": 65,
        "co2": 1200,
        "ammonia": 15,
        "pressure": 101.3
      },
      "update_time": "2025-07-16T20:00:00"
    }
  ]
}
```

**对应API接口**：
- TemperatureController.list()
- HumidityController.list()
- Co2Controller.list()
- AmmoniaController.list()
- AirPressureController.list()

**权限要求**：read
**安全考虑**：低风险，只读操作

#### 1.2 get_device_status
**功能描述**：获取设备运行状态

**JSON Schema**：
```json
{
  "name": "get_device_status",
  "description": "获取农业设备的当前运行状态，包括风机、水帘、加热片、窗户等",
  "parameters": {
    "type": "object",
    "properties": {
      "device_type": {
        "type": "string",
        "enum": ["fan", "nappe", "heat", "window", "all"],
        "description": "设备类型，默认为all"
      },
      "device_id": {
        "type": "integer",
        "description": "具体设备ID，可选"
      },
      "location": {
        "type": "string",
        "description": "设备所在区域，可选"
      }
    },
    "required": []
  }
}
```

**返回值格式**：
```json
{
  "timestamp": "2025-07-16T20:01:25",
  "devices": [
    {
      "device_id": 1,
      "device_type": "fan",
      "device_name": "大风机1",
      "location": "温室1",
      "status": 1,
      "status_text": "运行中",
      "last_update": "2025-07-16T19:55:00"
    }
  ]
}
```

**对应API接口**：
- FanController.list()
- WindowController.list()
- HeatController.list()
- NappeController.list()

**权限要求**：read
**安全考虑**：低风险，只读操作

#### 1.3 get_ml_prediction_result
**功能描述**：获取AI预测结果和建议

**JSON Schema**：
```json
{
  "name": "get_ml_prediction_result",
  "description": "获取机器学习模型的预测结果和设备控制建议",
  "parameters": {
    "type": "object",
    "properties": {
      "include_reasoning": {
        "type": "boolean",
        "description": "是否包含预测推理过程，默认true"
      },
      "format": {
        "type": "string",
        "enum": ["detailed", "summary"],
        "description": "返回格式，详细或摘要，默认detailed"
      }
    },
    "required": []
  }
}
```

**返回值格式**：
```json
{
  "timestamp": "2025-07-16T20:01:25",
  "prediction": {
    "environment_input": {
      "temperature": 25.5,
      "humidity": 65,
      "co2": 1200,
      "ammonia": 15,
      "internal_pressure": 101.3,
      "external_pressure": 101.5
    },
    "recommendations": {
      "fan1": {"action": 0, "reason": "温度适宜，无需开启"},
      "fan2": {"action": 0, "reason": "温度适宜，无需开启"},
      "nappe": {"action": 1, "reason": "湿度偏高，建议开启水帘"},
      "heat": {"action": 0, "reason": "温度正常，无需加热"},
      "window": {"action": 1, "reason": "CO2浓度偏高，建议开窗通风"}
    },
    "confidence": 0.85,
    "ml_service_status": "healthy"
  }
}
```

**对应API接口**：
- MlTestController.testPrediction()
- AiCommandServiceImpl.getMlPredictionCommands()

**权限要求**：read
**安全考虑**：低风险，只读操作

### 优先级2：历史数据分析（第二阶段实施）

#### 2.1 get_historical_trends
**功能描述**：获取历史数据趋势分析

**JSON Schema**：
```json
{
  "name": "get_historical_trends",
  "description": "获取指定时间范围内的环境数据历史趋势",
  "parameters": {
    "type": "object",
    "properties": {
      "data_type": {
        "type": "string",
        "enum": ["temperature", "humidity", "co2", "ammonia", "pressure"],
        "description": "数据类型"
      },
      "time_range": {
        "type": "string",
        "enum": ["24h", "7d", "30d"],
        "description": "时间范围，默认24h"
      },
      "device_id": {
        "type": "integer",
        "description": "设备ID，可选"
      },
      "aggregation": {
        "type": "string",
        "enum": ["hourly", "daily"],
        "description": "数据聚合方式，默认hourly"
      }
    },
    "required": ["data_type"]
  }
}
```

**对应API接口**：
- TemperatureController.getHistory24()
- HumidityController.getHistory24()
- Co2Controller.getHistory24()

**权限要求**：read
**安全考虑**：低风险，只读操作

#### 2.2 analyze_environment_patterns
**功能描述**：分析环境数据模式和异常

**JSON Schema**：
```json
{
  "name": "analyze_environment_patterns",
  "description": "分析环境数据的模式、趋势和异常情况",
  "parameters": {
    "type": "object",
    "properties": {
      "analysis_type": {
        "type": "string",
        "enum": ["anomaly", "pattern", "correlation", "all"],
        "description": "分析类型，默认all"
      },
      "time_range": {
        "type": "string",
        "enum": ["24h", "7d", "30d"],
        "description": "分析时间范围，默认24h"
      },
      "threshold": {
        "type": "number",
        "description": "异常检测阈值，默认2.0"
      }
    },
    "required": []
  }
}
```

**权限要求**：read
**安全考虑**：低风险，只读操作

### 优先级3：设备控制操作（第三阶段实施）

#### 3.1 control_device
**功能描述**：控制农业设备开关

**JSON Schema**：
```json
{
  "name": "control_device",
  "description": "控制指定设备的开关状态，需要用户确认",
  "parameters": {
    "type": "object",
    "properties": {
      "device_type": {
        "type": "string",
        "enum": ["fan", "nappe", "heat", "window"],
        "description": "设备类型"
      },
      "device_id": {
        "type": "integer",
        "description": "设备ID"
      },
      "action": {
        "type": "integer",
        "enum": [0, 1],
        "description": "操作：0=关闭，1=开启"
      },
      "reason": {
        "type": "string",
        "description": "操作原因，必填"
      }
    },
    "required": ["device_type", "device_id", "action", "reason"]
  }
}
```

**对应API接口**：
- AiCommandServiceImpl.command()
- HuaweiIotCommandService.sendCommand()

**权限要求**：write
**安全考虑**：高风险，需要用户确认和审计日志

## 技术风险评估和缓解措施

### 高风险项
1. **设备控制安全性**
   - **风险**：误操作可能影响生产
   - **缓解**：多重确认机制、操作日志、权限控制

2. **API性能影响**
   - **风险**：频繁调用可能影响系统性能
   - **缓解**：缓存机制、限流控制、异步处理

### 中风险项
1. **数据一致性**
   - **风险**：实时数据可能不一致
   - **缓解**：数据版本控制、缓存更新策略

2. **错误处理**
   - **风险**：工具调用失败影响用户体验
   - **缓解**：完善的异常处理、降级策略

### 低风险项
1. **参数验证**
   - **风险**：参数错误导致功能异常
   - **缓解**：严格的参数验证、默认值处理

## 预期效果

### 第一阶段完成后
- AI可以获取实时环境数据，提供基于真实数据的专业建议
- 用户体验从"通用农业咨询"升级为"个性化实时指导"
- 显著提升系统的实用价值

### 第二阶段完成后
- AI可以分析历史趋势，提供深度洞察
- 支持复杂的农业决策和长期规划
- 成为真正的"智能农业顾问"

### 第三阶段完成后
- 实现完整的"对话式农业管理"
- 用户可以通过自然语言完成设备控制
- 系统成为完整的智能农业助手

## 成功指标

1. **功能指标**
   - 工具调用成功率 > 95%
   - 响应时间 < 3秒
   - 数据准确率 > 99%

2. **用户体验指标**
   - 用户满意度 > 4.5/5
   - 功能使用率 > 60%
   - 问题解决率 > 80%

3. **技术指标**
   - 系统可用性 > 99.5%
   - 错误率 < 1%
   - 性能影响 < 10%

## 阶段二测试说明

### 已实现的工具函数

1. **get_historical_trends** - 历史数据趋势分析
   - 支持的数据类型：temperature, humidity, co2, ammonia, pressure
   - 时间范围：24h, 7d, 30d（默认24h）
   - 聚合方式：hourly, daily（默认hourly）
   - 返回统计信息、趋势方向、变化率和专业建议

2. **analyze_environment_patterns** - 环境数据模式分析
   - 分析类型：anomaly（异常检测）, pattern（模式分析）, correlation（相关性分析）, all（全部）
   - 检测阈值：可配置的异常检测阈值
   - 详细程度：basic, detailed, comprehensive
   - 提供综合健康评分和改进建议

### 测试方法

#### 方法一：通过API测试

1. **获取工具函数列表**：
   ```
   GET /api/function-test/functions
   ```

2. **获取测试建议**：
   ```
   GET /api/function-test/test-suggestions
   ```

3. **测试对话功能**：
   ```
   POST /api/function-test/chat
   {
     "message": "请分析过去24小时的温度变化趋势",
     "sessionId": "test-session"
   }
   ```

#### 方法二：通过聊天界面测试

可以使用以下测试用例：

**历史趋势分析测试**：
- "请分析过去24小时的温度变化趋势"
- "获取湿度的历史数据分析，包括统计信息和建议"
- "分析CO2浓度的历史趋势，时间范围24小时"
- "查看氨气浓度的历史变化，提供专业分析"

**环境模式分析测试**：
- "请对环境数据进行全面的模式分析"
- "检测当前环境中的异常情况"
- "分析环境参数之间的相关性"
- "进行环境数据的异常检测，阈值设为2.5"

**综合测试**：
- "请分析当前环境状况，包括历史趋势和异常检测，然后给出专业建议"
- "基于温度和湿度的历史数据，预测未来的环境变化趋势"
- "分析环境数据模式，建议如何优化设备运行策略"

### 预期结果

测试成功的标志：
1. 工具函数能够正确注册并在函数列表中显示
2. AI可以根据用户请求自动选择合适的工具函数
3. 返回结构化的分析结果，包括统计信息、趋势分析和专业建议
4. 错误处理机制正常工作，提供有意义的错误信息

## 结论

基于已成功实现的Function Calling基础框架，AgriGuard AI系统现已完成阶段二的历史数据分析功能实施。通过新增的`get_historical_trends`和`analyze_environment_patterns`工具函数，AI助手现在具备了：

- 深度的历史数据趋势分析能力
- 智能的环境数据模式识别
- 专业的异常检测和相关性分析
- 基于数据的专业农业建议

阶段二的完成使AgriGuard AI从"实时数据查询助手"升级为"智能数据分析顾问"，为进入阶段三的设备控制功能奠定了坚实基础。
