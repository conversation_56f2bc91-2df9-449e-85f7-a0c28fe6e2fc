<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.HeatMapper">

    <!--    查看加热片设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM heat_real_time WHERE device_id = #{deviceId}
    </select>

<!--   添加加热片实时状态-->
    <insert id="addHeatRealTime" parameterType="com.agriguard.pojo.HeatRealTime">
        INSERT INTO heat_real_time
            (device_id, heat_status, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>


    <!-- 查询加热片状态的结果映射 -->
    <resultMap id="HeatStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="heat_status" property="heatStatus" jdbcType="TINYINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询加热片状态 -->
    <select id="findLatestStatus" resultMap="HeatStatusResultMap">
        SELECT
        hrt.device_id,
        d.device_name,
        d.place,
        hrt.heat_status,
     --   hrt.update_time
        DATE_FORMAT(hrt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        heat_real_time hrt
        JOIN
        device d ON hrt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null">
            AND hrt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name = #{deviceName}
        </if>
        <if test="heatStatus != null">
            AND hrt.heat_status = #{heatStatus}
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        hrt.update_time DESC
    </select>

    <!--获取加热片详情-->
    <select id="findHeatDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!--修改加热片实时表数据-->
    <update id="updateHeatRealTime" parameterType="com.agriguard.pojo.HeatRealTime">
        UPDATE heat_real_time
        SET
        heat_status = #{heatStatus},
        update_time = #{updateTime}
        WHERE device_id = #{deviceId}
    </update>

    <!--添加加热片历史数据-->
    <insert id="updateHeatDateHistory" parameterType="com.agriguard.pojo.HeatDataHistory">
        INSERT INTO heat_data_history
            (device_id, heat_status, update_time)
        VALUES
            (#{deviceId}, #{heatStatus}, now())
    </insert>

    <!-- 历史数据查询 -->
    <select id="findHistoryData" resultType="com.agriguard.pojo.HeatRealTime">
        SELECT
        h.device_id AS deviceId,
        h.heat_status AS heatStatus,
        h.update_time AS updateTime
        FROM heat_data_history h
        INNER JOIN device d ON h.device_id = d.device_id
        WHERE h.device_id = #{deviceId}
        <if test="start != null and end != null">
            AND h.update_time BETWEEN #{start} AND #{end}
        </if>
        ORDER BY h.update_time DESC
    </select>

<!--    判断Ai命令是否已存在-->
    <select id="heatAiCommand" resultType="int">
        select heat_status from heat_real_time where device_id = #{deviceId}
    </select>
    <update id="ordercommand" parameterType="com.agriguard.pojo.HeatRealTime">
        UPDATE heat_real_time
        SET
            heat_status = #{heatStatus},
            update_time = #{updateTime}
        WHERE device_id = #{deviceId}
    </update>
</mapper>
