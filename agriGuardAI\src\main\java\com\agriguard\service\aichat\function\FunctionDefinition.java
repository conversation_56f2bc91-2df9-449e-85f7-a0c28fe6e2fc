package com.agriguard.service.aichat.function;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 工具函数定义
 * 用于定义AI可以调用的工具函数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FunctionDefinition {
    
    /**
     * 函数名称，必须唯一
     */
    private String name;
    
    /**
     * 函数描述，告诉AI这个函数的作用
     */
    private String description;
    
    /**
     * 函数参数定义，JSON Schema格式
     */
    private Map<String, Object> parameters;
    
    /**
     * 是否需要用户权限验证
     */
    private boolean requiresAuth;
    
    /**
     * 权限级别：read（只读）、write（写入）、admin（管理员）
     */
    private String permissionLevel;
    
    /**
     * 是否需要用户确认（用于危险操作）
     */
    private boolean requiresConfirmation;
}
