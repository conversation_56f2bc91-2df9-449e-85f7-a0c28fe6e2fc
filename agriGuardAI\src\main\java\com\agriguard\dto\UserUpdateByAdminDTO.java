package com.agriguard.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdateByAdminDTO {
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @NotBlank(message = "用户名不能为空")
    private String userName;

    @Email(message = "邮箱格式不正确")
    private String email;

    private String newPassword; // 非必填
    private String realName;
    private String phone;
}
