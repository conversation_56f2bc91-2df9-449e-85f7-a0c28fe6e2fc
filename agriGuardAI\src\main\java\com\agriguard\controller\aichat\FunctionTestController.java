package com.agriguard.controller.aichat;

import com.agriguard.dto.aichat.ChatRequestDTO;
import com.agriguard.dto.aichat.ChatResponseDTO;
import com.agriguard.pojo.Result;
import com.agriguard.service.aichat.AiChatService;
import com.agriguard.service.aichat.function.FunctionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 工具调用测试控制器
 * 用于测试Function Calling功能
 */
@Slf4j
@RestController
@RequestMapping("/api/function-test")
public class FunctionTestController {

    @Autowired
    private AiChatService aiChatService;
    
    @Autowired
    private FunctionService functionService;

    /**
     * 测试工具调用功能
     */
    @PostMapping("/chat")
    public Result<ChatResponseDTO> testFunctionCalling(@RequestBody ChatRequestDTO chatRequest) {
        try {
            // 使用固定的测试用户ID
            Long testUserId = 1L;
            
            log.info("测试工具调用，用户消息: {}", chatRequest.getMessage());
            
            ChatResponseDTO response = aiChatService.chat(chatRequest, testUserId);
            
            if (response.getSuccess()) {
                return Result.success(response);
            } else {
                return Result.error(response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("工具调用测试失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用的工具函数列表
     */
    @GetMapping("/functions")
    public Result<Object> getFunctions() {
        try {
            Object functions = functionService.getFunctionDefinitions();
            return Result.success(functions);
        } catch (Exception e) {
            log.error("获取工具函数列表失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试建议的对话内容
     */
    @GetMapping("/test-suggestions")
    public Result<Map<String, Object>> getTestSuggestions() {
        Map<String, Object> suggestions = Map.of(
            "基础功能", Map.of(
                "时间查询", "现在几点了？",
                "环境数据", "请获取当前的环境监控数据",
                "设备状态", "请查看所有设备的运行状态",
                "AI预测", "请获取机器学习模型的预测建议"
            ),
            "阶段二功能", Map.of(
                "历史趋势-温度", "请分析过去24小时的温度变化趋势",
                "历史趋势-湿度", "获取湿度的历史数据分析，包括统计信息和建议",
                "历史趋势-CO2", "分析CO2浓度的历史趋势，时间范围24小时",
                "环境模式-全面", "请对环境数据进行全面的模式分析",
                "环境模式-异常", "检测当前环境中的异常情况",
                "环境模式-相关性", "分析环境参数之间的相关性"
            ),
            "综合测试", Map.of(
                "智能分析", "请分析当前环境状况，包括历史趋势和异常检测，然后给出专业建议",
                "趋势预测", "基于温度和湿度的历史数据，预测未来的环境变化趋势",
                "设备优化", "分析环境数据模式，建议如何优化设备运行策略"
            )
        );
        return Result.success(suggestions);
    }
}
