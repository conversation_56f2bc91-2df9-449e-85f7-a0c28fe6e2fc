package com.agriguard.dto;


import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class UserLoginDTO {
    @NotBlank(message = "用户名/邮箱不能为空")
    private String account;

    @NotBlank(message = "密码不能为空")
    private String password;

    // // 新增邮箱绑定校验
    // @NotBlank(message = "绑定邮箱不能为空")
    // @Email(message = "邮箱格式不正确")
    // private String email;



}
