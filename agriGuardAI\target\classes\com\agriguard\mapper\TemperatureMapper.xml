<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.TemperatureMapper">

<!--    模拟华为云温度数据-->
<!--    更新温度实时状态表-->
    <update id="updateTempToRealTime" parameterType="com.agriguard.pojo.TemperatureRealTime">
        UPDATE temperature_real_time
        SET
        temp = #{temp},
        update_time = #{updateTime}
        WHERE device_id = #{deviceId}
    </update>
<!--    插入温度数据至温度缓存表-->
    <insert id="insertTempToDataCache" parameterType="com.agriguard.pojo.TemperatureDataCache">
        INSERT INTO temperature_data_cache (device_id, temp, update_time)
        VALUES (#{deviceId}, #{temp}, #{updateTime})
    </insert>

<!--    分析温度缓存数据-->
    <select id="getTemperatureStatsByTimeRange" resultType="com.agriguard.pojo.TemperatureDataHistory">
        SELECT
        device_id AS deviceId,
        MAX(temp) AS maxTemp,
        MIN(temp) AS minTemp,
        ROUND(AVG(temp), 1) AS avgTemp,
        COUNT(*) AS dataCount
        FROM temperature_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY device_id
    </select>

    <!-- 插入温度历史数据 -->
    <insert id="addTempDateHistory" parameterType="com.agriguard.pojo.TemperatureDataHistory">
        INSERT INTO temperature_data_history (
            device_id, collect_date, collect_hour,
            max_temp, min_temp, avg_temp, data_count, update_time
        ) VALUES (
                     #{deviceId}, #{collectDate}, #{collectHour},
                     #{maxTemp}, #{minTemp}, #{avgTemp}, #{dataCount}, #{updateTime}
                 )
    </insert>

    <!-- 清空温度缓存表 -->
    <delete id="deleteTempDateCacheByHour">
        DELETE FROM temperature_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
    </delete>


    <!--    查看温度设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM temperature_real_time WHERE device_id = #{deviceId}
    </select>

    <!--    增加温度传感器实时状态-->
    <insert id="addTempRealTime" parameterType="com.agriguard.pojo.TemperatureRealTime">
        INSERT INTO temperature_real_time
            (device_id, temp, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>

<!--    查看温度传感器详情-->
    <select id="findTempDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

<!--    查询历史数据-->
    <select id="findHistoryData" resultType="com.agriguard.pojo.TemperatureDataHistory">
        SELECT
            device_id AS deviceId,
            collect_date AS collectDate,
            collect_hour AS collectHour,
            max_temp AS maxTemp,
            min_temp AS minTemp,
            avg_temp AS avgTemp,
            data_count AS dataCount,
            update_time AS updateTime
        FROM temperature_data_history
        WHERE device_id = #{deviceId}
        <if test="start != null">
            AND update_time >= #{start}
        </if>
        <if test="end != null">
            AND update_time &lt; #{end}
        </if>
        ORDER BY update_time DESC
    </select>


    <!-- 查询温度传感器状态的结果映射 -->
    <resultMap id="TempStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="temp" property="temp" jdbcType="DECIMAL"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询温度传感器状态 -->
    <select id="findLatestStatus" resultMap="TempStatusResultMap">
        SELECT
        trt.device_id,
        d.device_name,
        d.place,
        trt.temp,
--        trt.update_time
        DATE_FORMAT(trt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        temperature_real_time trt
        JOIN
        device d ON trt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null and deviceId != ''">
            AND trt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        trt.update_time DESC
    </select>

    <!--    获取实时温度平均值-->
    <select id="getAvgTemperature" resultType="com.agriguard.entity.device.TempRealTimeByMinute">
        SELECT
        CONCAT(DATE_FORMAT(tdc.update_time, '%Y-%m-%d %H:%i'), ':00') AS update_time,
        AVG(tdc.temp) AS temp
        FROM
        temperature_data_cache tdc
        JOIN device d ON tdc.device_id = d.device_id
        WHERE
        1=1
        <if test="place != null">
            AND d.place = #{place}
        </if>
        AND tdc.update_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        AND tdc.update_time &lt;= NOW()
        GROUP BY
        update_time
        ORDER BY
        update_time ASC
    </select>

<!--    获取过去24小时的温度，便于绘制折线统计图-->

    <select id="getHistory24Hours" parameterType="String" resultType="com.agriguard.entity.device.TempHistory24">
        WITH hour_series AS (
        SELECT
        DATE_FORMAT(DATE_SUB(NOW(), INTERVAL n HOUR), '%Y-%m-%d') AS target_date,
        HOUR(DATE_SUB(NOW(), INTERVAL n HOUR)) AS target_hour
        FROM (
        SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION
        SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION
        SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION
        SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
        SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
        SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
        ) AS numbers
        ),
        devices_in_area AS (
        SELECT device_id
        FROM device
        <if test="place != null">
            WHERE place = #{place}
        </if>
        )
        SELECT
        hs.target_date AS collect_date,
        hs.target_hour AS collect_hour,
        COALESCE(MAX(t.max_temp), 0) AS max_temp,
        COALESCE(MIN(t.min_temp), 0) AS min_temp,
        COALESCE(ROUND(AVG(t.avg_temp), 1), 0) AS avg_temp
        FROM
        hour_series hs
        LEFT JOIN (
        SELECT
        collect_date,
        collect_hour,
        max_temp,
        min_temp,
        avg_temp
        FROM temperature_data_history
        WHERE device_id IN (SELECT device_id FROM devices_in_area)
        AND update_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ) t ON DATE_FORMAT(t.collect_date, '%Y-%m-%d') = hs.target_date
        AND t.collect_hour = hs.target_hour
        GROUP BY hs.target_date, hs.target_hour
        ORDER BY hs.target_date ASC, hs.target_hour ASC
    </select>

<!--    饼图数据获取sql-->
        <resultMap id="TempPieResultMap" type="com.agriguard.entity.device.TempPie">
            <result property="fanwei" column="fanwei"/>
            <result property="TempNum" column="TempNum"/>
        </resultMap>

        <select id="getHistory24HoursForPie" resultMap="TempPieResultMap">
            SELECT
            temp.fanwei,
            SUM(temp.data_count) AS TempNum
            FROM (
            SELECT
            tdh.data_count,
            CASE
            WHEN tdh.avg_temp >= 0 AND tdh.avg_temp &lt; 10 THEN 1
            WHEN tdh.avg_temp >= 10 AND tdh.avg_temp &lt; 20 THEN 2
            WHEN tdh.avg_temp >= 20 AND tdh.avg_temp &lt; 30 THEN 3
            WHEN tdh.avg_temp >= 30 AND tdh.avg_temp &lt; 40 THEN 4
            WHEN tdh.avg_temp >= 40 AND tdh.avg_temp &lt;= 50 THEN 5
            ELSE 0
            END AS fanwei
            FROM
            temperature_data_history tdh FORCE INDEX (idx_device_date)
            JOIN
            device d FORCE INDEX (PRIMARY) ON tdh.device_id = d.device_id
            WHERE
            1=1
            <if test="place != null">
                AND d.place = #{place}
            </if>
            AND (
            (tdh.collect_date = CURDATE() AND tdh.collect_hour &lt;= HOUR(NOW()))
            OR
            (tdh.collect_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_hour &gt;= HOUR(NOW()))
            OR
            (tdh.collect_date &gt; DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_date &lt; CURDATE())
            )
            ) AS temp
            GROUP BY temp.fanwei
            ORDER BY temp.fanwei
        </select>

<!--    获取AI温度参数-->
    <select id="getAiTemp" resultType="decimal">
    SELECT AVG(temp) AS average_temperature
    FROM temperature_real_time;
    </select>
</mapper>
