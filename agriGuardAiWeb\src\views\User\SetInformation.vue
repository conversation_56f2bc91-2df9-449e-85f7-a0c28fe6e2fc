<template>
  <div class="user-center-container">
    <!-- 用户信息主卡片 -->
    <div class="user-card">
      <div class="user-avatar">
        <el-avatar :size="120" :src="userInfo.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
        <el-button class="avatar-edit-btn" type="primary" circle @click="showAvatarDialog = true">
          <el-icon><Edit /></el-icon>
        </el-button>
      </div>

      <div class="user-info">
        <h2 class="username">{{ userInfo.user_name }}</h2>
        <p class="realname">{{ userInfo.real_name || '未设置真实姓名' }}</p>

        <div class="info-grid">
          <div class="info-item">
            <el-icon class="info-icon"><Message /></el-icon>
            <span class="info-label">邮箱</span>
            <span class="info-value">{{ userInfo.email || '未设置' }}</span>
          </div>
          <div class="info-item">
            <el-icon class="info-icon"><Iphone /></el-icon>
            <span class="info-label">电话</span>
            <span class="info-value">{{ userInfo.phone || '未设置' }}</span>
          </div>
          <div class="info-item">
            <el-icon class="info-icon"><Calendar /></el-icon>
            <span class="info-label">注册时间</span>
            <span class="info-value">{{ formatDate(userInfo.create_date) }}</span>
          </div>
          <div class="info-item">
            <el-icon class="info-icon"><Clock /></el-icon>
            <span class="info-label">最后登录</span>
            <span class="info-value">{{ formatDate(userInfo.login_date) }}</span>
          </div>
        </div>

        <div class="action-buttons">
          <el-button type="primary" @click="showEditDialog = true" round>
            <el-icon><Edit /></el-icon>
            <span>编辑资料</span>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 账号安全卡片 -->
    <el-card class="security-card">
      <template #header>
        <div class="card-header">
          <el-icon><Lock /></el-icon>
          <span>账号安全</span>
        </div>
      </template>

      <div class="security-items">
        <!-- 修改密码 -->
        <el-card class="security-item" shadow="hover" @click="showPasswordDialog = true">
          <div class="item-content">
            <div class="item-icon">
              <el-icon><Key /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-title">修改密码</div>
              <div class="item-desc">定期修改密码有助于账号安全</div>
            </div>
            <div class="item-action">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>

        <!-- 绑定手机 -->
        <el-card class="security-item" shadow="hover" @click="handleBindPhone">
          <div class="item-content">
            <div class="item-icon">
              <el-icon><Iphone /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-title">绑定手机</div>
              <div class="item-desc">{{ userInfo.phone ? '已绑定: ' + userInfo.phone : '未绑定手机号' }}</div>
            </div>
            <div class="item-action">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>

        <!-- 绑定邮箱 -->
        <el-card class="security-item" shadow="hover" @click="handleBindEmail">
          <div class="item-content">
            <div class="item-icon">
              <el-icon><Message /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-title">绑定邮箱</div>
              <div class="item-desc">{{ userInfo.email ? '已绑定: ' + userInfo.email : '未绑定邮箱' }}</div>
            </div>
            <div class="item-action">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>

        <!-- 退出登录 -->
        <el-card class="security-item" shadow="hover" @click="handleLogout">
          <div class="item-content">
            <div class="item-icon">
              <el-icon><SwitchButton /></el-icon>
            </div>
            <div class="item-info">
              <div class="item-title">退出登录</div>
              <div class="item-desc">安全退出当前账号</div>
            </div>
            <div class="item-action">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 编辑信息对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑个人信息" width="500px">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="80px">
        <el-form-item label="用户名" prop="user_name">
          <el-input v-model="editForm.user_name" disabled />
        </el-form-item>
        <el-form-item label="真实姓名" prop="real_name">
          <el-input v-model="editForm.real_name" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="500px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="80px">
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="submitPasswordForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 更换头像对话框 -->
    <el-dialog v-model="showAvatarDialog" title="更换头像" width="400px">
      <div class="avatar-uploader">
        <el-upload
            class="avatar-upload"
            action="#"
            :show-file-list="false"
            :on-change="handleAvatarChange"
            :auto-upload="false"
        >
          <img v-if="avatarPreview" :src="avatarPreview" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAvatarDialog = false">取消</el-button>
          <el-button type="primary" @click="uploadAvatar">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Edit, Lock, Message, Iphone, Calendar, Clock, Plus,
  Key, ArrowRight, SwitchButton
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import {useAuthStore} from "@/stores/auth.js";
import router from "@/router/index.js";
const AuthStore = useAuthStore()

// 用户信息
const userInfo = ref({
  user_id: 1,
  user_name: AuthStore.user?.userName,
  real_name: AuthStore.user?.realName,
  email: AuthStore.user?.email,
  phone: AuthStore.user?.phone,
  create_date: '2023-01-01 10:00:00',
  update_date: '2023-06-15 15:30:00',
  login_date: '2025-06-20 09:15:00',
  avatar: ''
})

// 对话框控制
const showEditDialog = ref(false)
const showPasswordDialog = ref(false)
const showAvatarDialog = ref(false)

// 表单引用
const editFormRef = ref(null)
const passwordFormRef = ref(null)

// 编辑表单
const editForm = reactive({
  user_name: '',
  real_name: '',
  email: '',
  phone: ''
})

// 编辑表单验证规则
const editRules = reactive({
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码表单验证规则
const passwordRules = reactive({
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
})

// 头像上传相关
const avatarPreview = ref('')
const avatarFile = ref(null)

// 初始化数据
onMounted(() => {
  // API: 获取用户信息
  // getUserInfo()

  // 初始化编辑表单
  editForm.user_name = userInfo.value.user_name
  editForm.real_name = userInfo.value.real_name
  editForm.email = userInfo.value.email
  editForm.phone = userInfo.value.phone
})

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 密码验证
function validatePassword(rule, value, callback) {
  if (value === passwordForm.oldPassword) {
    callback(new Error('新密码不能与原密码相同'))
  } else {
    callback()
  }
}

// 确认密码验证
function validateConfirmPassword(rule, value, callback) {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 提交编辑表单
const submitEditForm = () => {
  editFormRef.value.validate((valid) => {
    if (valid) {
      // 模拟API调用
      setTimeout(() => {
        userInfo.value.real_name = editForm.real_name
        userInfo.value.email = editForm.email
        userInfo.value.phone = editForm.phone
        userInfo.value.update_date = new Date().toISOString()
        ElMessage.success('信息更新成功')
        showEditDialog.value = false
      }, 500)
    }
  })
}

// 提交密码表单
const submitPasswordForm = () => {
  passwordFormRef.value.validate((valid) => {
    if (valid) {
      // 模拟API调用
      setTimeout(() => {
        ElMessage.success('密码修改成功')
        showPasswordDialog.value = false
        passwordFormRef.value.resetFields()
      }, 500)
    }
  })
}

// 处理头像选择
const handleAvatarChange = (file) => {
  const isImage = file.raw.type.indexOf('image') !== -1
  const isLt2M = file.raw.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB')
    return false
  }

  avatarFile.value = file.raw
  avatarPreview.value = URL.createObjectURL(file.raw)
}

// 上传头像
const uploadAvatar = () => {
  if (!avatarFile.value) {
    ElMessage.warning('请选择头像图片')
    return
  }

  // 模拟API调用
  setTimeout(() => {
    userInfo.value.avatar = avatarPreview.value
    ElMessage.success('头像上传成功')
    showAvatarDialog.value = false
    avatarPreview.value = ''
    avatarFile.value = null
  }, 500)
}

// 绑定手机
const handleBindPhone = () => {
  ElMessage.info('绑定手机功能')
}

// 绑定邮箱
const handleBindEmail = () => {
  ElMessage.info('绑定邮箱功能')
}

// 安全退出
const handleLogout = () => {
  ElMessage.success('已安全退出')
  logout()
}

const logout = () => {
  AuthStore.clearUser()
  router.push('/login')
}
</script>

<style scoped>
.user-center-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 用户卡片样式 */
.user-card {
  display: flex;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.user-avatar {
  position: relative;
  margin-right: 40px;
}

.avatar-edit-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  transform: translate(30%, 30%);
  background-color: var(--el-color-primary);
  color: white;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 28px;
  margin: 0 0 8px;
  color: #333;
}

.realname {
  font-size: 16px;
  color: #666;
  margin: 0 0 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.7);
  padding: 12px 16px;
  border-radius: 8px;
}

.info-icon {
  margin-right: 10px;
  color: var(--el-color-primary);
  font-size: 18px;
}

.info-label {
  color: #666;
  margin-right: 8px;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

/* 账号安全卡片样式 */
.security-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 18px;
}

.card-header .el-icon {
  margin-right: 8px;
  color: var(--el-color-primary);
}

.security-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.security-item {
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.security-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.item-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.item-icon {
  margin-right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-icon .el-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #333;
}

.item-desc {
  font-size: 14px;
  color: #999;
}

.item-action .el-icon {
  color: #999;
  font-size: 18px;
}

/* 头像上传样式 */
.avatar-uploader {
  display: flex;
  justify-content: center;
}

.avatar-upload {
  width: 150px;
  height: 150px;
  border: 2px dashed var(--el-border-color);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}
</style>
