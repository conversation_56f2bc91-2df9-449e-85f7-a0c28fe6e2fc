package com.agriguard.service;

import com.agriguard.dto.BigDataScreen.*;

import java.util.List;

public interface BigDataScreenService {
    List<DeviceTypeDistribution> getDeviceTypeDistribution();

    List<TempHumidityByPeriod> getHourlyAvgTempHumidity();

    List<SensorCountDTO> getSensorCounts();

    AvgAirPressureDTO getAvgAirPressure();

    List<GasConcentrationByPeriod> getHourlyAvgGasConcentration();

    List<WarehouseEquipmentCount> getWarehouseEquipmentCount();
}
