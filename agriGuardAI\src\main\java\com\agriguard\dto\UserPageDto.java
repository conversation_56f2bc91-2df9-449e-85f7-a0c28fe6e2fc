package com.agriguard.dto;

import com.agriguard.entity.User;
import lombok.Data;

import java.util.List;

@Data
public class UserPageDto {
    private Integer userId;         // 改为Long类型与数据库BIGINT匹配
    private String userName;
    private String realName;
    private String email;
    private String phone;
    private List<String> roles;

    // 全参构造函数（必须与查询结果字段类型完全匹配）
    public UserPageDto(
            Integer userId,
            String userName,
            String realName,
            String email,
            String phone,
            List<String> roles) {
        this. userId =  userId;
        this.userName = userName;
        this.realName = realName;
        this.email = email;
        this.phone = phone;
        this.roles = roles;
    }

    // 必须保留无参构造函数
    public UserPageDto() {
    }
}
