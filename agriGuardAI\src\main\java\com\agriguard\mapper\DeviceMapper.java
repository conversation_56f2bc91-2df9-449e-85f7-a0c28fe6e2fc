package com.agriguard.mapper;

import com.agriguard.pojo.*;
import org.apache.ibatis.annotations.*;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface DeviceMapper {
    @Select("SELECT * FROM device WHERE device_id = #{id}")
    Device findById(Integer id);

    @Select("SELECT * FROM device")
    List<Device> findAll();

    @Insert("INSERT INTO device(device_id, device_name, type, place, model, factory, install_time, update_time, description) " +
            "VALUES(#{deviceId}, #{deviceName}, #{type}, #{place}, #{model}, #{factory}, #{installTime}, #{updateTime}, #{description})")
    void insertDevice(Device device);

    @Update("UPDATE device SET " +
            "device_name=#{deviceName}, " +
            "type=#{type}, " +
            "place=#{place}, " +
            "model=#{model}, " +
            "factory=#{factory}, " +
            "install_time=#{installTime}, " +
            "update_time=#{updateTime}, " +
            "description=#{description} " +
            "WHERE device_id=#{deviceId}")
    void updateDevice(Device device);

    @Delete("DELETE FROM device WHERE device_id = #{deviceId}")
    void deleteById(Integer deviceId);

    List<Device> find(Integer deviceId, String deviceName, String type, String place, String model, String factory, LocalDate installTime);

    @Select("SELECT DISTINCT place FROM device WHERE type = #{deviceType}")
    List<String> get_all_location_of_device(String deviceType);

    //查新地方的设备总数
    Integer getTotalCount(String real, String name,String place);

    //查询异常设备数量
    Integer getAbnormalCount(String real, String name,String place);
    //查询异常设备id
    List<Integer> getAbnormalDeviceIds(@Param("place") String place,
                                       @Param("real") String realTimeTable,
                                       @Param("name") String cacheTable);


    List<Device> getDevicesByDeviceIds(@Param("deviceIds") List<Integer> deviceIds);
    <T> List<T> getRealTimeData(@Param("tableName") String tableName, @Param("place") String place);
}
