package com.agriguard.service.aichat.function;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工具函数管理服务
 * 负责注册、管理和执行工具函数
 */
@Slf4j
@Service
public class FunctionService {
    
    private final Map<String, FunctionExecutor> functionExecutors = new HashMap<>();
    
    /**
     * 注册工具函数执行器
     */
    @Autowired
    public void registerFunctionExecutors(List<FunctionExecutor> executors) {
        for (FunctionExecutor executor : executors) {
            FunctionDefinition definition = executor.getFunctionDefinition();
            functionExecutors.put(definition.getName(), executor);
            log.info("注册工具函数: {}", definition.getName());
        }
    }
    
    /**
     * 获取所有可用的工具函数定义
     * 
     * @return 工具函数定义列表，OpenAI格式
     */
    public JSONArray getFunctionDefinitions() {
        JSONArray functions = new JSONArray();
        
        for (FunctionExecutor executor : functionExecutors.values()) {
            FunctionDefinition definition = executor.getFunctionDefinition();
            
            JSONObject function = new JSONObject();
            function.put("type", "function");
            
            JSONObject functionInfo = new JSONObject();
            functionInfo.put("name", definition.getName());
            functionInfo.put("description", definition.getDescription());
            functionInfo.put("parameters", definition.getParameters());
            
            function.put("function", functionInfo);
            functions.add(function);
        }
        
        return functions;
    }
    
    /**
     * 执行工具函数
     * 
     * @param functionName 函数名称
     * @param parameters 函数参数
     * @param userId 用户ID
     * @return 执行结果
     */
    public Object executeFunction(String functionName, JSONObject parameters, Long userId) {
        try {
            FunctionExecutor executor = functionExecutors.get(functionName);
            if (executor == null) {
                throw new IllegalArgumentException("未找到工具函数: " + functionName);
            }
            
            // 验证参数
            if (!executor.validateParameters(parameters)) {
                throw new IllegalArgumentException("参数验证失败");
            }
            
            // 检查权限
            if (!executor.checkPermission(userId)) {
                throw new SecurityException("权限不足");
            }
            
            // 执行函数
            Object result = executor.execute(parameters, userId);
            
            log.info("工具函数执行成功: {} -> {}", functionName, JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("工具函数执行失败: {}", functionName, e);
            return Map.of("error", e.getMessage());
        }
    }
    
    /**
     * 检查函数是否存在
     */
    public boolean hasFunction(String functionName) {
        return functionExecutors.containsKey(functionName);
    }
    
    /**
     * 获取函数定义
     */
    public FunctionDefinition getFunctionDefinition(String functionName) {
        FunctionExecutor executor = functionExecutors.get(functionName);
        return executor != null ? executor.getFunctionDefinition() : null;
    }
}
