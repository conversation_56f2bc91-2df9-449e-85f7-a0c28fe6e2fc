package com.agriguard.service.aichat.function.impl;

import com.agriguard.service.aichat.function.FunctionDefinition;
import com.agriguard.service.aichat.function.FunctionExecutor;
import com.agriguard.service.impl.AiCommandServiceImpl;
import com.agriguard.service.MlPredictionService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 获取AI预测结果和建议的工具函数
 * 基于当前环境数据进行机器学习预测
 */
@Slf4j
@Component
public class GetMlPredictionResultFunction implements FunctionExecutor {
    
    @Autowired
    private AiCommandServiceImpl aiCommandService;
    
    @Autowired
    private MlPredictionService mlPredictionService;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public FunctionDefinition getFunctionDefinition() {
        // 定义参数Schema
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // 是否包含推理过程参数（可选）
        Map<String, Object> includeReasoningParam = new HashMap<>();
        includeReasoningParam.put("type", "boolean");
        includeReasoningParam.put("description", "是否包含预测推理过程，默认true");
        includeReasoningParam.put("default", true);
        properties.put("include_reasoning", includeReasoningParam);
        
        // 返回格式参数（可选）
        Map<String, Object> formatParam = new HashMap<>();
        formatParam.put("type", "string");
        formatParam.put("enum", Arrays.asList("detailed", "summary"));
        formatParam.put("description", "返回格式，详细或摘要，默认detailed");
        formatParam.put("default", "detailed");
        properties.put("format", formatParam);
        
        parameters.put("properties", properties);
        parameters.put("required", new String[]{}); // 没有必需参数
        
        return FunctionDefinition.builder()
                .name("get_ml_prediction_result")
                .description("获取机器学习模型的预测结果和设备控制建议")
                .parameters(parameters)
                .requiresAuth(false)
                .permissionLevel("read")
                .requiresConfirmation(false)
                .build();
    }
    
    @Override
    public Object execute(JSONObject parameters, Long userId) throws Exception {
        try {
            // 获取参数
            Boolean includeReasoning = parameters.getBoolean("include_reasoning");
            String format = parameters.getString("format");
            
            // 处理默认值
            if (includeReasoning == null) {
                includeReasoning = true;
            }
            if (format == null || format.trim().isEmpty()) {
                format = "detailed";
            }
            
            log.info("获取ML预测结果，参数: includeReasoning={}, format={}, userId={}", 
                    includeReasoning, format, userId);
            
            // 获取当前环境参数
            BigDecimal temperature = aiCommandService.getAiTemp();
            Integer humidity = aiCommandService.getAiHum();
            Integer ammonia = aiCommandService.getAiAmm();
            Integer co2 = aiCommandService.getAiCo2();
            BigDecimal internalPressure = aiCommandService.getInAiAirPressure();
            BigDecimal externalPressure = aiCommandService.getOutAiAirPressure();
            
            // 调用ML预测服务
            Integer[] predictions = mlPredictionService.predictControlCommands(
                    temperature, humidity, ammonia, co2, internalPressure, externalPressure);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("timestamp", LocalDateTime.now().format(FORMATTER));
            result.put("request_params", Map.of(
                    "include_reasoning", includeReasoning,
                    "format", format
            ));
            
            // 构建预测结果
            Map<String, Object> prediction = new HashMap<>();
            
            // 环境输入数据
            Map<String, Object> environmentInput = new HashMap<>();
            environmentInput.put("temperature", temperature);
            environmentInput.put("humidity", humidity);
            environmentInput.put("co2", co2);
            environmentInput.put("ammonia", ammonia);
            environmentInput.put("internal_pressure", internalPressure);
            environmentInput.put("external_pressure", externalPressure);
            prediction.put("environment_input", environmentInput);
            
            // 预测建议
            Map<String, Object> recommendations = buildRecommendations(predictions, includeReasoning, 
                    temperature, humidity, co2, ammonia, internalPressure, externalPressure);
            prediction.put("recommendations", recommendations);
            
            // TODO: 从ML服务获取真实的预测置信度，替换硬编码值
            prediction.put("confidence", 0.85); // 临时硬编码值，应从ML服务返回
            
            // ML服务状态
            prediction.put("ml_service_status", "healthy");
            
            // 预测摘要
            if ("summary".equals(format)) {
                prediction.put("summary", generateSummary(predictions, environmentInput));
            }
            
            result.put("prediction", prediction);
            result.put("data_source", "AgriGuard机器学习预测系统");
            result.put("status", "success");
            
            log.info("ML预测结果获取成功，预测命令: {}", Arrays.toString(predictions));
            
            return result;
            
        } catch (Exception e) {
            log.error("获取ML预测结果失败", e);
            throw new Exception("获取ML预测结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建预测建议
     */
    private Map<String, Object> buildRecommendations(Integer[] predictions, boolean includeReasoning,
                                                     BigDecimal temperature, Integer humidity, Integer co2, 
                                                     Integer ammonia, BigDecimal internalPressure, BigDecimal externalPressure) {
        Map<String, Object> recommendations = new HashMap<>();
        
        // 预测结果数组含义：[大风机1状态, 大风机2状态, 水帘状态, 加热片状态, 窗户状态, 小风扇1状态, 小风扇2状态]
        if (predictions != null && predictions.length >= 5) {
            // 大风机1
            Map<String, Object> fan1 = new HashMap<>();
            fan1.put("action", predictions[0]);
            fan1.put("action_text", predictions[0] == 1 ? "开启" : "关闭");
            if (includeReasoning) {
                fan1.put("reason", generateFanReason(predictions[0], temperature, humidity));
            }
            recommendations.put("fan1", fan1);
            
            // 大风机2
            Map<String, Object> fan2 = new HashMap<>();
            fan2.put("action", predictions[1]);
            fan2.put("action_text", predictions[1] == 1 ? "开启" : "关闭");
            if (includeReasoning) {
                fan2.put("reason", generateFanReason(predictions[1], temperature, humidity));
            }
            recommendations.put("fan2", fan2);
            
            // 水帘
            Map<String, Object> nappe = new HashMap<>();
            nappe.put("action", predictions[2]);
            nappe.put("action_text", predictions[2] == 1 ? "开启" : "关闭");
            if (includeReasoning) {
                nappe.put("reason", generateNappeReason(predictions[2], temperature, humidity));
            }
            recommendations.put("nappe", nappe);
            
            // 加热片
            Map<String, Object> heat = new HashMap<>();
            heat.put("action", predictions[3]);
            heat.put("action_text", predictions[3] == 1 ? "开启" : "关闭");
            if (includeReasoning) {
                heat.put("reason", generateHeatReason(predictions[3], temperature));
            }
            recommendations.put("heat", heat);
            
            // 窗户
            Map<String, Object> window = new HashMap<>();
            window.put("action", predictions[4]);
            window.put("action_text", predictions[4] == 1 ? "开启" : "关闭");
            if (includeReasoning) {
                window.put("reason", generateWindowReason(predictions[4], co2, ammonia));
            }
            recommendations.put("window", window);
            
            // 如果有小风扇数据
            if (predictions.length >= 7) {
                Map<String, Object> smallFan1 = new HashMap<>();
                smallFan1.put("action", predictions[5]);
                smallFan1.put("action_text", predictions[5] == 1 ? "开启" : "关闭");
                if (includeReasoning) {
                    smallFan1.put("reason", generateSmallFanReason(predictions[5], temperature, humidity));
                }
                recommendations.put("small_fan1", smallFan1);
                
                Map<String, Object> smallFan2 = new HashMap<>();
                smallFan2.put("action", predictions[6]);
                smallFan2.put("action_text", predictions[6] == 1 ? "开启" : "关闭");
                if (includeReasoning) {
                    smallFan2.put("reason", generateSmallFanReason(predictions[6], temperature, humidity));
                }
                recommendations.put("small_fan2", smallFan2);
            }
        }
        
        return recommendations;
    }
    
    /**
     * 生成预测摘要
     */
    private String generateSummary(Integer[] predictions, Map<String, Object> environmentInput) {
        if (predictions == null || predictions.length < 5) {
            return "预测数据不完整";
        }
        
        int activeDevices = 0;
        List<String> activeDeviceNames = new ArrayList<>();
        
        if (predictions[0] == 1) { activeDevices++; activeDeviceNames.add("大风机1"); }
        if (predictions[1] == 1) { activeDevices++; activeDeviceNames.add("大风机2"); }
        if (predictions[2] == 1) { activeDevices++; activeDeviceNames.add("水帘"); }
        if (predictions[3] == 1) { activeDevices++; activeDeviceNames.add("加热片"); }
        if (predictions[4] == 1) { activeDevices++; activeDeviceNames.add("窗户"); }
        
        StringBuilder summary = new StringBuilder();
        summary.append("基于当前环境条件，建议开启 ").append(activeDevices).append(" 个设备");
        if (!activeDeviceNames.isEmpty()) {
            summary.append("：").append(String.join("、", activeDeviceNames));
        }
        summary.append("。");
        
        return summary.toString();
    }
    
    // 推理原因生成方法
    private String generateFanReason(Integer action, BigDecimal temperature, Integer humidity) {
        if (action == 1) {
            if (temperature != null && temperature.doubleValue() > 28) {
                return "温度偏高(" + temperature + "°C)，需要通风降温";
            }
            if (humidity != null && humidity > 75) {
                return "湿度偏高(" + humidity + "%)，需要通风除湿";
            }
            return "环境条件需要通风调节";
        } else {
            return "当前环境条件适宜，无需开启风机";
        }
    }
    
    private String generateNappeReason(Integer action, BigDecimal temperature, Integer humidity) {
        if (action == 1) {
            if (temperature != null && temperature.doubleValue() > 30) {
                return "温度过高(" + temperature + "°C)，需要水帘降温";
            }
            if (humidity != null && humidity < 50) {
                return "湿度偏低(" + humidity + "%)，需要水帘增湿";
            }
            return "环境条件需要水帘调节";
        } else {
            return "当前环境条件适宜，无需开启水帘";
        }
    }
    
    private String generateHeatReason(Integer action, BigDecimal temperature) {
        if (action == 1) {
            if (temperature != null && temperature.doubleValue() < 18) {
                return "温度偏低(" + temperature + "°C)，需要加热";
            }
            return "环境温度需要提升";
        } else {
            return "当前温度适宜，无需加热";
        }
    }
    
    private String generateWindowReason(Integer action, Integer co2, Integer ammonia) {
        if (action == 1) {
            if (co2 != null && co2 > 1800) {
                return "CO2浓度偏高(" + co2 + "ppm)，需要开窗通风";
            }
            if (ammonia != null && ammonia > 20) {
                return "氨气浓度偏高(" + ammonia + "ppm)，需要开窗通风";
            }
            return "空气质量需要改善，建议开窗通风";
        } else {
            return "当前空气质量良好，无需开窗";
        }
    }
    
    private String generateSmallFanReason(Integer action, BigDecimal temperature, Integer humidity) {
        if (action == 1) {
            return "需要局部通风调节";
        } else {
            return "当前无需局部通风";
        }
    }
    
    @Override
    public boolean validateParameters(JSONObject parameters) {
        // 参数验证
        try {
            String format = parameters.getString("format");
            if (format != null) {
                List<String> validFormats = Arrays.asList("detailed", "summary");
                if (!validFormats.contains(format.toLowerCase())) {
                    log.warn("无效的格式类型: {}", format);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("参数验证失败", e);
            return false;
        }
    }
    
    @Override
    public boolean checkPermission(Long userId) {
        // ML预测结果查询不需要特殊权限
        return true;
    }
}
