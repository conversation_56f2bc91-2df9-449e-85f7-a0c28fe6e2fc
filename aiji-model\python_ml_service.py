"""
家禽养殖场机器学习预测API服务
基于Flask的高性能HTTP API服务，提供机器学习预测接口

API接口：
- POST /predict: 单次预测接口
- POST /batch-predict: 批量预测接口
- GET /health: 健康检查接口
- GET /docs: API文档接口
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import pandas as pd
import numpy as np
from ml_training_pipeline import PoultryFarmMLPipeline
import logging
import time
import threading
from functools import wraps
from datetime import datetime
import os
import sys
import argparse

# 配置日志 - 解决Windows控制台Unicode编码问题
def setup_logging():
    """设置日志配置，解决Unicode编码问题"""
    # 创建logs目录（如果不存在）
    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        print(f"创建日志目录: {logs_dir}/")

    # 创建自定义格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 文件处理器 - 使用UTF-8编码，日志文件保存到logs目录
    log_file_path = os.path.join(logs_dir, 'ml_service.log')
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_handler.setFormatter(formatter)

    # 控制台处理器 - 处理Windows控制台编码问题
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # 在Windows系统上设置控制台编码
    if sys.platform.startswith('win'):
        try:
            # 尝试设置控制台为UTF-8
            os.system('chcp 65001 >nul 2>&1')
        except:
            pass

    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    return logging.getLogger(__name__)

logger = setup_logging()

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量
predictor = None
service_start_time = time.time()
request_count = 0
request_count_lock = threading.Lock()

class PoultryFarmMLService:
    """机器学习预测服务类"""

    def __init__(self):
        """初始化服务"""
        self.pipeline = PoultryFarmMLPipeline()
        self.load_model()

        # 控制系统状态描述
        self.state_descriptions = {
            '大风机1状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'},
            '大风机2状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'},
            '水帘状态': {0: '关闭', 1: '开启'},
            '加热片状态': {0: '关闭', 1: '开启'},
            '窗户状态': {0: '关闭', 1: '开启'},
            '小风扇1状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'},
            '小风扇2状态': {0: '关闭', 1: '低速', 2: '中速', 3: '高速'}
        }

        logger.info("机器学习服务初始化完成")
    
    def load_model(self):
        """加载训练好的模型"""
        try:
            self.pipeline.load_model()
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise
    
    def predict(self, temperature, humidity, ammonia, co2, internal_pressure, external_pressure):
        """
        进行预测

        参数:
            temperature: 温度（摄氏度）
            humidity: 湿度百分比
            ammonia: 氨气浓度
            co2: 二氧化碳浓度
            internal_pressure: 内气压
            external_pressure: 外气压

        返回:
            dict: 预测结果
        """
        try:
            # 准备输入数据
            input_data = {
                '温度': float(temperature),
                '湿度': float(humidity),
                '氨气浓度': float(ammonia),
                '二氧化碳浓度': float(co2),
                '内气压': float(internal_pressure),
                '外气压': float(external_pressure)
            }

            # 进行预测
            predictions = self.pipeline.predict_new_data(input_data)

            # 格式化结果
            result = {
                'success': True,
                'predictions': {},
                'timestamp': time.time()
            }

            # 添加中文描述
            for chinese_name, state in predictions.items():
                state_value = int(state)
                description = self.state_descriptions[chinese_name].get(state_value, '未知')

                result['predictions'][chinese_name] = {
                    'state': state_value,
                    'description': description
                }

            return result

        except Exception as e:
            logger.error(f"预测过程中发生错误: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': time.time()
            }

def timing_decorator(f):
    """计时装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        global request_count
        with request_count_lock:
            request_count += 1

        start_time = time.time()
        result = f(*args, **kwargs)
        end_time = time.time()
        processing_time = (end_time - start_time) * 1000

        logger.info(f"API调用 {f.__name__} 耗时: {processing_time:.2f}ms")

        # 如果返回的是jsonify对象，添加处理时间
        if hasattr(result, 'get_json') and result.get_json():
            data = result.get_json()
            if isinstance(data, dict):
                data['processing_time_ms'] = round(processing_time, 2)

        return result
    return decorated_function

def validate_environment_data(data):
    """验证环境数据参数"""
    required_params = ['temperature', 'humidity', 'ammonia', 'co2', 'internal_pressure', 'external_pressure']

    # 检查必需参数
    missing_params = [param for param in required_params if param not in data]
    if missing_params:
        return False, f'缺少必需参数: {", ".join(missing_params)}'

    # 参数范围验证
    validations = [
        ('temperature', data['temperature'], -50, 60, '温度'),
        ('humidity', data['humidity'], 0, 100, '湿度'),
        ('ammonia', data['ammonia'], 0, 100, '氨气浓度'),
        ('co2', data['co2'], 0, 10000, '二氧化碳浓度'),
        ('internal_pressure', data['internal_pressure'], 90, 110, '内气压'),
        ('external_pressure', data['external_pressure'], 90, 110, '外气压')
    ]

    for param_name, value, min_val, max_val, chinese_name in validations:
        try:
            float_val = float(value)
            if not (min_val <= float_val <= max_val):
                return False, f'{chinese_name}超出有效范围 [{min_val}, {max_val}]，当前值: {float_val}'
        except (ValueError, TypeError):
            return False, f'{chinese_name}必须是数字，当前值: {value}'

    return True, None

@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口

    返回:
        JSON: 服务状态信息
    """
    uptime = time.time() - service_start_time
    return jsonify({
        'status': 'healthy',
        'service': 'poultry-farm-ml-service',
        'version': '1.0.0',
        'uptime_seconds': round(uptime, 2),
        'total_requests': request_count,
        'model_loaded': predictor is not None,
        'timestamp': time.time()
    })

@app.route('/predict', methods=['POST'])
@timing_decorator
def predict_endpoint():
    """
    单次预测接口

    请求体:
        {
            "temperature": 25.0,
            "humidity": 60.0,
            "ammonia": 15.0,
            "co2": 1500.0,
            "internal_pressure": 101.3,
            "external_pressure": 101.5
        }

    返回:
        JSON: 预测结果
    """
    try:
        # 检查模型是否已加载
        if predictor is None:
            return jsonify({
                'success': False,
                'error': '模型未加载，请检查服务状态'
            }), 503

        # 验证请求数据
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': '请求必须是JSON格式'
            }), 400

        data = request.get_json()

        # 验证参数
        is_valid, error_msg = validate_environment_data(data)
        if not is_valid:
            return jsonify({
                'success': False,
                'error': error_msg
            }), 400

        # 进行预测
        result = predictor.predict(
            data['temperature'],
            data['humidity'],
            data['ammonia'],
            data['co2'],
            data['internal_pressure'],
            data['external_pressure']
        )

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"预测接口发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500

@app.route('/batch-predict', methods=['POST'])
@timing_decorator
def batch_predict_endpoint():
    """
    批量预测接口

    请求体:
        {
            "samples": [
                {
                    "temperature": 25.0,
                    "humidity": 60.0,
                    "ammonia": 15.0,
                    "co2": 1500.0,
                    "internal_pressure": 101.3,
                    "external_pressure": 101.5
                },
                ...
            ]
        }

    返回:
        JSON: 批量预测结果
    """
    try:
        # 检查模型是否已加载
        if predictor is None:
            return jsonify({
                'success': False,
                'error': '模型未加载，请检查服务状态'
            }), 503

        if not request.is_json:
            return jsonify({
                'success': False,
                'error': '请求必须是JSON格式'
            }), 400

        data = request.get_json()

        if 'samples' not in data or not isinstance(data['samples'], list):
            return jsonify({
                'success': False,
                'error': '请求必须包含samples数组'
            }), 400

        if len(data['samples']) > 100:
            return jsonify({
                'success': False,
                'error': '批量预测最多支持100个样本'
            }), 400

        results = []
        successful_count = 0

        for i, sample in enumerate(data['samples']):
            try:
                # 验证单个样本
                is_valid, error_msg = validate_environment_data(sample)
                if not is_valid:
                    results.append({
                        'success': False,
                        'error': f'样本{i+1}: {error_msg}',
                        'sample_index': i
                    })
                    continue

                result = predictor.predict(
                    sample['temperature'],
                    sample['humidity'],
                    sample['ammonia'],
                    sample['co2'],
                    sample['internal_pressure'],
                    sample['external_pressure']
                )
                result['sample_index'] = i
                results.append(result)

                if result['success']:
                    successful_count += 1

            except Exception as e:
                results.append({
                    'success': False,
                    'error': f'样本{i+1}处理错误: {str(e)}',
                    'sample_index': i
                })

        return jsonify({
            'success': True,
            'results': results,
            'total_samples': len(data['samples']),
            'successful_predictions': successful_count,
            'failed_predictions': len(data['samples']) - successful_count
        })

    except Exception as e:
        logger.error(f"批量预测接口发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500

@app.route('/docs', methods=['GET'])
def api_docs():
    """
    API文档接口

    返回:
        HTML: API使用文档
    """
    docs_html = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>家禽养殖场机器学习预测API文档</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            h1, h2, h3 { color: #333; }
            .endpoint { background: #f4f4f4; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .method { background: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
            .method.post { background: #28a745; }
            .method.get { background: #17a2b8; }
            pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
            .example { background: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <h1>家禽养殖场机器学习预测API文档</h1>
        <p>版本: 1.0.0</p>
        <p>基于机器学习的家禽养殖场环境控制系统预测服务</p>

        <h2>API接口列表</h2>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /health</h3>
            <p><strong>功能:</strong> 健康检查接口</p>
            <p><strong>返回:</strong> 服务状态信息</p>
            <div class="example">
                <strong>示例响应:</strong>
                <pre>{
  "status": "healthy",
  "service": "poultry-farm-ml-service",
  "version": "1.0.0",
  "uptime_seconds": 3600.5,
  "total_requests": 150,
  "model_loaded": true,
  "timestamp": **********.123
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /predict</h3>
            <p><strong>功能:</strong> 单次预测接口</p>
            <p><strong>输入参数:</strong></p>
            <ul>
                <li>temperature: 温度（-50°C 到 60°C）</li>
                <li>humidity: 湿度（0% 到 100%）</li>
                <li>ammonia: 氨气浓度（0 到 100）</li>
                <li>co2: 二氧化碳浓度（0 到 10000）</li>
                <li>internal_pressure: 内气压（90 到 110）</li>
                <li>external_pressure: 外气压（90 到 110）</li>
            </ul>
            <div class="example">
                <strong>请求示例:</strong>
                <pre>curl -X POST http://localhost:5000/predict \\
  -H "Content-Type: application/json" \\
  -d '{
    "temperature": 25.0,
    "humidity": 60.0,
    "ammonia": 15.0,
    "co2": 1500.0,
    "internal_pressure": 101.3,
    "external_pressure": 101.5
  }'</pre>
            </div>
            <div class="example">
                <strong>响应示例:</strong>
                <pre>{
  "success": true,
  "timestamp": **********.123,
  "processing_time_ms": 45.2,
  "predictions": {
    "大风机1状态": {"state": 2, "description": "中速"},
    "大风机2状态": {"state": 1, "description": "低速"},
    "水帘状态": {"state": 1, "description": "开启"},
    "加热片状态": {"state": 0, "description": "关闭"},
    "窗户状态": {"state": 1, "description": "开启"},
    "小风扇1状态": {"state": 2, "description": "中速"},
    "小风扇2状态": {"state": 1, "description": "低速"}
  }
}</pre>
            </div>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /batch-predict</h3>
            <p><strong>功能:</strong> 批量预测接口（最多100个样本）</p>
            <div class="example">
                <strong>请求示例:</strong>
                <pre>curl -X POST http://localhost:5000/batch-predict \\
  -H "Content-Type: application/json" \\
  -d '{
    "samples": [
      {
        "temperature": 25.0,
        "humidity": 60.0,
        "ammonia": 15.0,
        "co2": 1500.0,
        "internal_pressure": 101.3,
        "external_pressure": 101.5
      },
      {
        "temperature": 30.0,
        "humidity": 70.0,
        "ammonia": 20.0,
        "co2": 2000.0,
        "internal_pressure": 101.0,
        "external_pressure": 101.2
      }
    ]
  }'</pre>
            </div>
        </div>

        <h2>错误码说明</h2>
        <ul>
            <li><strong>400 Bad Request:</strong> 请求参数错误或格式不正确</li>
            <li><strong>404 Not Found:</strong> 接口不存在</li>
            <li><strong>500 Internal Server Error:</strong> 服务器内部错误</li>
            <li><strong>503 Service Unavailable:</strong> 模型未加载或服务不可用</li>
        </ul>

        <h2>部署说明</h2>
        <ol>
            <li>确保已安装依赖: <code>pip install flask flask-cors pandas numpy scikit-learn joblib matplotlib</code></li>
            <li>确保模型文件存在: <code>models/poultry_farm_model.pkl</code></li>
            <li>启动服务: <code>python python_ml_service.py</code></li>
            <li>服务将在 http://localhost:5000 启动</li>
        </ol>

        <h2>性能指标</h2>
        <ul>
            <li>单次预测响应时间: < 500ms</li>
            <li>支持并发请求</li>
            <li>批量预测最多支持100个样本</li>
        </ul>
    </body>
    </html>
    """
    return docs_html

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    _ = error  # 忽略未使用参数警告
    return jsonify({
        'success': False,
        'error': '接口不存在',
        'available_endpoints': ['/health', '/predict', '/batch-predict', '/docs']
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    _ = error  # 忽略未使用参数警告
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500

def initialize_service():
    """初始化服务"""
    global predictor
    try:
        logger.info("正在初始化机器学习服务...")
        predictor = PoultryFarmMLService()
        logger.info("[成功] 服务初始化成功")
        return True
    except Exception as e:
        logger.error(f"[失败] 服务初始化失败: {str(e)}")
        logger.error("请确保以下条件满足:")
        logger.error("1. 已运行 python ml_training_pipeline.py 训练模型")
        logger.error("2. models/ 目录下存在模型文件")
        logger.error("3. 已安装所有必需的依赖包")
        return False

def check_dependencies():
    """检查依赖包是否已安装"""
    required_packages = [
        'flask', 'flask_cors', 'pandas', 'numpy',
        'sklearn', 'joblib', 'matplotlib'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        logger.error("缺少以下依赖包:")
        for package in missing_packages:
            logger.error(f"   - {package}")
        logger.error("请运行: pip install -r requirements.txt")
        return False

    return True

def check_model_files():
    """检查模型文件是否存在"""
    model_files = [
        'models/poultry_farm_model.pkl',
        'models/scaler.pkl',
        'models/metadata.pkl'
    ]

    missing_files = []
    for file_path in model_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        logger.error("缺少以下模型文件:")
        for file_path in missing_files:
            logger.error(f"   - {file_path}")
        logger.error("请先运行: python ml_training_pipeline.py")
        return False

    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='启动家禽养殖场机器学习预测服务')
    parser.add_argument('--port', type=int, default=5000, help='服务端口 (默认: 5000)')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务主机 (默认: 0.0.0.0)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')

    args = parser.parse_args()

    print("="*60)
    print("家禽养殖场机器学习预测API服务")
    print("版本: 1.0.0")
    print("="*60)

    # 检查依赖
    logger.info("检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    logger.info("[成功] 依赖包检查通过")

    # 检查模型文件
    logger.info("检查模型文件...")
    if not check_model_files():
        sys.exit(1)
    logger.info("[成功] 模型文件检查通过")

    # 初始化服务
    if initialize_service():
        logger.info(f"[启动] Flask服务器启动中...")
        logger.info(f"[信息] 服务地址: http://{args.host}:{args.port}")
        logger.info(f"[信息] API文档: http://localhost:{args.port}/docs")
        logger.info(f"[信息] 健康检查: http://localhost:{args.port}/health")
        logger.info(f"[信息] 预测接口: http://localhost:{args.port}/predict")

        try:
            app.run(
                host=args.host,
                port=args.port,
                debug=args.debug,
                threaded=True,
                use_reloader=False
            )
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭服务...")
        except Exception as e:
            logger.error(f"服务器运行错误: {str(e)}")
    else:
        logger.error("[失败] 服务初始化失败，无法启动服务器")
        sys.exit(1)

if __name__ == '__main__':
    main()
