import numpy as np
import pandas as pd
import argparse
from typing import Tuple, Dict, List
from datetime import datetime, timedelta
import random


class AdvancedPoultryFarmSimulator:
    """专业级养鸡场环境模拟数据生成器（优化版）"""

    def __init__(self, config_path: str = None):
        """初始化模拟器，可通过配置文件自定义参数范围和概率"""
        # 默认参数配置（基于现代化养鸡场实际情况优化）
        self.param_config = {
            # 环境参数范围（基于鸡舍实际环境）
            'environment': {
                'temperature': {'min': 14.0, 'max': 36.0, 'optimal': (18.0, 24.0), 'noise': 10.0},  # 调整温度范围和噪声
                'humidity': {'min': 40.0, 'max': 80.0, 'optimal': (50.0, 70.0), 'noise': 3.0},  # 调整湿度范围和噪声
                'ammonia': {'min': 5.0, 'max': 35.0, 'warning': 20.0, 'danger': 30.0, 'noise': 1.0},  # 调整氨气范围
                'co2': {'min': 800.0, 'max': 3000.0, 'warning': 2000.0, 'danger': 2000.0, 'noise': 30.0},  # 调整CO2范围
                'pressure_in': {'min': 100.5, 'max': 102.0, 'noise': 0.1},  # 调整气压范围和噪声
                'pressure_out': {'min': 100.0, 'max': 102.5, 'noise': 0.1}
            },

            # 设备配置（修改大风机数量为2）
            'equipment': {
                'big_fan_count': 2,    # 大风机数量改为2
                'small_fan_count': 2,   # 小风扇数量
                'window_count': 1,      # 窗户数量
                'curtain_count': 1,     # 水帘数量
                'heater_count': 1       # 加热片数量
            },

            # 设备控制逻辑参数
            'control_logic': {
                # 大风机控制参数（基于温度、氨气和CO2）
                'big_fan': {
                    'temp_thresholds': [20.0, 24.0, 28.0],  # 调整温度阈值
                    'gas_weight': 0.4,  # 有害气体对风机的影响权重
                    'min_off_time': 15,  # 最小关闭时间（分钟）
                    'min_on_time': 10,  # 最小开启时间（分钟）
                    'pressure_diff_threshold': 0.3  # 内外气压差阈值
                },

                # 小风扇控制参数
                'small_fan': {
                    'temp_thresholds': [18.0, 22.0, 26.0],  # 比大风机低2度
                    'gas_weight': 0.3,
                    'min_off_time': 10,
                    'min_on_time': 8
                },

                # 水帘控制参数
                'curtain': {
                    'temp_threshold': 28.0,  # 开启温度阈值
                    'humidity_threshold': 75.0,  # 最大允许湿度
                    'min_off_time': 30,
                    'min_on_time': 20
                },

                # 加热片控制参数（调整加热片逻辑）
                'heater': {
                    'temp_threshold': 18.0,  # 低于此温度开启
                    'temp_hysteresis': 2.0,  # 迟滞范围
                    'min_off_time': 20,
                    'min_on_time': 15
                },

                # 窗户控制参数
                'window': {
                    'temp_threshold': 20.0,  # 温度阈值
                    'gas_threshold': 0.7,  # 综合气体指数阈值
                    'min_off_time': 15,
                    'min_on_time': 10,
                    'pressure_diff_threshold': 0.2
                },

                # 昼夜模式参数
                'day_night': {
                    'day_start': 6,  # 早上6点
                    'night_start': 18,  # 晚上6点
                    'night_temp_offset': -3.0,  # 夜间温度偏移
                    'night_humidity_offset': 5.0
                },

                # 季节模式参数
                'season': {
                    'summer_temp_offset': 5.0,
                    'winter_temp_offset': -3.0,  # 减少冬季温度偏移
                    'summer_humidity_offset': 10.0,
                    'winter_humidity_offset': -5.0
                }
            },

            # 随机因素影响概率
            'random_factors': {
                'malfunction': 0.005,  # 设备故障概率
                'manual_override': 0.01,  # 人工干预概率
                'sensor_error': 0.005,  # 提高传感器误差概率
                'weather_impact': 0.15  # 提高天气影响概率
            },

            # 天气影响参数
            'weather_impact': {
                'hot': {'temp_increase': 5.0, 'humidity_decrease': 10.0},  # 增强炎热天气影响
                'cold': {'temp_decrease': 6.0, 'humidity_increase': 8.0},  # 增强寒冷天气影响
                'rainy': {'humidity_increase': 20.0, 'temp_decrease': 3.0},  # 增强雨天影响
                'windy': {'pressure_diff_increase': 0.3, 'temp_decrease': 2.0}  # 增加温度影响
            }
        }

        # 设备状态记忆（修改大风机状态列表长度为2）
        self.device_memory = {
            'big_fan': {'last_change': None, 'last_state': [0] * 2},  # 2个大风机
            'small_fan': {'last_change': None, 'last_state': [0] * 2},  # 2个小风扇
            'curtain': {'last_change': None, 'last_state': 0},  # 1个水帘
            'heater': {'last_change': None, 'last_state': 0},  # 1个加热片
            'window': {'last_change': None, 'last_state': [0] * 1}  # 1个窗户
        }

        # 当前时间（用于模拟昼夜和季节变化）
        self.current_time = datetime(2025, 4, 1, 6, 0)  # 从春季开始，便于测试更多场景
        self.current_season = "spring"  # 初始季节

        # 天气状态
        self.weather_state = "normal"
        self.weather_duration = 0

        # 如果提供了配置文件，则加载配置
        if config_path:
            self._load_config(config_path)

    def _load_config(self, config_path: str):
        """从配置文件加载参数（实现略）"""
        pass

    def _update_season(self):
        """根据当前时间更新季节"""
        month = self.current_time.month
        if month in [12, 1, 2]:
            self.current_season = "winter"
        elif month in [3, 4, 5]:
            self.current_season = "spring"
        elif month in [6, 7, 8]:
            self.current_season = "summer"
        else:
            self.current_season = "autumn"

    def _update_weather(self):
        """模拟天气变化"""
        if self.weather_duration > 0:
            self.weather_duration -= 1
            return

        if random.random() < self.param_config['random_factors']['weather_impact']:
            # 根据季节调整天气概率
            if self.current_season == "summer":
                weather_types = ["hot", "cold", "rainy", "windy", "normal"]
                weights = [0.4, 0.05, 0.4, 0.1, 0.05]  # 夏季更可能出现炎热和雨天
            elif self.current_season == "winter":
                weather_types = ["hot", "cold", "rainy", "windy", "normal"]
                weights = [0.05, 0.5, 0.1, 0.3, 0.05]  # 冬季更可能出现寒冷和刮风
            else:  # 春秋季
                weather_types = ["hot", "cold", "rainy", "windy", "normal"]
                weights = [0.2, 0.2, 0.3, 0.2, 0.1]  # 更均匀的天气分布

            self.weather_state = random.choices(weather_types, weights=weights)[0]
            self.weather_duration = random.randint(3, 10)  # 持续3-10个时间周期
        else:
            self.weather_state = "normal"
            self.weather_duration = 0

    def _generate_environment_params(self) -> Tuple[float, float, float, float, float, float]:
        """生成一组环境参数（专业优化版，考虑季节、昼夜和天气变化）"""
        # 更新季节和天气
        self._update_season()
        self._update_weather()

        # 判断当前是白天还是夜晚
        is_daytime = self.param_config['control_logic']['day_night']['day_start'] <= self.current_time.hour < \
                     self.param_config['control_logic']['day_night']['night_start']

        # 基础温度（考虑季节和昼夜）
        season_config = self.param_config['control_logic']['season']
        day_night_config = self.param_config['control_logic']['day_night']

        if self.current_season == "summer":
            base_temp = np.random.normal(28.0 + season_config['summer_temp_offset'], 4.0)
        elif self.current_season == "winter":
            base_temp = np.random.normal(18.0 + season_config['winter_temp_offset'], 3.5)  # 提高冬季基础温度
        else:  # 春秋季
            base_temp = np.random.normal(20.0, 3.0)

        if not is_daytime:
            base_temp += day_night_config['night_temp_offset'] * 0.7

        # 基础湿度（与温度、季节相关）
        if self.current_season == "summer":
            base_humidity = np.random.normal(60.0 + season_config['summer_humidity_offset'], 5.0)
        elif self.current_season == "winter":
            base_humidity = np.random.normal(55.0 + season_config['winter_humidity_offset'], 4.0)
        else:  # 春秋季
            base_humidity = np.random.normal(60.0, 5.0)

        if not is_daytime:
            base_humidity += day_night_config['night_humidity_offset']

        # 考虑天气影响
        if self.weather_state != "normal":
            weather_impact = self.param_config['weather_impact'][self.weather_state]
            if 'temp_increase' in weather_impact:
                base_temp += weather_impact['temp_increase']
            if 'temp_decrease' in weather_impact:
                base_temp -= weather_impact['temp_decrease']
            if 'humidity_increase' in weather_impact:
                base_humidity += weather_impact['humidity_increase']
            if 'humidity_decrease' in weather_impact:
                base_humidity -= weather_impact['humidity_decrease']
            if 'temp_decrease' in weather_impact and self.weather_state == "windy":
                base_temp -= weather_impact['temp_decrease']

        # 有害气体浓度（与通风情况相关，增加波动）
        base_ammonia = np.random.normal(12.0, 6.0)
        base_co2 = np.random.normal(1800.0, 500.0)

        # 气压（内外相关）
        base_pressure_out = np.random.uniform(
            self.param_config['environment']['pressure_out']['min'],
            self.param_config['environment']['pressure_out']['max']
        )

        # 考虑天气对气压的影响
        if self.weather_state == "windy":
            base_pressure_out += self.param_config['weather_impact']['windy']['pressure_diff_increase']

        base_pressure_in = base_pressure_out - np.random.uniform(0.05, 0.2)

        # 添加随机噪声
        temp = round(base_temp + np.random.uniform(-self.param_config['environment']['temperature']['noise'],
                                                   self.param_config['environment']['temperature']['noise']), 1)
        humidity = round(base_humidity + np.random.uniform(-self.param_config['environment']['humidity']['noise'],
                                                           self.param_config['environment']['humidity']['noise']), 1)
        ammonia = round(base_ammonia + np.random.uniform(-self.param_config['environment']['ammonia']['noise'],
                                                         self.param_config['environment']['ammonia']['noise']), 1)
        co2 = round(base_co2 + np.random.uniform(-self.param_config['environment']['co2']['noise'],
                                                 self.param_config['environment']['co2']['noise']))
        pressure_in = round(
            base_pressure_in + np.random.uniform(-self.param_config['environment']['pressure_in']['noise'],
                                                 self.param_config['environment']['pressure_in']['noise']), 2)
        pressure_out = round(
            base_pressure_out + np.random.uniform(-self.param_config['environment']['pressure_out']['noise'],
                                                  self.param_config['environment']['pressure_out']['noise']), 2)

        # 确保参数在合理范围内
        env = self.param_config['environment']
        temp = max(env['temperature']['min'], min(temp, env['temperature']['max']))
        humidity = max(env['humidity']['min'], min(humidity, env['humidity']['max']))
        ammonia = max(env['ammonia']['min'], min(ammonia, env['ammonia']['max']))
        co2 = max(env['co2']['min'], min(co2, env['co2']['max']))
        pressure_in = max(env['pressure_in']['min'], min(pressure_in, env['pressure_in']['max']))
        pressure_out = max(env['pressure_out']['min'], min(pressure_out, env['pressure_out']['max']))

        # 更新时间（每次生成数据后时间前进5-20分钟，增加时间波动）
        time_step = np.random.randint(5, 20)
        self.current_time += timedelta(minutes=time_step)

        return temp, humidity, ammonia, co2, pressure_in, pressure_out

    def _check_min_time(self, device: str, new_state) -> bool:
        """检查设备是否满足最小开启/关闭时间要求"""
        memory = self.device_memory[device]
        if memory['last_change'] is None:
            return True

        time_diff = (self.current_time - memory['last_change']).total_seconds() / 60  # 分钟数

        if isinstance(new_state, list):
            # 对于多设备（风机、窗户），只要有一个状态变化就需要检查
            if new_state != memory['last_state']:
                min_time = (self.param_config['control_logic'][device]['min_on_time']
                            if any(s > 0 for s in new_state) else self.param_config['control_logic'][device][
                    'min_off_time'])
                return time_diff >= min_time
            return True
        else:
            # 对于单设备（水帘、加热片）
            if memory['last_state'] != new_state:
                min_time = (self.param_config['control_logic'][device]['min_on_time']
                            if new_state != 0 else self.param_config['control_logic'][device]['min_off_time'])
                return time_diff >= min_time
            return True

    def _determine_device_status(self,
                                temperature: float,
                                humidity: float,
                                ammonia: float,
                                co2: float,
                                pressure_in: float,
                                pressure_out: float) -> Tuple[List[int], List[int], int, int, List[int]]:
        """根据环境参数确定设备状态（专业优化版控制逻辑）"""
        # 计算气体综合指数（归一化）
        gas_index = (ammonia / self.param_config['environment']['ammonia']['warning'] +
                     co2 / self.param_config['environment']['co2']['warning']) / 2

        # 计算气压差
        pressure_diff = pressure_out - pressure_in

        # 1. 确定大风机状态（考虑温度、有害气体和气压差）
        big_fan_count = self.param_config['equipment']['big_fan_count']
        big_fan_status = [0] * big_fan_count
        temp_thresholds = self.param_config['control_logic']['big_fan']['temp_thresholds']
        gas_weight = self.param_config['control_logic']['big_fan']['gas_weight']
        pressure_threshold = self.param_config['control_logic']['big_fan']['pressure_diff_threshold']

        # 计算需要的大风机档位（0-3）
        required_big_fan_level = 0
        if temperature > temp_thresholds[2] or gas_index > 1.2:
            required_big_fan_level = 3
        elif temperature > temp_thresholds[1] or gas_index > 0.8:
            required_big_fan_level = 2
        elif temperature > temp_thresholds[0] or gas_index > 0.6:
            required_big_fan_level = 1

        # 考虑气压差影响（气压差大时减少风机使用）
        if pressure_diff > pressure_threshold:
            required_big_fan_level = max(0, required_big_fan_level - 1)

        # 分配大风机状态（优先使用部分风机）
        if required_big_fan_level > 0:
            active_fans = min(big_fan_count, max(1, required_big_fan_level))  # 至少开启1个风机
            for i in range(active_fans):
                big_fan_status[i] = required_big_fan_level

        # 检查最小开启/关闭时间
        if not self._check_min_time('big_fan', big_fan_status):
            big_fan_status = self.device_memory['big_fan']['last_state']

        # 2. 确定小风扇状态（比大风机更敏感，但功率小）
        small_fan_count = self.param_config['equipment']['small_fan_count']
        small_fan_status = [0] * small_fan_count
        small_temp_thresholds = self.param_config['control_logic']['small_fan']['temp_thresholds']
        small_gas_weight = self.param_config['control_logic']['small_fan']['gas_weight']

        # 计算需要的小风扇档位（0-3）
        required_small_fan_level = 0
        if temperature > small_temp_thresholds[2] or gas_index > 1.0:
            required_small_fan_level = 3
        elif temperature > small_temp_thresholds[1] or gas_index > 0.7:
            required_small_fan_level = 2
        elif temperature > small_temp_thresholds[0] or gas_index > 0.5:
            required_small_fan_level = 1

        # 分配小风扇状态
        if required_small_fan_level > 0:
            active_small_fans = min(small_fan_count, max(1, required_small_fan_level))
            for i in range(active_small_fans):
                small_fan_status[i] = required_small_fan_level

        # 检查最小开启/关闭时间
        if not self._check_min_time('small_fan', small_fan_status):
            small_fan_status = self.device_memory['small_fan']['last_state']

        # 3. 确定水帘状态（考虑温度、湿度和风机状态）
        curtain_status = 0
        if (temperature > self.param_config['control_logic']['curtain']['temp_threshold'] and
                humidity < self.param_config['control_logic']['curtain']['humidity_threshold'] and
                (any(f > 0 for f in big_fan_status) or any(f > 0 for f in small_fan_status))):
            curtain_status = 1

        if not self._check_min_time('curtain', curtain_status):
            curtain_status = self.device_memory['curtain']['last_state']

        # 4. 确定加热片状态（考虑温度迟滞）
        heater_status = 0
        heater_config = self.param_config['control_logic']['heater']

        # 使用迟滞控制防止频繁开关
        if temperature < 16:
            heater_status = 1
        elif temperature < heater_config['temp_threshold'] - heater_config['temp_hysteresis']:
            heater_status = 1
        elif temperature > heater_config['temp_threshold'] + heater_config['temp_hysteresis']:
            heater_status = 0
        else:
            # 在迟滞区间内保持原状态
            heater_status = self.device_memory['heater']['last_state']

        # 冬季增加加热片开启概率
        if self.current_season == "winter" and temperature < heater_config['temp_threshold'] + 2:
            if random.random() < 0.8:  # 40%概率开启
                heater_status = 1

        if not self._check_min_time('heater', heater_status):
            heater_status = self.device_memory['heater']['last_state']

        # 5. 确定窗户状态（考虑温度、有害气体和气压差）
        window_count = self.param_config['equipment']['window_count']
        window_status = [0] * window_count
        window_config = self.param_config['control_logic']['window']

        if (temperature > window_config['temp_threshold'] or
                gas_index > window_config['gas_threshold'] or
                pressure_diff > window_config['pressure_diff_threshold']):

            # 根据需求决定开启多少窗户
            open_windows = 1
            if temperature > window_config['temp_threshold'] + 3 or gas_index > 1.0:
                open_windows = min(window_count, 1)

            for i in range(open_windows):
                window_status[i] = 1

        if not self._check_min_time('window', window_status):
            window_status = self.device_memory['window']['last_state']

        # 更新设备状态记忆
        if big_fan_status != self.device_memory['big_fan']['last_state']:
            self.device_memory['big_fan'] = {'last_change': self.current_time, 'last_state': big_fan_status.copy()}
        if small_fan_status != self.device_memory['small_fan']['last_state']:
            self.device_memory['small_fan'] = {'last_change': self.current_time, 'last_state': small_fan_status.copy()}
        if curtain_status != self.device_memory['curtain']['last_state']:
            self.device_memory['curtain'] = {'last_change': self.current_time, 'last_state': curtain_status}
        if heater_status != self.device_memory['heater']['last_state']:
            self.device_memory['heater'] = {'last_change': self.current_time, 'last_state': heater_status}
        if window_status != self.device_memory['window']['last_state']:
            self.device_memory['window'] = {'last_change': self.current_time, 'last_state': window_status.copy()}

        # 随机因素影响（设备故障、人工干预等）
        if np.random.random() < self.param_config['random_factors']['malfunction']:
            # 随机一个设备出现故障（强制关闭或异常开启）
            device_idx = np.random.randint(0, 5)
            if device_idx == 0:  # 大风机
                fan_idx = np.random.randint(0, big_fan_count)  # 大风机索引改为0-1
                big_fan_status[fan_idx] = 0 if np.random.random() < 0.7 else np.random.randint(1, 4)
            elif device_idx == 1:  # 小风扇
                fan_idx = np.random.randint(0, small_fan_count)
                small_fan_status[fan_idx] = 0 if np.random.random() < 0.7 else np.random.randint(1, 4)
            elif device_idx == 2:  # 水帘
                curtain_status = 0 if np.random.random() < 0.7 else 1
            elif device_idx == 3:  # 加热片
                heater_status = 0 if np.random.random() < 0.7 else 1
            else:  # 窗户
                window_idx = np.random.randint(0, window_count)
                window_status[window_idx] = 0 if np.random.random() < 0.7 else 1

        if np.random.random() < self.param_config['random_factors']['manual_override']:
            # 随机人工干预（更倾向于关闭设备）
            device_idx = np.random.randint(0, 5)
            if device_idx == 0:  # 大风机
                fan_idx = np.random.randint(0, big_fan_count)  # 大风机索引改为0-1
                big_fan_status[fan_idx] = 0 if np.random.random() < 0.8 else np.random.randint(1, 4)
            elif device_idx == 1:  # 小风扇
                fan_idx = np.random.randint(0, small_fan_count)
                small_fan_status[fan_idx] = 0 if np.random.random() < 0.8 else np.random.randint(1, 4)
            elif device_idx == 2:  # 水帘
                curtain_status = 0 if np.random.random() < 0.8 else 1
            elif device_idx == 3:  # 加热片
                heater_status = 0 if np.random.random() < 0.8 else 1
            else:  # 窗户
                window_idx = np.random.randint(0, window_count)
                window_status[window_idx] = 0 if np.random.random() < 0.8 else 1

        return big_fan_status, small_fan_status, curtain_status, heater_status, window_status

    def generate_samples(self, num_samples: int) -> pd.DataFrame:
        """生成指定数量的样本数据"""
        data = []

        # 重置设备状态记忆
        big_fan_count = self.param_config['equipment']['big_fan_count']
        small_fan_count = self.param_config['equipment']['small_fan_count']
        window_count = self.param_config['equipment']['window_count']
        self.device_memory = {
            'big_fan': {'last_change': None, 'last_state': [0] * big_fan_count},
            'small_fan': {'last_change': None, 'last_state': [0] * small_fan_count},
            'curtain': {'last_change': None, 'last_state': 0},
            'heater': {'last_change': None, 'last_state': 0},
            'window': {'last_change': None, 'last_state': [0] * window_count}
        }
        self.current_time = datetime(2025, 4, 1, 6, 0)  # 重置时间到春季
        self.current_season = "spring"  # 重置季节
        self.weather_state = "normal"  # 重置天气
        self.weather_duration = 0

        for _ in range(num_samples):
            # 生成环境参数
            temperature, humidity, ammonia, co2, pressure_in, pressure_out = self._generate_environment_params()

            # 确定设备状态
            big_fan_status, small_fan_status, curtain_status, heater_status, window_status = self._determine_device_status(
                temperature, humidity, ammonia, co2, pressure_in, pressure_out
            )

            # 组合数据行（修改大风机状态列数量为2）
            row = [
                temperature, humidity, ammonia, co2, pressure_in, pressure_out,
                *big_fan_status,
                curtain_status,
                heater_status,
                *window_status,
                *small_fan_status
            ]

            # 添加到数据列表
            data.append(row)

        # 创建DataFrame（修改大风机列名为2个）
        columns = [
            '温度', '湿度', '氨气浓度', '二氧化碳浓度', '内气压', '外气压',
            '大风机1状态', '大风机2状态',
            '水帘状态', '加热片状态',
            '窗户状态',
            '小风扇1状态', '小风扇2状态'
        ]
        df = pd.DataFrame(data, columns=columns)

        return df

    def save_to_csv(self, df: pd.DataFrame, file_path: str):
        """将数据保存到CSV文件"""
        df.to_csv(file_path, index=False)
        print(f"数据已保存到 {file_path}")


def main():
    """主函数，用于命令行调用"""
    parser = argparse.ArgumentParser(description='专业级养鸡场环境监控系统模拟数据生成器')
    parser.add_argument('--num_samples', type=int, default=100000, help='生成的样本数量')
    parser.add_argument('--output_file', type=str, default='ai_data\\poultry_farm_environment_data.csv',
                        help='输出文件路径')
    parser.add_argument('--config_file', type=str, default=None, help='配置文件路径')

    args = parser.parse_args()

    # 创建模拟器
    simulator = AdvancedPoultryFarmSimulator(args.config_file)

    # 生成数据
    data = simulator.generate_samples(args.num_samples)

    # 保存数据
    simulator.save_to_csv(data, args.output_file)

    # 打印一些样本数据
    print("\n生成的样本数据示例:")
    print(data.head(10).to_string(index=False))
    print("\n数据说明:")
    print("大风机状态: 0-关闭, 1-1档, 2-2档, 3-3档")
    print("小风扇状态: 0-关闭, 1-1档, 2-2档, 3-3档")
    print("水帘状态: 0-关闭, 1-开启")
    print("加热片状态: 0-关闭, 1-开启")
    print("窗户状态: 0-关闭, 1-开启")


if __name__ == "__main__":
    main()