<template>
  <div class="ai-chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-left">
        <el-icon class="header-icon"><ChatDotRound /></el-icon>
        <div class="header-title">
          <h3>AgriGuard AI 助手</h3>
          <span class="subtitle">智能农业环境监控专家</span>
        </div>
      </div>
      <div class="header-right">
        <el-tooltip content="健康检查" placement="bottom">
          <el-button
            :icon="StatusIcon"
            circle
            :type="healthStatus === 'healthy' ? 'success' : 'danger'"
            size="small"
            @click="checkHealth"
            :loading="healthChecking"
          />
        </el-tooltip>
        <el-tooltip content="新建会话" placement="bottom">
          <el-button
            :icon="Plus"
            circle
            type="primary"
            size="small"
            @click="createNewSession"
          />
        </el-tooltip>
        <el-tooltip content="设置" placement="bottom">
          <el-button
            :icon="Setting"
            circle
            size="small"
            @click="showSettings = true"
          />
        </el-tooltip>
      </div>
    </div>

    <div class="chat-body">
      <!-- 会话列表侧边栏 -->
      <div class="sessions-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <span v-show="!sidebarCollapsed">会话历史</span>
          <el-button
            :icon="sidebarCollapsed ? ArrowRight : ArrowLeft"
            text
            size="small"
            @click="sidebarCollapsed = !sidebarCollapsed"
          />
        </div>

        <div class="sessions-list" v-show="!sidebarCollapsed">
          <el-scrollbar height="100%">
            <div
              v-for="session in sessions"
              :key="session.id"
              class="session-item"
              :class="{ 'active': currentSessionId === session.id }"
              @click="switchSession(session.id)"
            >
              <div class="session-content">
                <div class="session-title">{{ session.title || '新对话' }}</div>
                <div class="session-time">{{ formatTime(session.updateTime) }}</div>
              </div>
              <el-button
                :icon="Delete"
                text
                size="small"
                type="danger"
                @click.stop="deleteSession(session.id)"
              />
            </div>
          </el-scrollbar>
        </div>
      </div>

      <!-- 聊天主区域 -->
      <div class="chat-main">
        <!-- 消息列表 -->
        <div class="messages-container">
          <el-scrollbar ref="messagesScrollbar" height="100%">
            <div class="messages-list" ref="messagesList">
              <!-- 欢迎消息 -->
              <div v-if="messages.length === 0" class="welcome-message">
                <div class="welcome-icon">
                  <el-icon><Avatar /></el-icon>
                </div>
                <h3>欢迎使用 AgriGuard AI 助手！</h3>
                <p>我是您的智能农业环境监控专家，可以帮助您：</p>
                <ul class="feature-list">
                  <li>🌡️ 分析温度、湿度等环境数据</li>
                  <li>🌱 提供作物种植建议</li>
                  <li>⚡ 设备故障诊断和维护建议</li>
                  <li>📊 环境数据趋势分析</li>
                </ul>
                <p>请开始您的提问吧！</p>
              </div>

              <!-- 消息列表 -->
              <div
                v-for="(message, index) in messages"
                :key="index"
                class="message-item"
                :class="message.role"
              >
                <div class="message-avatar">
                  <el-avatar :size="32">
                    <el-icon v-if="message.role === 'user'"><User /></el-icon>
                    <el-icon v-else><Avatar /></el-icon>
                  </el-avatar>
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="message-sender">
                      {{ message.role === 'user' ? '您' : 'AgriGuard AI' }}
                    </span>
                    <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                  </div>
                  <div class="message-text" v-html="formatMessage(message.content)"></div>
                  <div class="message-meta">
                    <el-tag v-if="message.tokenUsed" size="small" type="info">Token: {{ message.tokenUsed }}</el-tag>
                    <el-tag v-if="message.isStreaming" size="small" type="warning">正在生成...</el-tag>
                  </div>
                </div>
              </div>

              <!-- 正在输入指示器 -->
              <div v-if="isTyping" class="message-item assistant typing">
                <div class="message-avatar">
                  <el-avatar :size="32">
                    <el-icon><Avatar /></el-icon>
                  </el-avatar>
                </div>
                <div class="message-content">
                  <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>

        <!-- 输入区域 -->
        <div class="input-container">
          <div class="input-tools">
            <el-tooltip content="清空当前会话" placement="top">
              <el-button
                :icon="Delete"
                text
                size="small"
                @click="clearCurrentSession"
                :disabled="messages.length === 0"
              />
            </el-tooltip>
            <el-tooltip content="导出会话" placement="top">
              <el-button
                :icon="Download"
                text
                size="small"
                @click="exportSession"
                :disabled="messages.length === 0"
              />
            </el-tooltip>
          </div>

          <div class="message-input-area">
            <el-input
              v-model="messageInput"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 4 }"
              placeholder="请输入您想了解的农业环境问题..."
              @keydown.enter.exact.prevent="sendMessage"
              @keydown.enter.shift.exact="messageInput += '\n'"
              :disabled="isLoading"
              class="message-input"
            />
            <div class="input-actions">
              <div class="input-tips">
                <span>Enter 发送 • Shift+Enter 换行</span>
              </div>
              <el-button
                type="primary"
                :icon="isLoading ? Loading : Promotion"
                @click="sendMessage"
                :loading="isLoading"
                :disabled="!messageInput.trim()"
                class="send-button"
              >
                {{ isLoading ? '发送中' : '发送' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="AI 助手设置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="settings" label-width="100px">
        <el-form-item label="温度参数">
          <el-slider
            v-model="settings.temperature"
            :min="0"
            :max="1"
            :step="0.1"
            show-input
            :input-size="'small'"
          />
          <el-text type="info" size="small">
            较低值(0.1-0.3)适合数据分析，较高值(0.7-0.9)适合创意讨论
          </el-text>
        </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound, Plus, Setting, Avatar, User, Delete, Download,
  ArrowLeft, ArrowRight, Promotion, Loading,
  CircleCheckFilled, CircleCloseFilled
} from '@element-plus/icons-vue'
import aiChatApi from '@/api/ai_chat.js'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (__) {}
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

// 响应式数据
const messages = ref([])
const messageInput = ref('')
const isLoading = ref(false)
const isTyping = ref(false)
const currentSessionId = ref(null)
const sessions = ref([])
const sidebarCollapsed = ref(false)
const showSettings = ref(false)
const healthStatus = ref('unknown')
const healthChecking = ref(false)

// 设置
const settings = reactive({
  temperature: 0.7
})

// 计算属性
const StatusIcon = computed(() => {
  return healthStatus.value === 'healthy' ? CircleCheckFilled : CircleCloseFilled
})

// DOM 引用
const messagesScrollbar = ref(null)
const messagesList = ref(null)

// 生命周期
onMounted(() => {
  loadSettings()
  loadSessionsList() // 加载会话列表
  createNewSession()
})

// 方法
// 加载会话列表
const loadSessionsList = async () => {
  try {
    const response = await aiChatApi.getSessionsList()

    if (response.code === 0 || response.code === 1) {
      // 处理会话列表数据
      if (response.data && Array.isArray(response.data)) {
        sessions.value = response.data.map(session => ({
          id: session.id || session.sessionId,
          title: session.title || session.name || '新对话',
          updateTime: session.updateTime || session.lastUpdated || Date.now()
        }))
      } else if (response.data && response.data.sessions && Array.isArray(response.data.sessions)) {
        sessions.value = response.data.sessions.map(session => ({
          id: session.id || session.sessionId,
          title: session.title || session.name || '新对话',
          updateTime: session.updateTime || session.lastUpdated || Date.now()
        }))
      }


    } else {
      console.warn('获取会话列表失败:', response.message)
    }
  } catch (error) {
    console.error('加载会话列表失败:', error)
    // 不显示错误提示，静默处理
  }
}

// 解析对话历史字符串为消息数组
const parseDialogueHistory = (dialogueString) => {
  const messages = []

  try {
    // 将字符串按照 \n\n 分割成段落，然后处理每个段落
    const lines = dialogueString.split('\n')
    let currentMessage = null
    let currentContent = []

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      if (line.startsWith('用户: ')) {
        // 保存之前的消息
        if (currentMessage && currentContent.length > 0) {
          currentMessage.content = currentContent.join('\n').trim()
          if (currentMessage.content) {
            messages.push(currentMessage)
          }
        }

        // 开始新的用户消息
        currentMessage = {
          role: 'user',
          content: '',
          timestamp: Date.now() - (messages.length * 60000),
          tokenUsed: 0
        }
        currentContent = [line.substring(3).trim()]

      } else if (line.startsWith('AI助手: ')) {
        // 保存之前的消息
        if (currentMessage && currentContent.length > 0) {
          currentMessage.content = currentContent.join('\n').trim()
          if (currentMessage.content) {
            messages.push(currentMessage)
          }
        }

        // 开始新的助手消息
        currentMessage = {
          role: 'assistant',
          content: '',
          timestamp: Date.now() - (messages.length * 60000),
          tokenUsed: 0
        }
        currentContent = [line.substring(5).trim()]

      } else if (line === '') {
        // 空行，如果有当前消息，添加到内容中（保持段落结构）
        if (currentMessage && currentContent.length > 0) {
          currentContent.push('')
        }
      } else if (currentMessage) {
        // 继续当前消息的内容
        currentContent.push(line)
      }
    }

    // 保存最后一个消息
    if (currentMessage && currentContent.length > 0) {
      currentMessage.content = currentContent.join('\n').trim()
      if (currentMessage.content) {
        messages.push(currentMessage)
      }
    }


    return messages
  } catch (error) {
    console.error('解析对话历史失败:', error)
    // 如果解析失败，返回一个包含原始文本的消息
    return [{
      role: 'assistant',
      content: dialogueString,
      timestamp: Date.now(),
      tokenUsed: 0
    }]
  }
}



const sendMessage = async () => {
  if (!messageInput.value.trim() || isLoading.value) return

  const userMessage = {
    role: 'user',
    content: messageInput.value.trim(),
    timestamp: Date.now()
  }

  messages.value.push(userMessage)
  messageInput.value = ''
  isLoading.value = true
  isTyping.value = true

  try {
    await nextTick()
    scrollToBottom()

    // 发送消息到AI
    const response = await aiChatApi.sendMessage({
      message: userMessage.content,
      sessionId: currentSessionId.value,
      temperature: settings.temperature,
      stream: false
    })

    // 处理多种可能的响应格式
    let assistantMessage = null

    // 检查响应是否成功 (code: 0 表示成功)
    if (response && response.code === 0 && response.data) {
      // 标准响应格式
      if (response.data.message) {
        assistantMessage = {
          role: 'assistant',
          content: response.data.message,
          timestamp: response.data.timestamp || Date.now(),
          tokenUsed: response.data.tokenUsed || 0
        }

        // 更新会话ID
        if (response.data.sessionId && !currentSessionId.value) {
          currentSessionId.value = response.data.sessionId
          updateSessionsList()
        }
      }
    }
    // 兼容其他可能的成功代码
    else if (response && response.code === 1 && response.data) {
      if (response.data.message) {
        assistantMessage = {
          role: 'assistant',
          content: response.data.message,
          timestamp: response.data.timestamp || Date.now(),
          tokenUsed: response.data.tokenUsed || 0
        }

        if (response.data.sessionId && !currentSessionId.value) {
          currentSessionId.value = response.data.sessionId
          updateSessionsList()
        }
      }
    }

    if (assistantMessage) {
      messages.value.push(assistantMessage)
      await nextTick()
      scrollToBottom()
    } else {
      console.error('无法解析AI响应:', response)
      throw new Error(`AI服务返回了意外的响应格式`)
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    console.error('错误详情:', error.response || error)

    // 移除发送失败的用户消息
    if (messages.value.length > 0 && messages.value[messages.value.length - 1].role === 'user') {
      messages.value.pop()
    }

    ElMessage.error(`发送失败: ${error.message || error.response?.data?.message || '网络错误'}`)
  } finally {
    isLoading.value = false
    isTyping.value = false
  }
}

const createNewSession = () => {
  messages.value = []
  currentSessionId.value = null
  updateSessionsList()
}

const switchSession = async (sessionId) => {
  if (sessionId === currentSessionId.value) return

  try {
    ElMessage.info('正在加载会话历史...')
    const response = await aiChatApi.getSessionHistory(sessionId)


    // 兼容 code: 0 和 code: 1 两种成功代码
    if (response.code === 0 || response.code === 1) {
      // 处理多种可能的数据格式
      let historyMessages = []

      if (response.data) {
        // 如果data.messages存在（结构化数据）
        if (response.data.messages && Array.isArray(response.data.messages)) {
          historyMessages = response.data.messages
        }
        // 如果data本身是消息数组
        else if (Array.isArray(response.data)) {
          historyMessages = response.data
        }
        // 如果data是字符串格式的对话历史（需要解析）
        else if (typeof response.data === 'string' && response.data.trim()) {
          historyMessages = parseDialogueHistory(response.data)
        }
        // 如果需要转换格式
        else if (response.data.history) {
          historyMessages = response.data.history
        }
      }

      // 确保消息格式正确
      messages.value = historyMessages.map(msg => ({
        role: msg.role || (msg.type === 'user' ? 'user' : 'assistant'),
        content: msg.content || msg.message || msg.text || '',
        timestamp: msg.timestamp || Date.now(),
        tokenUsed: msg.tokenUsed || 0
      }))

      currentSessionId.value = sessionId
      await nextTick()
      scrollToBottom()

      ElMessage.success(`已加载 ${messages.value.length} 条历史消息`)
    } else {
      ElMessage.error(`加载会话失败: ${response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('切换会话失败:', error)
    ElMessage.error(`切换会话失败: ${error.message || '网络错误'}`)
  }
}

const clearCurrentSession = async () => {
  try {
    await ElMessageBox.confirm('确定要清空当前会话吗？', '确认操作', {
      type: 'warning'
    })

    if (currentSessionId.value) {
      await aiChatApi.deleteSession(currentSessionId.value)
    }

    createNewSession()
    ElMessage.success('会话已清空')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空会话失败:', error)
      ElMessage.error('清空会话失败')
    }
  }
}

const exportSession = () => {
  if (messages.value.length === 0) return

  const content = messages.value.map(msg => {
    const role = msg.role === 'user' ? '用户' : 'AI助手'
    const time = formatTime(msg.timestamp)
    return `[${time}] ${role}: ${msg.content}`
  }).join('\n\n')

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `AgriGuard_AI_对话_${new Date().toLocaleDateString()}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const checkHealth = async () => {
  healthChecking.value = true
  try {
    const response = await aiChatApi.healthCheck()
    // 兼容 code: 0 和 code: 1 两种成功代码
    if (response.code === 0 || response.code === 1) {
      healthStatus.value = 'healthy'
      ElMessage.success('AI服务状态正常')
    } else {
      healthStatus.value = 'error'
      ElMessage.error(`AI服务异常: ${response.message || '未知错误'}`)
    }
  } catch (error) {
    healthStatus.value = 'error'
    console.error('健康检查失败:', error)
    ElMessage.error(`健康检查失败: ${error.message || '网络连接错误'}`)
  } finally {
    healthChecking.value = false
  }
}

const updateSessionsList = async () => {
  // 重新加载会话列表以获取最新的数据
  await loadSessionsList()

  // 如果当前会话ID存在但不在列表中，添加到列表（本地临时会话）
  if (currentSessionId.value && !sessions.value.find(s => s.id === currentSessionId.value)) {
    // 查找第一条用户消息作为标题
    const firstUserMessage = messages.value.find(msg => msg.role === 'user')
    const title = firstUserMessage ?
      (firstUserMessage.content.substring(0, 20) + (firstUserMessage.content.length > 20 ? '...' : '')) :
      '新对话'

    sessions.value.unshift({
      id: currentSessionId.value,
      title: title,
      updateTime: Date.now()
    })
  }
}

const deleteSession = async (sessionId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个会话吗？', '确认操作', {
      type: 'warning'
    })

    await aiChatApi.deleteSession(sessionId)
    sessions.value = sessions.value.filter(s => s.id !== sessionId)

    if (sessionId === currentSessionId.value) {
      createNewSession()
    }

    ElMessage.success('会话已删除')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除会话失败:', error)
      ElMessage.error('删除会话失败')
    }
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesScrollbar.value) {
      messagesScrollbar.value.setScrollTop(messagesList.value?.scrollHeight || 0)
    }
  })
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const isToday = date.toDateString() === now.toDateString()

  if (isToday) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }
}

const formatMessage = (content) => {
  try {
    // 使用marked库将markdown转换为HTML
    const html = marked(content)
    return html
  } catch (error) {
    console.error('Markdown解析失败:', error)
    // 如果markdown解析失败，降级为简单的换行处理
    return content.replace(/\n/g, '<br>')
  }
}

const loadSettings = () => {
  const saved = localStorage.getItem('aiChatSettings')
  if (saved) {
    Object.assign(settings, JSON.parse(saved))
  }
}

const saveSettings = () => {
  localStorage.setItem('aiChatSettings', JSON.stringify(settings))
  showSettings.value = false
  ElMessage.success('设置已保存')
}
</script>

<style scoped>
.ai-chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 28px;
  color: #409eff;
}

.header-title h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.subtitle {
  color: #909399;
  font-size: 12px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.chat-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sessions-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.sessions-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.sessions-list {
  flex: 1;
  overflow: hidden;
}

.session-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f2f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.session-item:hover {
  background: #f0f2f5;
}

.session-item.active {
  background: #ecf5ff;
  border-right: 3px solid #409eff;
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-time {
  font-size: 12px;
  color: #909399;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.messages-container {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

.messages-list {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message {
  text-align: center;
  padding: 40px 20px;
  color: #606266;
}

.welcome-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.feature-list {
  text-align: left;
  max-width: 300px;
  margin: 16px auto;
}

.feature-list li {
  margin: 8px 0;
  list-style: none;
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 85%;
}

.message-item.user {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-item.assistant {
  margin-right: auto;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-item.user .message-content {
  background: #409eff;
  color: white;
  border-radius: 16px 16px 4px 16px;
  padding: 12px 16px;
}

.message-item.assistant .message-content {
  background: #f0f2f5;
  color: #303133;
  border-radius: 16px 16px 16px 4px;
  padding: 12px 16px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

/* Markdown 样式 */
.message-text :deep(h1),
.message-text :deep(h2),
.message-text :deep(h3),
.message-text :deep(h4),
.message-text :deep(h5),
.message-text :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.message-text :deep(h1) { font-size: 1.5em; }
.message-text :deep(h2) { font-size: 1.3em; }
.message-text :deep(h3) { font-size: 1.1em; }

.message-text :deep(p) {
  margin: 8px 0;
}

.message-text :deep(ul),
.message-text :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.message-text :deep(li) {
  margin: 4px 0;
}

.message-text :deep(blockquote) {
  border-left: 4px solid #ddd;
  margin: 8px 0;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.message-text :deep(code) {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.message-text :deep(pre) {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 8px 0;
}

.message-text :deep(pre code) {
  background: none;
  padding: 0;
}

.message-text :deep(table) {
  border-collapse: collapse;
  margin: 8px 0;
  width: 100%;
}

.message-text :deep(th),
.message-text :deep(td) {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.message-text :deep(th) {
  background: #f5f5f5;
  font-weight: 600;
}

.message-text :deep(a) {
  color: #409eff;
  text-decoration: none;
}

.message-text :deep(a:hover) {
  text-decoration: underline;
}

.message-text :deep(strong) {
  font-weight: 600;
}

.message-text :deep(em) {
  font-style: italic;
}

.message-meta {
  margin-top: 8px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background: #409eff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-container {
  border-top: 1px solid #e4e7ed;
  background: white;
}

.input-tools {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f2f5;
  display: flex;
  gap: 8px;
}

.message-input-area {
  padding: 16px;
}

.message-input {
  margin-bottom: 12px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-tips {
  font-size: 12px;
  color: #909399;
}

.send-button {
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sessions-sidebar {
    width: 60px;
  }

  .sessions-sidebar .sidebar-header span {
    display: none;
  }

  .sessions-list {
    display: none;
  }

  .message-item {
    max-width: 95%;
  }
}
</style>
