package com.agriguard.service;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.ElectricityRealTime;
import com.agriguard.pojo.PageResult;

import java.time.LocalDate;
import java.util.List;

public interface ElectricityService {
    //定时对电流数据进行分析，存储历史数据，并进行数据删除
    void ElectricityDateSys();

    // 检查设备是否已存在电流传感器
    boolean existsByDeviceId(Integer deviceId);

    // 添加电流传感器
    String addElectricityRealTime(Integer deviceId);

    // 查询电流传感器详情
    Device findElectricityDetailById(Integer deviceId);

    // 分页查询历史数据
    PageResult<ElectricityRealTime> findHistoryData(Integer pageNum, Integer pageSize,
                                                    Integer deviceId, LocalDate beginData, LocalDate endData);

    // 多条件分页查询实时状态
    PageResult<ElectricityRealTime> findLatestStatus(Integer pageNum, Integer pageSize,
                                                     Integer deviceId, String deviceName, String place);

    // 获取指定位置的分钟级平均电流
    List<ElectricityRealTimeByMinute> getAvgElectricity(String place);

    //插入电流数据
    void insertTemperature();

    //获取过去24小时的历史电流数据，用于折线图展示
    List<ElectricityHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史电流数据，用于饼图展示
    List<ElectricityPie> getHistory24HoursForPie(String place);
}
