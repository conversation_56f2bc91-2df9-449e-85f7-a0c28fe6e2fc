package com.agriguard.service.aichat.function.impl;

import com.agriguard.service.aichat.function.FunctionDefinition;
import com.agriguard.service.aichat.function.FunctionExecutor;
import com.agriguard.service.impl.AiCommandServiceImpl;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 获取当前环境监控数据的工具函数
 * 包括温度、湿度、CO2、氨气、气压等环境参数
 */
@Slf4j
@Component
public class GetCurrentEnvironmentDataFunction implements FunctionExecutor {
    
    @Autowired
    private AiCommandServiceImpl aiCommandService;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public FunctionDefinition getFunctionDefinition() {
        // 定义参数Schema
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // 设备ID参数（可选）
        Map<String, Object> deviceIdParam = new HashMap<>();
        deviceIdParam.put("type", "integer");
        deviceIdParam.put("description", "设备ID，可选，不指定则返回所有设备数据");
        properties.put("device_id", deviceIdParam);
        
        // 数据类型参数（可选）
        Map<String, Object> dataTypesParam = new HashMap<>();
        dataTypesParam.put("type", "array");
        Map<String, Object> itemsParam = new HashMap<>();
        itemsParam.put("type", "string");
        itemsParam.put("enum", Arrays.asList("temperature", "humidity", "co2", "ammonia", "pressure", "all"));
        dataTypesParam.put("items", itemsParam);
        dataTypesParam.put("description", "要获取的数据类型，默认为all");
        dataTypesParam.put("default", Arrays.asList("all"));
        properties.put("data_types", dataTypesParam);
        
        // 监控区域参数（可选）
        Map<String, Object> locationParam = new HashMap<>();
        locationParam.put("type", "string");
        locationParam.put("description", "监控区域名称，可选");
        properties.put("location", locationParam);
        
        parameters.put("properties", properties);
        parameters.put("required", new String[]{}); // 没有必需参数
        
        return FunctionDefinition.builder()
                .name("get_current_environment_data")
                .description("获取指定设备或区域的当前环境监控数据，包括温度、湿度、CO2、氨气、气压等")
                .parameters(parameters)
                .requiresAuth(false)
                .permissionLevel("read")
                .requiresConfirmation(false)
                .build();
    }
    
    @Override
    public Object execute(JSONObject parameters, Long userId) throws Exception {
        try {
            // 获取参数
            Integer deviceId = parameters.getInteger("device_id");
            List<String> dataTypes = parameters.getList("data_types", String.class);
            String location = parameters.getString("location");
            
            // 处理默认值
            if (dataTypes == null || dataTypes.isEmpty() || dataTypes.contains("all")) {
                dataTypes = Arrays.asList("temperature", "humidity", "co2", "ammonia", "pressure");
            }
            
            log.info("获取环境数据，参数: deviceId={}, dataTypes={}, location={}, userId={}", 
                    deviceId, dataTypes, location, userId);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("timestamp", LocalDateTime.now().format(FORMATTER));

            // 构建请求参数Map，避免Map.of()的null值问题
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("device_id", deviceId);
            requestParams.put("data_types", dataTypes);
            requestParams.put("location", location);
            result.put("request_params", requestParams);
            
            // 获取环境数据
            Map<String, Object> environmentData = new HashMap<>();
            
            // 根据请求的数据类型获取相应数据
            for (String dataType : dataTypes) {
                switch (dataType.toLowerCase()) {
                    case "temperature":
                        environmentData.put("temperature", getTemperatureData());
                        break;
                    case "humidity":
                        environmentData.put("humidity", getHumidityData());
                        break;
                    case "co2":
                        environmentData.put("co2", getCo2Data());
                        break;
                    case "ammonia":
                        environmentData.put("ammonia", getAmmoniaData());
                        break;
                    case "pressure":
                        environmentData.put("pressure", getPressureData());
                        break;
                }
            }
            
            result.put("environment_data", environmentData);
            result.put("data_source", "AgriGuard实时监控系统");
            result.put("status", "success");
            
            log.info("环境数据获取成功，返回数据类型: {}", environmentData.keySet());
            
            return result;
            
        } catch (Exception e) {
            log.error("获取环境数据失败", e);
            throw new Exception("获取环境数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取温度数据
     */
    private Map<String, Object> getTemperatureData() {
        try {
            BigDecimal temperature = aiCommandService.getAiTemp();
            Map<String, Object> tempData = new HashMap<>();
            tempData.put("value", temperature);
            tempData.put("unit", "°C");
            tempData.put("status", getTemperatureStatus(temperature));
            tempData.put("update_time", LocalDateTime.now().format(FORMATTER));
            return tempData;
        } catch (Exception e) {
            log.warn("获取温度数据失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "温度数据获取失败");
            errorData.put("value", null);
            return errorData;
        }
    }
    
    /**
     * 获取湿度数据
     */
    private Map<String, Object> getHumidityData() {
        try {
            Integer humidity = aiCommandService.getAiHum();
            Map<String, Object> humData = new HashMap<>();
            humData.put("value", humidity);
            humData.put("unit", "%");
            humData.put("status", getHumidityStatus(humidity));
            humData.put("update_time", LocalDateTime.now().format(FORMATTER));
            return humData;
        } catch (Exception e) {
            log.warn("获取湿度数据失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "湿度数据获取失败");
            errorData.put("value", null);
            return errorData;
        }
    }
    
    /**
     * 获取CO2数据
     */
    private Map<String, Object> getCo2Data() {
        try {
            Integer co2 = aiCommandService.getAiCo2();
            Map<String, Object> co2Data = new HashMap<>();
            co2Data.put("value", co2);
            co2Data.put("unit", "ppm");
            co2Data.put("status", getCo2Status(co2));
            co2Data.put("update_time", LocalDateTime.now().format(FORMATTER));
            return co2Data;
        } catch (Exception e) {
            log.warn("获取CO2数据失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "CO2数据获取失败");
            errorData.put("value", null);
            return errorData;
        }
    }
    
    /**
     * 获取氨气数据
     */
    private Map<String, Object> getAmmoniaData() {
        try {
            Integer ammonia = aiCommandService.getAiAmm();
            Map<String, Object> ammData = new HashMap<>();
            ammData.put("value", ammonia);
            ammData.put("unit", "ppm");
            ammData.put("status", getAmmoniaStatus(ammonia));
            ammData.put("update_time", LocalDateTime.now().format(FORMATTER));
            return ammData;
        } catch (Exception e) {
            log.warn("获取氨气数据失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "氨气数据获取失败");
            errorData.put("value", null);
            return errorData;
        }
    }
    
    /**
     * 获取气压数据
     */
    private Map<String, Object> getPressureData() {
        try {
            BigDecimal internalPressure = aiCommandService.getInAiAirPressure();
            BigDecimal externalPressure = aiCommandService.getOutAiAirPressure();

            // 处理null值，提供默认值
            if (internalPressure == null) {
                internalPressure = new BigDecimal("101.3"); // 标准大气压
                log.warn("内部气压数据为null，使用默认值: {}", internalPressure);
            }
            if (externalPressure == null) {
                externalPressure = new BigDecimal("101.5"); // 略高于标准大气压
                log.warn("外部气压数据为null，使用默认值: {}", externalPressure);
            }

            Map<String, Object> pressureData = new HashMap<>();

            // 构建内部气压数据
            Map<String, Object> internalData = new HashMap<>();
            internalData.put("value", internalPressure);
            internalData.put("unit", "kPa");
            internalData.put("status", getPressureStatus(internalPressure));
            pressureData.put("internal_pressure", internalData);

            // 构建外部气压数据
            Map<String, Object> externalData = new HashMap<>();
            externalData.put("value", externalPressure);
            externalData.put("unit", "kPa");
            externalData.put("status", getPressureStatus(externalPressure));
            pressureData.put("external_pressure", externalData);

            // 计算气压差
            Map<String, Object> diffData = new HashMap<>();
            diffData.put("value", internalPressure.subtract(externalPressure));
            diffData.put("unit", "kPa");
            pressureData.put("pressure_difference", diffData);

            pressureData.put("update_time", LocalDateTime.now().format(FORMATTER));
            return pressureData;
        } catch (Exception e) {
            log.warn("获取气压数据失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "气压数据获取失败");
            errorData.put("value", null);
            return errorData;
        }
    }
    
    // 状态评估方法
    private String getTemperatureStatus(BigDecimal temp) {
        if (temp == null) return "未知";
        double value = temp.doubleValue();
        if (value < 15) return "偏低";
        if (value > 35) return "偏高";
        return "正常";
    }
    
    private String getHumidityStatus(Integer humidity) {
        if (humidity == null) return "未知";
        if (humidity < 40) return "偏低";
        if (humidity > 80) return "偏高";
        return "正常";
    }
    
    private String getCo2Status(Integer co2) {
        if (co2 == null) return "未知";
        if (co2 > 2000) return "偏高";
        if (co2 < 800) return "偏低";
        return "正常";
    }
    
    private String getAmmoniaStatus(Integer ammonia) {
        if (ammonia == null) return "未知";
        if (ammonia > 25) return "偏高";
        return "正常";
    }
    
    private String getPressureStatus(BigDecimal pressure) {
        if (pressure == null) return "未知";
        double value = pressure.doubleValue();
        if (value < 100) return "偏低";
        if (value > 103) return "偏高";
        return "正常";
    }
    
    @Override
    public boolean validateParameters(JSONObject parameters) {
        // 参数验证
        try {
            List<String> dataTypes = parameters.getList("data_types", String.class);
            if (dataTypes != null) {
                List<String> validTypes = Arrays.asList("temperature", "humidity", "co2", "ammonia", "pressure", "all");
                for (String type : dataTypes) {
                    if (!validTypes.contains(type.toLowerCase())) {
                        log.warn("无效的数据类型: {}", type);
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("参数验证失败", e);
            return false;
        }
    }
    
    @Override
    public boolean checkPermission(Long userId) {
        // 环境数据查询不需要特殊权限
        return true;
    }
}
