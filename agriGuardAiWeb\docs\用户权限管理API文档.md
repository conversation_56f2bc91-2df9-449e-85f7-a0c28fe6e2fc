# 用户权限管理API文档

## 概述
本文档描述了用户权限管理相关的API接口，包括用户管理、角色管理、搜索功能等。

## 角色说明
- **管理员 (ADMIN)**: `role_id = 1`，拥有所有权限
- **普通用户 (USER)**: `role_id = 2`，基础用户权限

## API接口

### 1. 获取用户列表（支持搜索和筛选）
**接口**: `GET /user/admin/list`  
**权限**: 仅管理员  
**描述**: 获取用户列表，支持分页、角色筛选和关键词搜索

**查询参数**:
- `page`: 页码（从0开始，默认0）
- `size`: 每页大小（默认10）
- `keyword`: 搜索关键词（可选，支持用户名和邮箱模糊匹配）
- `roleId`: 角色筛选（可选，1-管理员，2-普通用户）

**请求示例**:
```
GET /user/admin/list?page=0&size=10&keyword=admin&roleId=1
```

**响应**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "content": [
            {
                "userId": 1,
                "userName": "admin",
                "realName": "管理员",
                "email": "<EMAIL>",
                "phone": "18879328792",
                "roles": ["ADMIN"]
            },
            {
                "userId": 2,
                "userName": "user001",
                "realName": "普通用户",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "roles": ["USER"]
            }
        ],
        "totalElements": 25,
        "totalPages": 3,
        "currentPage": 0,
        "pageSize": 10
    }
}
```

### 2. 添加用户
**接口**: `POST /user/admin/add`  
**权限**: 仅管理员  
**描述**: 管理员创建新用户

**请求体**:
```json
{
    "userName": "newuser",
    "password": "password123",
    "realName": "新用户",
    "email": "<EMAIL>",
    "phone": "13800138000"
}
```

**响应**:
```json
{
    "code": 0,
    "message": "用户创建成功",
    "data": {
        "userId": 123,
        "userName": "newuser",
        "realName": "新用户",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "roles": ["USER"]
    }
}
```

### 3. 修改用户信息
**接口**: `PUT /user/admin/update`  
**权限**: 仅管理员  
**描述**: 管理员修改用户信息

**请求体**:
```json
{
    "userId": 123,
    "userName": "updateduser",
    "realName": "更新用户",
    "email": "<EMAIL>",
    "phone": "13900139000",
    "newPassword": "newpassword123"
}
```

**响应**:
```json
{
    "code": 0,
    "message": "用户信息更新成功",
    "data": null
}
```

### 4. 删除用户
**接口**: `DELETE /user/admin/delete`  
**权限**: 仅管理员  
**描述**: 管理员删除用户

**查询参数**:
- `userId`: 用户ID（必需）

**请求示例**:
```
DELETE /user/admin/delete?userId=123
```

**响应**:
```json
{
    "code": 0,
    "message": "用户删除成功",
    "data": null
}
```

### 5. 修改用户角色
**接口**: `PUT /user/admin/role/update`  
**权限**: 仅管理员  
**描述**: 修改指定用户的角色（提升/降级）

**请求体**:
```json
{
    "userId": 123,
    "roleId": 1,
    "roleName": "管理员"
}
```

**说明**:
- `roleId = 1`: 提升为管理员
- `roleId = 2`: 降级为普通用户

**响应**:
```json
{
    "code": 0,
    "message": "用户角色已更新为: 管理员",
    "data": null
}
```

**错误响应**:
```json
{
    "code": 400,
    "message": "该用户已经是管理员",
    "data": null
}
```

### 6. 检查用户是否为管理员
**接口**: `GET /user/admin/role/check/{userId}`  
**权限**: 仅管理员  
**描述**: 检查指定用户是否具有管理员权限

**路径参数**:
- `userId`: 用户ID

**响应**:
```json
{
    "code": 0,
    "message": "success",
    "data": true
}
```



## 搜索功能说明

### 关键词搜索
- **支持字段**: 用户名（userName）、邮箱（email）
- **匹配方式**: 模糊匹配（LIKE查询）
- **大小写**: 不区分大小写
- **示例**: `keyword=admin` 会匹配用户名或邮箱中包含"admin"的用户

### 角色筛选
- **参数**: `roleId`
- **可选值**: 
  - `1`: 仅显示管理员
  - `2`: 仅显示普通用户
  - 不传递此参数: 显示所有角色用户

### 组合搜索
可以同时使用关键词搜索和角色筛选：
```
GET /user/admin/list?page=0&size=10&keyword=test&roleId=2
```
此请求会返回角色为普通用户且用户名或邮箱包含"test"的用户。

## 错误码说明
- `0`: 操作成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 注意事项
1. 所有管理员接口都需要`ADMIN`权限
2. 角色ID：1-管理员，2-普通用户
3. 新注册用户默认为普通用户角色
4. 管理员可以创建、修改、删除用户，以及变更用户角色
5. 普通用户只能访问基础功能，无法进行用户管理操作
6. 搜索功能支持空值，传递空字符串等同于不传递参数
7. 分页从0开始计数，与前端组件保持一致
8. 响应格式统一使用`code: 0`表示成功（不是200）

## 安全特性
- 统一的错误处理和用户提示
- API请求超时设置（10秒）
- 详细的错误日志记录
- 权限验证失败的友好提示
- 所有敏感操作都需要管理员权限验证 