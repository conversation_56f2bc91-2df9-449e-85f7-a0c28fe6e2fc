package com.agriguard.service.iotservice;

import com.agriguard.dto.SelectDevice;
import com.agriguard.mapper.ThresholdMapper;
import com.agriguard.pojo.Result;
import com.agriguard.service.IotDeviceServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;


@Slf4j
@RequiredArgsConstructor
@Service
public class Threshold implements IotDeviceServer {



    @Autowired
    private final ThresholdMapper thresholdMapper;

    @Override
    public boolean findDeviceType(String deviceType) {
        String type = "threshold";
        return type.equals(deviceType);
    }

    /**
     * 先查找当天该设备有没有记录数据，有的话修改，否则添加
     * @param
     */
    @Override
    public void addData(String value) {

        System.out.println("addDatad的命令是："+value);

        //取出标识符
        String tpye = splitString(value)[0];
        //取出数据
        String data = splitString(value)[1];
        System.out.println(splitString(value)[0]);
        System.out.println(splitString(value)[1]);

        //生成一个统一的时间便于实时状态表和历史数据表统一时间
        LocalDateTime updateTime = LocalDateTime.now();
        switch (tpye) {
            case "TU":
                thresholdMapper.updateThresholdUpValue(1,data);
                break;
            case "TD":
                thresholdMapper.updateThresholdDownValue(1,data);
                break;
            case "HU":
                thresholdMapper.updateThresholdUpValue(2,data);
                break;
            case "HD":
                thresholdMapper.updateThresholdDownValue(2,data);
                break;
            case "AU":
                thresholdMapper.updateThresholdUpValue(3,data);
                break;
            case "AD":
                thresholdMapper.updateThresholdDownValue(3,data);
                break;
            case "CU":
                thresholdMapper.updateThresholdUpValue(4,data);
                break;
            case "CD":
                thresholdMapper.updateThresholdDownValue(4,data);
                break;
            case "PU":
                thresholdMapper.updateThresholdUpValue(5,data);
                break;
            case "PD":
                thresholdMapper.updateThresholdDownValue(5,data);
                break;
            default:
                log.info("硬件组修改阈值错误");
        }
        log.info("硬件组修改了阈值，其命令为："+value+"，时间为："+updateTime);
    }

    //取出前两个，后面全部的方法
    public static String[] splitString(String input) {
        if (input == null) {

        }
        int length = input.length();
        if (length <= 2) {
            return new String[]{null, null};
        } else {
            return new String[]{input.substring(0, 2), input.substring(2)};
        }
    }

    //前端修改阈值，后端实现数据库修改阈值
    public Result<?> updateThreshold(String thresholdType, String place, String upValue, String downValue) {
        try {
            /// 检查至少有一个参数
            if (upValue == null && downValue == null) {
                throw new IllegalArgumentException("至少需要提供一个阈值参数");
            }

            // 分别校验非空参数
            if (upValue != null) validateValueFormat(thresholdType, upValue);
            if (downValue != null) validateValueFormat(thresholdType, downValue);

            // 保留原有更新逻辑
            int affectedRows = thresholdMapper.updateThresholdValue(thresholdType, place, upValue, downValue);
            return affectedRows > 0 ? Result.success("阈值更新成功") : Result.error("未找到对应阈值配置");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("系统错误: " + e.getMessage());
        }
    }

    private void validateValueFormat(String type, String value) {
        // 根据类型验证数值格式（温度/气压/电流保留1位小数）
        String pattern = switch (type) {
            case "温度", "气压", "电流" -> "^\\d+(\\.\\d)?$";
            default -> "^\\d+$";
        };
        if (!value.matches(pattern)) {
            throw new IllegalArgumentException("数值格式错误");
        }
    }


    public List<SelectDevice> selectThresholdTypeThreshold(String place) {
        try {
            // 保留原有更新逻辑
            return thresholdMapper.selectThresholdTypeThreshold(place);
        } catch (Exception e) {
            return List.of();
        }
    }
}
