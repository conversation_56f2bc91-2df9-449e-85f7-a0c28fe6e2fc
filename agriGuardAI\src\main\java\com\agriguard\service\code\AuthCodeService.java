package com.agriguard.service.code;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Service
public class AuthCodeService {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;


    // 生成6位随机验证码
    public String generateCode() {
        return String.valueOf(new Random().nextInt(899999) + 100000);
    }

    // 存储验证码（5分钟有效期）
    public void saveCode(String email, String code) {
        redisTemplate.opsForValue().set("auth_code:" + email, code, 5, TimeUnit.MINUTES);
    }

    // 验证码校验
    public boolean validateCode(String account, String code) {
        String storedCode = redisTemplate.opsForValue().get("auth_code:" + account);
        return code != null && code.equals(storedCode);
    }


}
