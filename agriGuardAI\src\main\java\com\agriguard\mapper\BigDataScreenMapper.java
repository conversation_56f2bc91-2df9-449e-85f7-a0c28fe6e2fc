package com.agriguard.mapper;

import com.agriguard.dto.BigDataScreen.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BigDataScreenMapper {
    List<DeviceTypeDistribution> countDevicesByType();

    List<TempHumidityByPeriod> selectHourlyAvgTemp();

    List<TempHumidityByPeriod> selectHourlyAvgHumidity();

    List<SensorCountDTO> selectSensorCounts();

    AvgAirPressureDTO selectAvgAirPressure();

    List<GasConcentrationByPeriod> selectHourlyAvgCo2();

    List<GasConcentrationByPeriod> selectHourlyAvgAmmonia();

    List<WarehouseEquipmentCount> countWarehouseEquipment();
}
