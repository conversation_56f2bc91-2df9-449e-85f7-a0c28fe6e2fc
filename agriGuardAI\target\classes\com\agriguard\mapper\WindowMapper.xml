<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.WindowMapper">


    <!-- 查询窗户状态的结果映射 -->
    <resultMap id="WindowStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="window_status" property="windowStatus" jdbcType="TINYINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询窗户状态 -->
    <select id="findLatestStatus" resultMap="WindowStatusResultMap">
        SELECT
        wrt.device_id,
        d.device_name,
        d.place,
        wrt.window_status,
      --  wrt.update_time
        DATE_FORMAT(wrt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        window_real_time wrt
        JOIN
        device d ON wrt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null">
            AND wrt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name = #{deviceName}
        </if>
        <if test="windowStatus != null">
            AND wrt.window_status = #{windowStatus}
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        wrt.update_time DESC
    </select>

<!--    查看窗户设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM window_real_time WHERE device_id = #{deviceId}
    </select>

<!--    增加窗户实时状态-->
    <insert id="addWindowRealTime" parameterType="com.agriguard.pojo.WindowRealTime">
        INSERT INTO window_real_time
            (device_id, window_status, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>

<!--    查询窗户详情-->
    <select id="findwindowDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>


    <!-- 界面更新窗户状态，后续可删除，用华为云的 -->
    <update id="updateStatus">
        UPDATE window_real_time
        SET
            window_status = #{windowStatus}
        WHERE device_id = #{deviceId}
    </update>
    <!-- 更新窗户状态 -->
    <update id="ordercommand">
        UPDATE window_real_time
        SET
            window_status = #{windowStatus}
        WHERE device_id = #{deviceId}
    </update>

<!--    判断Ai命令是否已存在-->
    <select id="windowAiCommand" resultType="int">
            select window_status from window_real_time where device_id = #{deviceId}
    </select>
</mapper>