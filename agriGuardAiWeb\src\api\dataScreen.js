import axios from 'axios';
import {useAuthStore} from '@/stores/auth'

const API_BASE_URL = '/api/bigdatascreen';

const auth = axios.create({
    baseURL: API_BASE_URL,
});

auth.interceptors.request.use(config => {
    const authStore = useAuthStore();
    const Authorization = authStore.user?.token;
    if (Authorization) {
        config.headers.Authorization = Authorization;
    }
    return config;
}, error => {
    return Promise.reject(error);
});

export default {
    // 设备类型分布
    getDeviceTypeDistribution() {
        return auth.get('/type-distribution');
    },
    // 温湿度数据
    getTempHumidity() {
        return auth.get('/hourly-avg-temp-humidity');
    },
    // 传感器数量
    getSensorCounts() {
        return auth.get('/sensor-counts');
    },
    // 气压数据
    getAirPressure() {
        return auth.get('/avg-air-pressure');
    },
    // 气体浓度
    getGasConcentration() {
        return auth.get('/hourly-avg-gas');
    },
    // 仓库设备数量
    getWarehouseEquipment() {
        return auth.get('/warehouse-equipment');
    },
    getAiControl() {
        return auth.get('/Get_ai-control');
    },
    setAiControl(boo){
        return auth.post(`/Set_ai-control?controlType=${boo}`, null)
    }
}
