package com.agriguard.service.iotservice;

import com.agriguard.mapper.HumidityMapper;
import com.agriguard.mapper.TemperatureMapper;
import com.agriguard.service.IotDeviceServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Slf4j
@RequiredArgsConstructor
@Service
public class TempAndHumi implements IotDeviceServer {
    private final TemperatureMapper temperatureMapper;
    private final HumidityMapper humidityMapper;

    @Override
    public boolean findDeviceType(String deviceType) {
        String type = "TempAndHumi";
        return type.equals(deviceType);
    }

    /**
     * 先查找当天该设备有没有记录数据，有的话修改，否则添加
     * @param
     */
    @Override
    public void addData(String value) {

//        Object TempAndHumi = map.get("TempAndHumi");
      System.out.println("addDatad的温度是："+value);
      //  String testvalue = "0A097153268277282050048051054053";


        String data = value.replace("0A", ""); // 去除标识符
        int dataLength = data.length();

        // 解析温度值（前5组）和湿度值（后5组）
        for (int i = 0; i < 10; i++) {
            int start = i * 3;
            if (start + 3 > dataLength) break;

            String segment = data.substring(start, start + 3);
            LocalDateTime updateTime = LocalDateTime.now();

            if (i < 5) {// 温度处理（传感器ID 50-54）
                BigDecimal temp = parseTemperature(segment);
                int deviceId = 50 + i;
                temperatureMapper.updateTempToRealTime(deviceId, temp, updateTime);
                temperatureMapper.insertTempToDataCache(deviceId, temp, updateTime);
            } else {// 湿度处理（传感器ID 55-59）
                int hum = parseHumidity(segment);
                int deviceId = 55 + (i - 5);
                humidityMapper.updateHumToRealTime(deviceId, hum, updateTime);
                humidityMapper.insertHumToDataCache(deviceId, hum, updateTime);
            }
        }
    }

    

    // 温度解析（097 -> 9.7）
    private BigDecimal parseTemperature(String segment) {
        return new BigDecimal(segment.substring(0, 2) + "." + segment.charAt(2));
    }

    // 湿度解析（050 -> 50）
    private int parseHumidity(String segment) {
        return Integer.parseInt(segment);
    }

}
