import axios from 'axios';
import {useAuthStore} from '@/stores/auth'

const API_BASE_URL = '/api';

const auth = axios.create({
    baseURL: API_BASE_URL,
});

auth.interceptors.request.use(config => {
    const authStore = useAuthStore();
    const Authorization = authStore.user?.token;
    if (Authorization) {
        config.headers.Authorization = Authorization;
    }
    return config;
}, error => {
    return Promise.reject(error);
});

export default {
    updateThresholdTypeThreshold(thresholdType,place,upValue,downValue) {
        return auth.post('/threshold/update-device', null,{
            params: {
                thresholdType: thresholdType,
                place:place,
                upValue:upValue,
                downValue:downValue
            }
        })
    },


    selectThresholdTypeThreshold(place) {
        return auth.get('/threshold/select-device', {
            params: {
                place: place
            }
        })
    },

    getAll_admin_name() {
        return auth.get('/regulationandalarm/all_admin_name')
    },

    regulationandalarmCreate(place,alarmTypes,personName,noticeWay) {
        return auth.post('/regulationandalarm', {
            place: place,
            alarmTypes:alarmTypes,
            personName:personName,
            noticeWay:noticeWay
        })
    },

    regulationandalarmSearch() {
        return auth.get('/regulationandalarm')
    },

    regulationandalarmUpdate(id, place, alarmTypes, personName, noticeWay) {
        return auth.put(`/regulationandalarm/${id}`, {
            place: place,
            alarmTypes: alarmTypes,
            personName: personName,
            noticeWay: noticeWay
        })
    },

    regulationandalarmDelete(id) {
        return auth.delete(`/regulationandalarm/${id}`)
    },

    // 修改后的报警记录API，支持分页和过滤参数
    getAlarms(params) {
        return auth.get('/alarms', {
            params: {
                alarmTypes: params.alarmTypes,
                place: params.place,
                startTime: params.startTime,
                endTime: params.endTime,
                page: params.page,
                size: params.size
            }
        })
    },

    // 添加处理报警的API
    handleAlarm(alertId) {
        return auth.post('/alarms', null, {
            params: {
                alertId: alertId
            }
        })
    },
}
