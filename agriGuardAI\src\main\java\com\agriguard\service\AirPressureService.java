package com.agriguard.service;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.AirPressureRealTime;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;

import java.time.LocalDate;
import java.util.List;

public interface AirPressureService {
    //定时对气压数据进行分析，存储历史数据，并进行数据删除
    void AirPressureDateSys();

    // 检查设备是否已存在气压传感器
    boolean existsByDeviceId(Integer deviceId);

    // 添加气压传感器
    String addAirPressureRealTime(Integer deviceId);

    // 查询气压传感器详情
    Device findAirPressureDetailById(Integer deviceId);

    // 分页查询历史数据
    PageResult<AirPressureRealTime> findHistoryData(Integer pageNum, Integer pageSize,
                                                    Integer deviceId, LocalDate beginData, LocalDate endData);

    // 多条件分页查询实时状态
    PageResult<AirPressureRealTime> findLatestStatus(Integer pageNum, Integer pageSize,
                                                 Integer deviceId, String deviceName, String place);

    // 获取指定位置的分钟级平均气压
    List<AirPressureRealTimeByMinute> getAvgAirPressure(String place);

    //插入温度数据
    void insertTemperature();
    //获取过去24小时的历史气压数据，用于折线图展示
    List<AirPressureHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史气压数据，用于饼图展示
    List<AirPressurePie> getHistory24HoursForPie(String place);
}
