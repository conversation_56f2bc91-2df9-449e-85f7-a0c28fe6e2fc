package com.agriguard.controller;

import com.agriguard.service.MoniService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/monidata")
public class MoniData {

    @Autowired
    private MoniService moniService;

    //使用每20秒钟执行一次温度数据
//    @Scheduled(cron = "0/20 * * * * ?  ")
    @Async("asyncExecutor")
    public void scheduledTaskTemp() {
        try {
            // 调用moni数据中的方法
            moniService.TempMoniDate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //使用每20秒钟执行一次湿度数据
//    @Scheduled(cron = "0/20 * * * * ?  ")
    @Async("asyncExecutor")
    public void scheduledTaskHum() {
        try {
            // 调用moni数据中的方法
            moniService.HumMoniDate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //使用每30秒执行一次CO2数据
//    @Scheduled(cron = "0/30 * * * * ?  ")
    @Async("asyncExecutor")
    public void scheduledTaskCo2() {
        try {
            // 调用moni数据中的方法
            moniService.Co2MoniDate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //使用每30秒执行一次氨气模拟数据
//    @Scheduled(cron = "0/30 * * * * ? ")
    @Async("asyncExecutor")
    public void scheduledTaskAmm() {
        try {
            // 调用moni数据中的方法
            moniService.AmmMoniDate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //使用每两30秒执行一次气压模拟数据
//    @Scheduled(cron = "0/30 * * * * ?  ")
    @Async("asyncExecutor")
    public void scheduledTaskApre() {
        try {
            // 调用moni数据中的方法
            moniService.ApreMoniDate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //使用每10秒钟执行一次电流模拟数据
//    @Scheduled(cron = "0/10 * * * * ? ")
    @Async("asyncExecutor")
    public void scheduledTaskEle() {
        try {
            // 调用moni数据中的方法
            moniService.EleMoniDate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
