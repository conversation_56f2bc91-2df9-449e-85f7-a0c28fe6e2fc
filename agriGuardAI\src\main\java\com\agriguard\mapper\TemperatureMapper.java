package com.agriguard.mapper;

import com.agriguard.entity.device.TempHistory24;
import com.agriguard.entity.device.TempPie;
import com.agriguard.entity.device.TempRealTimeByMinute;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.TemperatureDataCache;
import com.agriguard.pojo.TemperatureDataHistory;
import com.agriguard.pojo.TemperatureRealTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface TemperatureMapper {

    //插入温度数据
    //更新温度实时状态表
    void updateTempToRealTime(Integer deviceId, BigDecimal temp, LocalDateTime updateTime);

    //插入温度值到温度缓存表
    void insertTempToDataCache(Integer deviceId, BigDecimal temp, LocalDateTime updateTime);

    void insertTempToDataCache(TemperatureDataCache cache);

    //分析温度缓存数据
    List<TemperatureDataHistory> getTemperatureStatsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    //添加温度历史数据
    void addTempDateHistory(TemperatureDataHistory item);

    //删除指定时间段内的温度缓存数据
    void deleteTempDateCacheByHour(LocalDateTime startTime, LocalDateTime endTime);

    //查询设备id温度传感器是否已存在
    int existsByDeviceId(Integer deviceId);

    //添加温度传感器
    void addTempRealTime(Integer deviceId);

    //查询温度传感器详情
    Device findTempDetailById(Integer deviceId);


    //分页查询历史数据
    List<TemperatureRealTime> findHistoryData(@Param("deviceId") Integer deviceId,
                                              @Param("start") LocalDateTime start,
                                              @Param("end") LocalDateTime end);

    //分页查询实时状态数据
    List<TemperatureRealTime> findLatestStatus(@Param("deviceId") Integer deviceId,
                                               @Param("deviceName") String deviceName,
                                               @Param("place") String place);

    //查询实时状态数据
    List<TempRealTimeByMinute> getAvgTemperature(String place);

    //获取过去24小时的历史温度数据，用于折线图展示
    List<TempHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史温度数据，用于饼图展示
    List<TempPie> getHistory24HoursForPie(String place);

    //获取温度给AI当参数
    BigDecimal getAiTemp();
}
