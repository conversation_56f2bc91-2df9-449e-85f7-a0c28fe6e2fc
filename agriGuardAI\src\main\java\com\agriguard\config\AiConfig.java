package com.agriguard.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置类
 * 支持OpenAI兼容格式的API调用
 */
@Configuration
@ConfigurationProperties(prefix = "ai")
@Data
public class AiConfig {
    
    /**
     * AI API密钥
     * 从对应平台获取（如DeepSeek、OpenAI等）
     */
    private String apiKey;
    
    /**
     * API基础URL
     * 默认: https://api.deepseek.com
     */
    private String baseUrl = "https://api.deepseek.com";
    
    /**
     * 使用的模型名称
     * 可选: deepseek-chat, deepseek-reasoner, gpt-3.5-turbo等
     */
    private String model = "deepseek-chat";
    
    /**
     * 最大输出token数
     */
    private Integer maxTokens = 4000;
    
    /**
     * 温度参数，控制输出的随机性
     * 范围: 0.0-2.0，越高越随机
     */
    private Double temperature = 0.7;
    
    /**
     * 请求超时时间(秒)
     */
    private Integer timeout = 60;
    
    /**
     * 系统提示词，定义AI的角色和行为
     * 在配置文件中定义，确保一致性和安全性
     */
    private String systemPrompt;
} 