<template>
  <div class="dashboard-container">
    <!-- 现代化侧边栏 -->
    <div class="sidebar-wrapper" :class="{ 'collapsed': isCollapsed }">
      <!-- 品牌区域 -->
      <div class="sidebar-brand">
        <div class="brand-logo">
          <el-icon class="logo-icon"><Monitor /></el-icon>
        </div>
        <div v-show="!isCollapsed" class="brand-text">
          <h3>AgriGuard AI</h3>
          <span>智能监控系统</span>
        </div>
        <el-button
          type="text"
          :icon="isCollapsed ? Expand : Fold"
          @click="toggleSidebar"
          class="collapse-toggle"
        />
      </div>

      <!-- 导航菜单 -->
      <div class="sidebar-nav">
        <el-scrollbar height="100%">
          <div class="nav-section">
            <!-- 主控台 -->
            <div class="nav-item"
                 :class="{ active: activeMenuIndex === 'welcome' }"
                 @click="goToDataScreen">
              <div class="nav-item-content">
                <el-icon class="nav-icon"><DataBoard /></el-icon>
                <span v-show="!isCollapsed" class="nav-text">数据大屏</span>
              </div>
              <div class="nav-indicator"></div>
            </div>

            <!-- 设备管理 -->
            <div class="nav-group">
              <div class="nav-group-title" v-show="!isCollapsed">
                <el-icon><Setting /></el-icon>
                <span>设备管理</span>
              </div>
              <div class="nav-group-divider" v-show="isCollapsed"></div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'equipment-detail' }"
                   @click="setActiveComponent('EquipmentDetail')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Document /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">设备详情</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'fan' }"
                   @click="setActiveComponent('Fan')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Refresh /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">风机</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'water-curtain' }"
                   @click="setActiveComponent('WaterCurtain')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Drizzling /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">水帘</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'window' }"
                   @click="setActiveComponent('Window')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Grid /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">窗户</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'heating' }"
                   @click="setActiveComponent('HeatingElement')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Sunny /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">加热片</span>
                </div>
                <div class="nav-indicator"></div>
              </div>
            </div>

            <!-- 传感器管理 -->
            <div class="nav-group">
              <div class="nav-group-title" v-show="!isCollapsed">
                <el-icon><Monitor /></el-icon>
                <span>传感器管理</span>
              </div>
              <div class="nav-group-divider" v-show="isCollapsed"></div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'temperature' }"
                   @click="setActiveComponent('TemperatureSensor')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Sunny /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">温度传感器</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'humidity' }"
                   @click="setActiveComponent('HumiditySensor')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Cloudy /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">湿度传感器</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'co2' }"
                   @click="setActiveComponent('CarbonDioxideSensor')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><WindPower /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">二氧化碳传感器</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'ammonia' }"
                   @click="setActiveComponent('AmmoniaSensor')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Operation /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">氨气传感器</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'pressure' }"
                   @click="setActiveComponent('PressureSensor')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Odometer /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">气压传感器</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'current' }"
                   @click="setActiveComponent('CurrentSensor')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><Lightning /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">电流传感器</span>
                </div>
                <div class="nav-indicator"></div>
              </div>
            </div>

            <!-- 人员管理 -->
            <div class="nav-group">
              <div class="nav-group-title" v-show="!isCollapsed">
                <el-icon><UserFilled /></el-icon>
                <span>人员管理</span>
              </div>
              <div class="nav-group-divider" v-show="isCollapsed"></div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'user-management' }"
                   @click="setActiveComponent('UserManagement')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><User /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">用户管理</span>
                </div>
                <div class="nav-indicator"></div>
              </div>
            </div>

            <!-- AI 助手与阈值、警告 -->
            <div class="nav-group">
              <div class="nav-group-title" v-show="!isCollapsed">
                <el-icon><MoreFilled /></el-icon>
                <span>其他操作</span>
              </div>
              <div class="nav-group-divider" v-show="isCollapsed"></div>

              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'ai-chat' }"
                   @click="setActiveComponent('AiChat')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><ChatDotRound /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">智能对话</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

              <!-- 新增的阈值与警报菜单项 -->
              <div class="nav-item"
                   :class="{ active: activeMenuIndex === 'alert-settings' }"
                   @click="setActiveComponent('AlertSettings')">
                <div class="nav-item-content">
                  <el-icon class="nav-icon"><DataLine /></el-icon>
                  <span v-show="!isCollapsed" class="nav-text">阈值与警报</span>
                </div>
                <div class="nav-indicator"></div>
              </div>

            </div>
          </div>
        </el-scrollbar>
      </div>

      <!-- 用户区域 -->
      <div class="sidebar-user">
        <div class="user-profile" @click="setActiveComponent('SetInformation')">
          <el-avatar
            :src="userAvatar"
            :size="isCollapsed ? 40 : 48"
            @error="handleAvatarError"
            class="user-avatar"
          >
            <el-icon><UserFilled /></el-icon>
          </el-avatar>
          <div v-show="!isCollapsed" class="user-info">
            <div class="user-name">{{ authStore.user?.username || '用户' }}</div>
            <div class="user-role">管理员</div>
          </div>
        </div>

        <el-button
          type="danger"
          :icon="SwitchButton"
          @click="logout"
          :size="isCollapsed ? 'small' : 'default'"
          class="logout-button"
        >
          <span v-show="!isCollapsed">退出登录</span>
        </el-button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content" :class="{ 'ai-chat-mode': activeMenuIndex === 'ai-chat' }">
      <component :is="activeComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, shallowRef, onMounted } from 'vue'
import {useRoute, useRouter} from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import myPower from '@/api/user.js'
import authAPI from '@/api/auth.js'



const route = useRoute()

// Element Plus 图标导入
import {
  Expand,
  Fold,
  DataBoard,
  Setting,
  Document,
  Refresh,
  Drizzling,
  Grid,
  Sunny,
  Monitor,
  // Thermometer,
  Cloudy,
  WindPower,
  Operation,
  Odometer,
  Lightning,
  UserFilled,
  User,
  MoreFilled,
  SwitchButton,
  ChatDotRound,
  DataLine
} from '@element-plus/icons-vue'

import Welcome from '@/views/Admin/Welcome.vue'
// 设备管理
import Fan from '@/views/Admin/equipment_management/fan.vue'
import WaterCurtain from '@/views/Admin/equipment_management/water_curtain.vue'
import Window from '@/views/Admin/equipment_management/window.vue'
import HeatingElement from '@/views/Admin/equipment_management/heating_element.vue'
import EquipmentDetail from '@/views/Admin/equipment_management/equipment_detail.vue'
// 传感器管理
import TemperatureSensor from '@/views/Admin/sensor_management/temperature_sensor.vue'
import HumiditySensor from '@/views/Admin/sensor_management/humidity_sensor.vue'
import CarbonDioxideSensor from '@/views/Admin/sensor_management/carbon_dioxide_sensor.vue'
import AmmoniaSensor from '@/views/Admin/sensor_management/ammonia_sensor.vue'
import PressureSensor from '@/views/Admin/sensor_management/pressure_sensor.vue'
import CurrentSensor from '@/views/Admin/sensor_management/current_sensor.vue'
// 人员管理
import UserManagement from '@/views/Admin/staff_management/user_management.vue'

// 其他操作
import AiChat from '@/views/Admin/other/AiChat.vue'
import AlertSettings from '@/views/Admin/other/AlertSettings.vue'

// 其他
import SetInformation from "@/views/User/SetInformation.vue";
import {ElMessage} from "element-plus";

const authStore = useAuthStore()
const token = authStore.user?.token
const router = useRouter()

const isCollapsed = ref(false)
const activeComponent = shallowRef(AiChat)
const userAvatar = ref('logo.svg')
const activeMenuIndex = ref('ai-chat')

// 菜单索引映射
const componentMenuMap = {
  'Welcome': 'welcome',
  'EquipmentDetail': 'equipment-detail',
  'Fan': 'fan',
  'WaterCurtain': 'water-curtain',
  'Window': 'window',
  'HeatingElement': 'heating',
  'TemperatureSensor': 'temperature',
  'HumiditySensor': 'humidity',
  'CarbonDioxideSensor': 'co2',
  'AmmoniaSensor': 'ammonia',
  'PressureSensor': 'pressure',
  'CurrentSensor': 'current',
  'UserManagement': 'user-management',
  'AiChat': 'ai-chat',
  'AlertSettings': 'alert-settings',
  'SetInformation': 'profile'
}

const fetchUserAvatar = async () => {
  try {

    if (!token) return

    const response = await myPower.getMyInformation(token)
    if (response.data?.user_profile_picture) {
      userAvatar.value = response.data.user_profile_picture
    }
  } catch (error) {
    console.error('获取用户头像失败:', error)
  }
}

const handleAvatarError = () => {
  userAvatar.value = 'logo.svg'
}

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const setActiveComponent = (component) => {
  // 更新活跃菜单索引
  activeMenuIndex.value = componentMenuMap[component] || 'welcome'

  switch (component) {
    case 'Welcome':
      activeComponent.value = Welcome
      break
    // 设备管理
    case 'Fan':
      activeComponent.value = Fan
      break
    case 'WaterCurtain':
      activeComponent.value = WaterCurtain
      break
    case 'Window':
      activeComponent.value = Window
      break
    case 'HeatingElement':
      activeComponent.value = HeatingElement
      break
    case 'EquipmentDetail':
      activeComponent.value = EquipmentDetail
      break
    // 传感器管理
    case 'TemperatureSensor':
      activeComponent.value = TemperatureSensor
      break
    case 'HumiditySensor':
      activeComponent.value = HumiditySensor
      break
    case 'CarbonDioxideSensor':
      activeComponent.value = CarbonDioxideSensor
      break
    case 'AmmoniaSensor':
      activeComponent.value = AmmoniaSensor
      break
    case 'PressureSensor':
      activeComponent.value = PressureSensor
      break
    case 'CurrentSensor':
      activeComponent.value = CurrentSensor
      break
    // 人员管理
    case 'UserManagement':
      activeComponent.value = UserManagement
      break
    // 其他操作
    case 'AiChat':
      activeComponent.value = AiChat
      break
    case 'AlertSettings':
      activeComponent.value = AlertSettings
      break
    // 其他
    case 'SetInformation':
      activeComponent.value = SetInformation
      break
    default:
      activeComponent.value = Welcome
  }
}

const logout = () => {
  authStore.clearUser()
  router.push('/login')
}

const goToDataScreen = () => {
  router.push('/dataScreen')
}

onMounted(async () => {
  const is_expire = (await authAPI.is_expire(token)).data
  if(is_expire){
    ElMessage.error('您的token已过期')
    logout()
  }
  // 添加路由参数检查
  if (route.query.menu === 'ai-chat') {
    setActiveComponent('AiChat')
  }
  fetchUserAvatar()
})
</script>

<style scoped>
/* 主容器 */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8fafc;
  position: relative;
}

/* 侧边栏容器 */
.sidebar-wrapper {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e2e8f0;
  transition: width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 100;
  overflow: hidden;
}

.sidebar-wrapper.collapsed {
  width: 90px;
}

/* 品牌区域 */
.sidebar-brand {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  min-height: 80px;
  flex-shrink: 0;
}

.sidebar-wrapper.collapsed .sidebar-brand {
  padding: 20px 15px;
}

.brand-logo {
  width: 40px;
  height: 40px;
  background-color: #3b82f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-icon {
  font-size: 20px;
  color: white;
}

.brand-text {
  margin-left: 12px;
  color: #1e293b;
  flex: 1;
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
  white-space: nowrap;
}

.sidebar-wrapper.collapsed .brand-text {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
  margin-left: 0;
}

.brand-text h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.brand-text span {
  font-size: 12px;
  color: #64748b;
  display: block;
  margin-top: 2px;
}

.collapse-toggle {
  color: #64748b !important;
  background: transparent !important;
  border: 1px solid #e2e8f0 !important;
  padding: 6px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.collapse-toggle:hover {
  background: #f1f5f9 !important;
  color: #3b82f6 !important;
}

/* 导航区域 */
.sidebar-nav {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-wrapper.collapsed .sidebar-nav {
  padding: 12px 8px;
}

.nav-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 导航组 */
.nav-group {
  margin-bottom: 20px;
}

.sidebar-wrapper.collapsed .nav-group {
  margin-bottom: 12px;
}

.nav-group-title {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #64748b;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
  white-space: nowrap;
}

.sidebar-wrapper.collapsed .nav-group-title {
  opacity: 0;
  height: 0;
  padding: 0 12px;
  margin-bottom: 0;
}

.nav-group-title .el-icon {
  margin-right: 6px;
  font-size: 14px;
  flex-shrink: 0;
}

.nav-group-divider {
  height: 1px;
  background-color: #e2e8f0;
  margin: 12px 0;
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.sidebar-wrapper.collapsed .nav-group-divider {
  opacity: 1;
  margin: 8px 16px;
}

/* 导航项 */
.nav-item {
  position: relative;
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-item-content {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  color: #475569;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.sidebar-wrapper.collapsed .nav-item-content {
  justify-content: center;
  padding: 12px 8px;
}

.nav-icon {
  font-size: 18px;
  margin-right: 10px;
  width: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.sidebar-wrapper.collapsed .nav-icon {
  margin-right: 0;
  font-size: 20px;
}

.nav-text {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  opacity: 1;
  transform: translateX(0);
  overflow: hidden;
  white-space: nowrap;
}

.sidebar-wrapper.collapsed .nav-text {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
}

.nav-indicator {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #3b82f6;
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* 悬浮效果 */
.nav-item:hover {
  background-color: #f1f5f9;
  color: #3b82f6;
}

.nav-item:hover .nav-icon {
  color: #3b82f6;
}

.nav-item:hover .nav-text {
  color: #3b82f6;
}

/* 激活状态 */
.nav-item.active {
  background-color: #eff6ff;
  color: #3b82f6;
}

.nav-item.active .nav-indicator {
  opacity: 1;
}

.nav-item.active .nav-icon {
  color: #3b82f6;
}

.nav-item.active .nav-text {
  color: #3b82f6;
  font-weight: 600;
}

/* 用户区域 */
.sidebar-user {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

/* 正常状态 */
.user-profile {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 12px;
  border: 1px solid #e2e8f0;
}

.user-profile:hover {
  background-color: #f8fafc;
}

.user-avatar {
  border: 2px solid #e2e8f0;
  flex-shrink: 0;
}

.user-info {
  margin-left: 10px;
  color: #1e293b;
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
}

.user-role {
  font-size: 12px;
  color: #64748b;
  white-space: nowrap;
}

.logout-button {
  width: 100%;
  background-color: #ef4444 !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 10px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: #dc2626 !important;
}

/* 收起状态 - 重新设计 */
.sidebar-wrapper.collapsed .sidebar-user {
  align-items: center;
  padding: 20px 0;
  gap: 16px;
}

.sidebar-wrapper.collapsed .user-profile {
  width: 50px;
  height: 50px;
  padding: 0;
  justify-content: center;
  margin-bottom: 0;
  border: none;
  background: transparent;
}

.sidebar-wrapper.collapsed .user-info {
  display: none;
}

.sidebar-wrapper.collapsed .logout-button {
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 50px !important;
}

/* 主内容区 */
.main-content {
  flex: 1;
  padding: 24px;
  background-color: transparent;
  overflow-y: auto;
  margin-left: 280px;
  transition: margin-left 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  min-height: 100vh;
}

/* AI聊天模式下的特殊样式 */
.main-content.ai-chat-mode {
  padding: 0;
  overflow: hidden;
  height: 100vh;
}

.sidebar-wrapper.collapsed + .main-content {
  margin-left: 90px;
}

/* 滚动条美化 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sidebar-wrapper {
    width: 260px;
  }

  .sidebar-wrapper.collapsed {
    width: 80px;
  }

  .main-content {
    margin-left: 260px;
  }

  .sidebar-wrapper.collapsed + .main-content {
    margin-left: 80px;
  }
}

@media (max-width: 768px) {
  .sidebar-wrapper {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    width: 280px !important;
  }

  .sidebar-wrapper.mobile-open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0 !important;
    width: 100%;
    padding: 16px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background-color: #0f172a;
  }

  .sidebar-wrapper {
    background-color: #1e293b;
    border-right-color: #334155;
  }

  .sidebar-brand {
    border-bottom-color: #334155;
  }

  .brand-text {
    color: #f8fafc;
  }

  .brand-text span {
    color: #94a3b8;
  }

  .nav-group-title {
    color: #94a3b8;
  }

  .nav-group-divider {
    background-color: #334155;
  }

  .nav-item-content {
    color: #cbd5e1;
  }

  .nav-item:hover {
    background-color: #334155;
  }

  .nav-item.active {
    background-color: #1e40af;
  }

  .user-profile {
    border-color: #334155;
  }

  .user-profile:hover {
    background-color: #334155;
  }

  .user-info {
    color: #f8fafc;
  }

  .user-role {
    color: #94a3b8;
  }

  .main-content {
    background-color: transparent;
  }
}
</style>
