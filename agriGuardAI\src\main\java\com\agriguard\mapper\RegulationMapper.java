package com.agriguard.mapper;

import com.agriguard.entity.Regulation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RegulationMapper {
    int insert(Regulation regulation);
    int update(Regulation regulation);
    int delete(Integer regulationId);
    Regulation selectById(Integer regulationId);
    List<Regulation> selectByCondition(Regulation condition);

    List<String> all_admin_name();

    Regulation getRegulationByParams(String place, String thresholdType);
}
