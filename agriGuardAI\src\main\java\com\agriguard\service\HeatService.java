package com.agriguard.service;

import com.agriguard.pojo.*;

import java.time.LocalDate;

public interface HeatService {

    // 检查设备ID是否存在
    boolean existsByDeviceId(Integer deviceId);

    // 添加加热片状态，添加加热片设备id就可以
    String addHeatRealTime(Integer deviceId);

    // 分页查询加热片状态（默认工作状态为1）
    PageResult<HeatRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, Integer heatStatus, String place);

    //查询加热片详情
    Device findHeatDetailById(Integer deviceId);

    //修改加热片状态
    Result<String> updateStatus(Integer deviceId, Integer heatStatus);

    PageResult<NappeRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData);
}
