<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.AirPressureMapper">

    <!--    模拟华为云气压数据-->
    <!--    更新气压实时状态表-->
    <update id="updateAirPressureToRealTime" parameterType="com.agriguard.pojo.AirPressureRealTime">
        UPDATE air_pressure_real_time
        SET
            apre = #{apre},
            update_time = #{updateTime}
            WHERE device_id = #{deviceId}
    </update>
    <!--    插入气压数据至温度缓存表-->
    <insert id="insertAirPressureToDataCache" parameterType="com.agriguard.pojo.AirPressureDataCache">
        INSERT INTO air_pressure_data_cache (device_id, apre, update_time)
        VALUES (#{deviceId}, #{apre}, #{updateTime})
    </insert>

    <!--    分析气压缓存数据-->
    <select id="getAirPressureStatsByTimeRange" resultType="com.agriguard.pojo.AirPressureDataHistory">
        SELECT
            device_id AS deviceId,
            MAX(apre) AS maxApre,
            MIN(apre) AS minApre,
            ROUND(AVG(apre), 1) AS avgApre,
            COUNT(*) AS dataCount
        FROM air_pressure_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY device_id
    </select>

    <!-- 插入气压历史数据 -->
    <insert id="addAirPressureDateHistory" parameterType="com.agriguard.pojo.AirPressureDataHistory">
        INSERT INTO air_pressure_data_history (
            device_id, collect_date, collect_hour,
            max_apre, min_apre, avg_apre, data_count, update_time
        ) VALUES (
                     #{deviceId}, #{collectDate}, #{collectHour},
                     #{maxApre}, #{minApre}, #{avgApre}, #{dataCount}, #{updateTime}
                 )
    </insert>

    <!-- 清空气压缓存表 -->
    <delete id="deleteAirPressureDateCacheByHour">
        DELETE FROM air_pressure_data_cache
        WHERE update_time BETWEEN #{startTime} AND #{endTime}
    </delete>


    <!--    查看气压设备id是否存在-->
    <select id="existsByDeviceId" resultType="int">
        SELECT COUNT(1) FROM air_pressure_real_time WHERE device_id = #{deviceId}
    </select>

    <!--    增加气压传感器实时状态-->
    <insert id="addAirPressureRealTime" parameterType="com.agriguard.pojo.AirPressureRealTime">
        INSERT INTO air_pressure_real_time
            (device_id, apre, update_time)
        VALUES
            (#{deviceId}, 0,now())
    </insert>

    <!--    查看气压传感器详情-->
    <select id="findAirPressureDetailById" resultType="com.agriguard.pojo.Device">
        SELECT
            device_id AS deviceId,
            device_name AS deviceName,
            type,
            place,
            model,
            factory,
            install_time AS installTime,
            update_time AS updateTime,
            description
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!--    查询历史数据-->
    <select id="findHistoryData" resultType="com.agriguard.pojo.AirPressureDataHistory">
        SELECT
        device_id AS deviceId,
        collect_date AS collectDate,
        collect_hour AS collectHour,
        max_apre AS maxApre,
        min_apre AS minApre,
        avg_apre AS avgApre,
        data_count AS dataCount,
        update_time AS updateTime
        FROM air_pressure_data_history
        WHERE device_id = #{deviceId}
        <if test="start != null">
            AND update_time >= #{start}
        </if>
        <if test="end != null">
            AND update_time &lt; #{end}
        </if>
        ORDER BY update_time DESC
    </select>


    <!-- 查询气压传感器状态的结果映射 -->
    <resultMap id="AirPressureStatusResultMap" type="map">
        <id column="device_id" property="deviceId" jdbcType="INTEGER"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="apre" property="apre" jdbcType="DECIMAL"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 多条件查询气压传感器状态 -->
    <select id="findLatestStatus" resultMap="AirPressureStatusResultMap">
        SELECT
        trt.device_id,
        d.device_name,
        d.place,
        trt.apre,
--        trt.update_time
        DATE_FORMAT(trt.update_time, '%Y-%m-%d %H:%i:%s') AS update_time
        FROM
        air_pressure_real_time trt
        JOIN
        device d ON trt.device_id = d.device_id
        WHERE
        1=1
        <if test="deviceId != null and deviceId != ''">
            AND trt.device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND d.device_name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="place != null and place != ''">
            AND d.place LIKE CONCAT('%', #{place}, '%')
        </if>
        ORDER BY
        trt.update_time DESC
    </select>

    <!--    获取实时气压平均值-->
    <select id="getAvgAirPressure" resultType="com.agriguard.entity.device.AirPressureRealTimeByMinute">
        SELECT
            CONCAT(DATE_FORMAT(tdc.update_time, '%Y-%m-%d %H:%i'), ':00') AS update_time,
            AVG(tdc.apre) AS apre
        FROM
            air_pressure_data_cache tdc
                JOIN
            device d ON tdc.device_id = d.device_id
        WHERE
            d.place = #{place}
          AND tdc.update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          AND tdc.update_time &lt;= NOW()
        GROUP BY
            update_time
        ORDER BY
            update_time ASC
    </select>
    <!--    获取过去24小时的气压，便于绘制折线统计图-->

    <select id="getHistory24Hours" parameterType="String" resultType="com.agriguard.entity.device.AirPressureHistory24">
        WITH hour_series AS (
            SELECT
            DATE(DATE_SUB(NOW(), INTERVAL n HOUR)) AS target_date,
            HOUR(DATE_SUB(NOW(), INTERVAL n HOUR)) AS target_hour
        FROM (
            SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION
            SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION
            SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION
            SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
            SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
            SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
            ) AS numbers
            ),
            devices_in_area AS (
        SELECT device_id
        FROM device
        WHERE place = #{place}
            )
        SELECT
            hs.target_date AS collect_date,
            hs.target_hour AS collect_hour,
            MAX(t.max_apre) AS max_apre,
            MIN(t.min_apre) AS min_apre,
            AVG(t.avg_apre) AS avg_apre
        FROM
            hour_series hs
                LEFT JOIN (
                SELECT
                    collect_date,
                    collect_hour,
                    max_apre,
                    min_apre,
                    avg_apre
                FROM
                    air_pressure_data_history
                WHERE
                    device_id IN (SELECT device_id FROM devices_in_area)
                        AND (
                        (collect_date = CURDATE() AND collect_hour &lt;= HOUR(NOW()))
                   OR
                    (collect_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND collect_hour > HOUR(NOW()))
            )
    ) t ON hs.target_date = t.collect_date AND hs.target_hour = t.collect_hour
        GROUP BY
            hs.target_date, hs.target_hour
        ORDER BY
            hs.target_date ASC, hs.target_hour ASC
    </select>

    <!--    饼图数据获取sql-->
    <resultMap id="AprePieResultMap" type="com.agriguard.entity.device.AirPressurePie">
        <result property="fanwei" column="fanwei"/>
        <result property="ApreNum" column="ApreNum"/>
    </resultMap>

    <select id="getHistory24HoursForPie" resultMap="AprePieResultMap">
        SELECT
            apre.fanwei,
            SUM(apre.data_count) AS ApreNum
        FROM (
                 SELECT
                     tdh.data_count,
                     CASE
                         WHEN tdh.avg_apre >= 0 AND tdh.avg_apre &lt; 98 THEN 1
                         WHEN tdh.avg_apre >= 98 AND tdh.avg_apre &lt; 100 THEN 2
                         WHEN tdh.avg_apre >= 100 AND tdh.avg_apre &lt; 102 THEN 3
                         WHEN tdh.avg_apre >= 102  THEN 4
                         ELSE 0
                         END AS fanwei
                 FROM
                     air_pressure_data_history tdh FORCE INDEX (idx_device_date)
            JOIN
            device d FORCE INDEX (PRIMARY) ON tdh.device_id = d.device_id
                 WHERE
                     d.place = #{place}
                   AND (
                     (tdh.collect_date = CURDATE() AND tdh.collect_hour &lt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_hour &gt;= HOUR(NOW()))
                    OR
                     (tdh.collect_date &gt; DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND tdh.collect_date &lt; CURDATE())
                     )
             ) AS apre
        GROUP BY apre.fanwei
        ORDER BY apre.fanwei
    </select>

<!--    获取AI控制的气压参数-->
    <select id="getAiAirPressure" resultType="decimal">
        select apre from air_pressure_real_time where device_id = #{deviceId}
    </select>
</mapper>