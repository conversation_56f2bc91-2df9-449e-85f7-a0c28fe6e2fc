package com.agriguard.mapper;

import com.agriguard.entity.device.*;
import com.agriguard.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
@Mapper
public interface HumidityMapper {
    //插入湿度数据
    //更新湿度实时状态表
    void updateHumToRealTime(Integer deviceId, Integer hum,LocalDateTime updateTime);

    //插入湿度值到温度缓存表
    void insertHumToDataCache(Integer deviceId, Integer hum, LocalDateTime updateTime);

    //分析湿度缓存数据
    List<HumidityDataHistory> getHumStatsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    //添加湿度历史数据
    void addHumDateHistory(HumidityDataHistory item);

    //删除指定时间段内的湿度缓存数据
    void deleteHumDateCacheByHour(LocalDateTime startTime, LocalDateTime endTime);

    //查询设备id湿度传感器是否已存在
    int existsByDeviceId(Integer deviceId);

    //添加湿度传感器
    void addHumRealTime(Integer deviceId);

    //查询湿度传感器详情
    Device findHumDetailById(Integer deviceId);

    //分页查询历史数据
    List<HumidityRealTime> findHistoryData(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                              @org.apache.ibatis.annotations.Param("start")LocalDateTime start,
                                              @org.apache.ibatis.annotations.Param("end")LocalDateTime end);

    //分页查询实时状态数据
    List<HumidityRealTime> findLatestStatus(@org.apache.ibatis.annotations.Param("deviceId")Integer deviceId,
                                               @org.apache.ibatis.annotations.Param("deviceName")String deviceName,
                                               @Param("place")String place);

    //查询实时状态数据
    List<HumidityRealTimeByMinute> getAvgHumidity(String place);

    //获取过去24小时的历史湿度数据，用于折线图展示
    List<HumidityHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史湿度数据，用于饼图展示
    List<HumidityPie> getHistory24HoursForPie(String place);


    //获取湿度作为Ai环境参数
    Integer getAiHum();
}
