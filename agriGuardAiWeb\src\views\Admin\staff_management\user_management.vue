<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <el-card class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">
            <el-icon><User /></el-icon>
            用户管理
          </h2>
          <p class="page-description">管理系统用户账户和权限</p>
        </div>
        <div class="header-right">
          <el-button 
            type="primary" 
            :icon="Plus" 
            @click="showAddUserModal"
            size="default"
          >
            添加用户
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 搜索和筛选 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索用户名或邮箱"
              :prefix-icon="Search"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.roleId"
              placeholder="选择角色"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部" :value="null" />
              <el-option label="管理员" :value="1" />
              <el-option label="普通用户" :value="2" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button :icon="Refresh" @click="handleReset">
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 用户表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="users"
        v-loading="loading"
        element-loading-text="加载中..."
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'userId', order: 'ascending' }"
      >
        <el-table-column
          prop="userId"
          label="用户ID"
          width="80"
          sortable
        />
        <el-table-column
          prop="userName"
          label="用户名"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="realName"
          label="真实姓名"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.realName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="email"
          label="邮箱"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="phone"
          label="手机号"
          min-width="130"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.phone || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="角色"
          width="100"
        >
          <template #default="{ row }">
            <el-tag
              :type="getRoleTagType(row.roles)"
              effect="plain"
              size="small"
            >
              {{ getRoleDisplay(row.roles) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="280"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              :icon="Edit"
              size="small"
              @click="showEditModal(row)"
              link
            >
              编辑
            </el-button>
            <el-button
              :type="row.roles && row.roles.includes('ADMIN') ? 'warning' : 'success'"
              :icon="row.roles && row.roles.includes('ADMIN') ? ArrowDown : ArrowUp"
              size="small"
              @click="toggleUserRole(row)"
              link
            >
              {{ row.roles && row.roles.includes('ADMIN') ? '降级' : '提升' }}
            </el-button>
            <el-popconfirm
              title="确定要删除这个用户吗？"
              confirm-button-text="确定"
              cancel-button-text="取消"
              @confirm="deleteUser(row)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  :icon="Delete"
                  size="small"
                  link
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalElements"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="showUserModal"
      :title="isEditing ? '编辑用户' : '添加用户'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="currentUser"
        :rules="userFormRules"
        label-width="80px"
        @submit.prevent="saveUser"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input
            v-model="currentUser.userName"
            placeholder="请输入用户名"
            :disabled="isEditing"
            :prefix-icon="User"
          />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="currentUser.realName"
            placeholder="请输入真实姓名"
            :prefix-icon="UserFilled"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="currentUser.email"
            placeholder="请输入邮箱地址"
            :prefix-icon="Message"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="currentUser.phone"
            placeholder="请输入手机号"
            :prefix-icon="Phone"
          />
        </el-form-item>
        <el-form-item v-if="!isEditing" label="密码" prop="password">
          <el-input
            v-model="currentUser.password"
            type="password"
            placeholder="请输入密码"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        <el-form-item v-if="isEditing" label="新密码" prop="newPassword">
          <el-input
            v-model="currentUser.newPassword"
            type="password"
            placeholder="留空则不修改密码"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeModal">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveUser"
            :loading="saving"
          >
            {{ isEditing ? '更新' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import userManagementApi from '@/api/user_management.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, 
  UserFilled, 
  Plus, 
  Search, 
  Refresh, 
  Edit, 
  Delete, 
  ArrowUp, 
  ArrowDown,
  Message,
  Phone,
  Lock
} from '@element-plus/icons-vue'

const authStore = useAuthStore()
const token = authStore.user?.token

// 通用错误处理函数
const handleApiError = (error, defaultMessage = '操作失败，请重试') => {
  console.error('API错误:', error)
  let message = ''
  
  if (error.response?.status === 403) {
    message = '权限不足：您没有权限进行此操作，请联系管理员'
  } else if (error.response?.status === 401) {
    message = '登录已过期，请重新登录'
    // 可以在这里添加跳转到登录页的逻辑
    // authStore.logout()
    // router.push('/login')
  } else if (error.response?.status === 404) {
    message = '请求的资源不存在'
  } else if (error.response?.status >= 500) {
    message = '服务器错误，请稍后重试'
  } else {
    message = defaultMessage
  }
  
  // 使用Element Plus消息提示
  ElMessage.error(message)
}

// 数据状态
const users = ref([])
const loading = ref(false)
const saving = ref(false)
const showUserModal = ref(false)
const isEditing = ref(false)
const userFormRef = ref()

// 分页状态
const currentPage = ref(1) // Element Plus分页从1开始
const pageSize = ref(10)
const totalElements = ref(0)
const totalPages = ref(0)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  roleId: null
})

// 当前用户表单数据
const currentUser = ref({
  userId: null,
  userName: '',
  realName: '',
  email: '',
  phone: '',
  password: '',
  newPassword: ''
})

// 表单验证规则
const userFormRules = reactive({
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
})

// 角色显示映射
const getRoleDisplay = (roles) => {
  if (roles && roles.includes('ADMIN')) {
    return '管理员'
  }
  return '普通用户'
}

// 角色标签类型
const getRoleTagType = (roles) => {
  if (roles && roles.includes('ADMIN')) {
    return 'danger'
  }
  return 'primary'
}

// 获取用户列表
const fetchUsers = async (page = 1) => {
  try {
    loading.value = true
    if (!token) {
      ElMessage.error('未找到用户token，请重新登录')
      return
    }
    
    // Element Plus分页从1开始，API从0开始，需要转换
    const apiPage = page - 1
    
    // 调用API时传递搜索参数
    const response = await userManagementApi.getUserList(
      token, 
      apiPage, 
      pageSize.value, 
      searchForm.keyword, 
      searchForm.roleId
    )
    
    console.log('API响应:', response.data) // 调试信息
    
    if (response.data.code === 0) {
      const data = response.data.data
      users.value = data.content || []
      totalElements.value = data.totalElements || 0
      totalPages.value = data.totalPages || 0
      currentPage.value = page
      
      console.log('用户列表加载成功:', users.value) // 调试信息
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error) {
    handleApiError(error, '获取用户列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchUsers(1)
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.roleId = null
  currentPage.value = 1
  fetchUsers(1)
}

// 分页大小改变
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUsers(1)
}

// 当前页改变
const handleCurrentChange = (page) => {
  fetchUsers(page)
}

// 显示添加用户模态框
const showAddUserModal = () => {
  currentUser.value = {
    userId: null,
    userName: '',
    realName: '',
    email: '',
    phone: '',
    password: '',
    newPassword: ''
  }
  isEditing.value = false
  showUserModal.value = true
}

// 显示编辑用户模态框
const showEditModal = (user) => {
  currentUser.value = {
    userId: user.userId,
    userName: user.userName,
    realName: user.realName || '',
    email: user.email,
    phone: user.phone || '',
    password: '',
    newPassword: ''
  }
  isEditing.value = true
  showUserModal.value = true
}

// 关闭模态框
const closeModal = () => {
  showUserModal.value = false
  // 重置表单
  if (userFormRef.value) {
    userFormRef.value.resetFields()
  }
}

// 保存用户
const saveUser = async () => {
  if (!userFormRef.value) return
  
  try {
    // 表单验证
    await userFormRef.value.validate()
    
    saving.value = true
    
    if (isEditing.value) {
      // 更新用户
      const updateData = {
        userId: currentUser.value.userId,
        userName: currentUser.value.userName,
        realName: currentUser.value.realName,
        email: currentUser.value.email,
        phone: currentUser.value.phone
      }
      
      // 如果有新密码，添加到更新数据中
      if (currentUser.value.newPassword) {
        updateData.newPassword = currentUser.value.newPassword
      }
      
      const response = await userManagementApi.updateUser(token, updateData)
      
      if (response.data.code === 0) {
        ElMessage.success('用户更新成功')
        closeModal()
        fetchUsers(currentPage.value) // 刷新当前页
      } else {
        ElMessage.error(response.data.message || '更新失败')
      }
    } else {
      // 添加新用户
      const addData = {
        userName: currentUser.value.userName,
        password: currentUser.value.password,
        realName: currentUser.value.realName,
        email: currentUser.value.email,
        phone: currentUser.value.phone
      }
      
      const response = await userManagementApi.addUser(token, addData)
      
      if (response.data.code === 0) {
        ElMessage.success('用户添加成功')
        closeModal()
        fetchUsers(1) // 回到第一页
      } else {
        ElMessage.error(response.data.message || '添加失败')
      }
    }
  } catch (error) {
    if (error !== false) { // 表单验证失败时error为false
      handleApiError(error, '保存用户失败，请重试')
    }
  } finally {
    saving.value = false
  }
}

// 切换用户角色
const toggleUserRole = async (user) => {
  try {
    const isAdmin = user.roles && user.roles.includes('ADMIN')
    const newRoleId = isAdmin ? 2 : 1 // 1-管理员，2-普通用户
    const newRoleName = isAdmin ? '普通用户' : '管理员'
    
    const confirmMessage = `确定要将用户 "${user.userName}" ${isAdmin ? '降级为普通用户' : '提升为管理员'}吗？`
    
    await ElMessageBox.confirm(
      confirmMessage,
      '角色变更确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const roleData = {
      userId: user.userId,
      roleId: newRoleId,
      roleName: newRoleName
    }
    
    const response = await userManagementApi.updateUserRole(token, roleData)
    
    if (response.data.code === 0) {
      ElMessage.success(response.data.message || '角色更新成功')
      fetchUsers(currentPage.value) // 刷新当前页
    } else {
      ElMessage.error(response.data.message || '角色更新失败')
    }
  } catch (error) {
    if (error !== 'cancel') { // 用户取消操作
      handleApiError(error, '角色更新失败，请重试')
    }
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    const response = await userManagementApi.deleteUser(token, {
      userId: user.userId
    })
    
    if (response.data.code === 0) {
      ElMessage.success('用户删除成功')
      fetchUsers(currentPage.value) // 刷新当前页
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    handleApiError(error, '删除用户失败，请重试')
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management {
  padding: 0;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 16px;
  border: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left .page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 16px;
  border: none;
}

.search-form {
  padding: 8px 0;
}

/* 表格卡片 */
.table-card {
  border: none;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格行高度调整 */
:deep(.el-table .el-table__row) {
  height: 60px;
}

:deep(.el-table .el-table__header-wrapper th) {
  background-color: #fafafa;
  color: #303133;
  font-weight: 600;
}

/* 表格操作按钮间距 */
:deep(.el-table .el-button + .el-button) {
  margin-left: 8px;
}

/* 搜索表单样式 */
.search-form .el-row {
  align-items: center;
}

/* 卡片间距 */
.el-card {
  margin-bottom: 16px;
}

.el-card:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .search-form .el-col {
    margin-bottom: 12px;
  }
  
  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}

/* 自定义滚动条 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}
</style>

