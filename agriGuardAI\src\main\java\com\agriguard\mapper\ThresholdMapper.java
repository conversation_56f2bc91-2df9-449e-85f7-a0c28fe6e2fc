package com.agriguard.mapper;

import com.agriguard.dto.SelectDevice;
import com.agriguard.entity.Threshold;
import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface ThresholdMapper {

    //根据阈值id和修改的新的阈值up_value
    @Update("UPDATE threshold SET up_value = #{newValue} WHERE threshold_id = #{thresholdId}")
    void updateThresholdUpValue(Integer thresholdId,String newValue);

    //根据阈值id和修改的新的阈值up_value
    @Update("UPDATE threshold SET down_value = #{newValue} WHERE threshold_id = #{thresholdId}")
    void updateThresholdDownValue(Integer thresholdId,String newValue);

    //根据前端更改的阈值类型和位置，修改阈值的up_value和down_value
    int updateThresholdValue(
            @Param("thresholdType") String thresholdType,
            @Param("place") String place,
            @Param("upValue") String upValue,
            @Param("downValue") String downValue);

    List<Threshold> getAllThresholds();

    List<SelectDevice> selectThresholdTypeThreshold(String place);
}
