package com.agriguard.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class AirPressureDataHistory {
    private BigInteger id;
    private Integer deviceId;
    private LocalDate collectDate;
    private Integer collectHour;
    private BigDecimal maxApre;
    private  BigDecimal minApre;
    private  BigDecimal avgApre;
    private  Integer dataCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
