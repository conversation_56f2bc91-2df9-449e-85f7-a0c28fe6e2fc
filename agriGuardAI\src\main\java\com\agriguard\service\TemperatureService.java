package com.agriguard.service;

import com.agriguard.entity.device.TempHistory24;
import com.agriguard.entity.device.TempPie;
import com.agriguard.entity.device.TempRealTimeByMinute;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.TemperatureRealTime;

import java.time.LocalDate;
import java.util.List;

public interface TemperatureService {

    //插入温度数据
    void insertTemperature();


    //定时对温度数据进行分析，存储历史数据，并进行数据删除
    void TempDateSys();

    //查询设备id温度传感器是否已存在
    boolean existsByDeviceId(Integer deviceId);

    //添加温度传感器
    String addTempRealTime(Integer deviceId);

    //查询温度传感器详情
    Device findTempDetailById(Integer deviceId);

    //分页查询历史数据
    PageResult<TemperatureRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData);

    //分页查询实时状态数据
    PageResult<TemperatureRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, String place);

    //查询实时状态数据
    List<TempRealTimeByMinute> getAvgTemperature(String place);

    //获取过去24小时的历史温度数据，用于折线图展示
    List<TempHistory24> getHistory24Hours(String place);

    //获取过去24小时的历史温度数据，用于饼图展示
    List<TempPie> getHistory24HoursForPie(String place);

}
