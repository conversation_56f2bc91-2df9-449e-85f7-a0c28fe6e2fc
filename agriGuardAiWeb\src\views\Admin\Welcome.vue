<template>
  <!-- 结构保持不变 -->
  <dv-loading v-if="loading">正在加载......</dv-loading>
  <div class="page-container" cv-else>
    <!--标头-->
    <div class="flex-container1">
      <div class="box1 box11">
        <dv-decoration3 style="width:100%;height:30%;" />
        <div class="ai-control-container">
          <dv-button
              @click="toggleAiControl"
              border="Border6"
              :color="aiControlStatus ? '#4ecdc4' : '#ff6b6b'"
              fontSize="16"
          >
            AI控制: {{ aiControlStatus ? '开启' : '关闭' }}
          </dv-button>
        </div>
      </div>
      <div class="box1 box12">
        <dv-border-box1 style="width:100%;height:100%;">
          <div class="container121">
            <div id="box121">
              <dv-button @click="goToDashboard" border="Border6" color="#615ea8"
                         fontSize="30">基于AI的畜牧养殖智能环境监控系统</dv-button>
            </div>
          </div>
        </dv-border-box1>
      </div>
      <div class="box1 box13">
        <dv-decoration3 style="width:100%;height:30%;" />
        <div class="time-display" :style="{ color: timeColor }">当前时间：{{ currentTime }}</div>
      </div>
    </div>
    <!-- 中间区域 -->
    <div class="flex-container2">
      <div class="box2 box21">
        <dv-border-box9 style="width:100%;height:100%;">
          <div class="pie-chart-container">
            <div ref="pieChart" style="width: 100%; height: 100%;"></div>
          </div>
        </dv-border-box9>
      </div>
      <div class="box2 box22">
        <dv-border-box10 style="width:100%;height:100%;">
          <div class="chart-container">
            <div ref="chartRef" class="chart"></div>
          </div>
        </dv-border-box10>
      </div>
      <div class="box2 box23">
        <dv-border-box9 style="width:100%;height:100%;">
          <div ref="chartRef231" class="bar-chart"></div>
        </dv-border-box9>
      </div>
    </div>
    <!-- 底部部分 -->
    <div class="flex-container3">
      <div class="box3 box31">
        <dv-border-box8 style="width:100%;height:100%;">
          <div class="pressure-gauge">
            <div ref="gaugeChart" class="gauge-chart"></div>
            <div class="gauge-info">
              <div class="time31">{{ updateTime }}</div>
            </div>
          </div>
        </dv-border-box8>
      </div>
      <div class="box3 box32">
        <dv-border-box10 style="width:100%;height:100%;">
          <!-- box321 - 固定300px×300px的CO2和NH3折线图 -->
          <div class="box321">
            <div class="chart-title">环境气体监测</div>
            <div ref="gasChart" class="gas-chart"></div>
          </div>
        </dv-border-box10>
      </div>
      <div class="box3 box33">
        <dv-border-box8 :reverse="true" style="width:100%;height:100%;">
          <div ref="chartRef331" class="bar-chart"></div>
        </dv-border-box8>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { BorderBox9 as DvBorderBox9 } from '@kjgl77/datav-vue3';
import { BorderBox11 as DvBorderBox11 } from '@kjgl77/datav-vue3';
import * as echarts from 'echarts';
import { useRouter } from 'vue-router'
import dataScreenAPI from '@/api/dataScreen.js'

const router = useRouter()
const goToDashboard = () => {
  router.push('/index?menu=ai-chat')
}

// 图表实例变量 - 每个图表独立
const pieChartInstance = ref(null) // box21
const lineChartInstance = ref(null) // box22
const gasChartInstance = ref(null) // box32
const gaugeChartInstance = ref(null) // box31
const barChartInstance231 = ref(null) // box23
const barChartInstance331 = ref(null) // box33

// DOM引用
const pieChart = ref(null);
const chartRef = ref(null)
const gasChart = ref(null)
const gaugeChart = ref(null)
const chartRef331 = ref(null)
const chartRef231 = ref(null)

// 数据变量
let loading = ref(true);
const currentPressure = ref(0)
const updateTime = ref('')
const currentTime = ref('')
const timeColor = ref('#4ecdc4')
const aiControlStatus = ref(0) // 0为关闭，1为开启

let timer131
let timer

// 获取AI控制状态
const fetchAiControlStatus = async () => {
  try {
    const response = await dataScreenAPI.getAiControl()
    aiControlStatus.value = response.data.data
  } catch (error) {
    console.error('获取AI控制状态失败:', error)
  }
}

// 切换AI控制状态
const toggleAiControl = async () => {
  try {
    const newStatus = aiControlStatus.value ? 0 : 1
    await dataScreenAPI.setAiControl(newStatus)
    // 重新获取状态以确保同步
    await fetchAiControlStatus()
  } catch (error) {
    console.error('切换AI控制状态失败:', error)
  }
}

// 更新时间函数
const updateTime131 = () => {
  currentTime.value = new Date().toLocaleString()
}

// 统一resize处理函数
const handleResize = () => {
  pieChartInstance.value?.resize()
  lineChartInstance.value?.resize()
  gasChartInstance.value?.resize()
  gaugeChartInstance.value?.resize()
  barChartInstance231.value?.resize()
  barChartInstance331.value?.resize()
}

//box23柱状图
const initChart231 = async () => {
  try {
    // 获取API数据
    const response = (await dataScreenAPI.getSensorCounts()).data.data;

    if (!chartRef231.value) return;

    if (barChartInstance231.value) {
      barChartInstance231.value.dispose();
    }

    barChartInstance231.value = echarts.init(chartRef231.value);

    // 处理API数据，转换为图表需要的格式
    const categories = response.map(item => item.sensorType);
    const values = response.map(item => item.count);

    const option = {
      title: {
        text: '传感器工作数量',
        left: 'center',
        textStyle: {  // 添加textStyle配置
          color: '#5eace5'  // 设置为暗蓝色
        }
      },
      tooltip: {},
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 如果标签太长可以旋转30度
          width: 80,  // 设置标签宽度
          overflow: 'break' // 超出自动换行
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: '工作数量',
        type: 'bar',
        data: values,
        itemStyle: {
          color: '#3398db'
        }
      }]
    };

    barChartInstance231.value.setOption(option);
  } catch (error) {
    console.error('初始化图表失败:', error);
  }
}

//box33柱状图
const initChart331 = async () => {
  const response = (await dataScreenAPI.getWarehouseEquipment()).data.data;

  if (!chartRef331.value) return;

  if (barChartInstance331.value) {
    barChartInstance331.value.dispose();
  }

  barChartInstance331.value = echarts.init(chartRef331.value);

  // 处理API返回的数据
  const categories = response.map(item => item.equipmentType);
  const values = response.map(item => item.count);

  const option = {
    title: {
      text: '仓房设备数量',
      left: 'center',
      textStyle: {  // 添加textStyle配置
        color: '#5eace5'  // 设置为暗蓝色
      }
    },
    tooltip: {},
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        rotate: 30, // 如果标签太长可以旋转30度
        interval: 0 // 强制显示所有标签
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '设备数量',
      type: 'bar',
      data: values,
      itemStyle: {
        color: '#3398db'
      }
    }]
  };

  barChartInstance331.value.setOption(option);
}

//box31气压仪表图
const fetchPressureData = async () => {
  // 假设这是从后端API获取的真实数据
  const response = await dataScreenAPI.getAirPressure();
  return {
    device_id: 1,
    apre: response.data.data.avgAirPressure, // 使用后端返回的avgAirPressure
    // update_time: new Date().toLocaleTimeString()
  }
}

const initChart31 = async () => {
  if (!gaugeChart.value) return

  if (gaugeChartInstance.value) {
    gaugeChartInstance.value.dispose()
  }

  gaugeChartInstance.value = echarts.init(gaugeChart.value)

  const option = {
    backgroundColor: 'transparent',
    series: [{
      type: 'gauge',
      center: ['50%', '55%'],
      radius: '90%',
      min: 99,  // 调整最小值以适配633.05的值
      max: 101,  // 调整最大值
      axisLine: {
        lineStyle: {
          width: 15,
          color: [
            [0.2, '#FF6E76'],
            [0.4, '#FDDD60'],
            [0.6, '#58D9F9'],
            [0.8, '#7CFFB2'],
            [1, '#9287FF']
          ]
        }
      },
      pointer: {
        itemStyle: {
          color: '#d7d6ee'
        },
        length: '60%',
        width: 4
      },
      axisTick: {
        distance: -12,
        length: 5,
        lineStyle: {
          color: '#248ec5',
          width: 1
        }
      },
      splitLine: {
        distance: -12,
        length: 10,
        lineStyle: {
          color: '#0000FF',
          width: 2
        }
      },
      axisLabel: {
        color: '#248ec5',
        distance: 15,
        fontSize: 10
      },
      detail: {
        valueAnimation: true,
        formatter: '{value}',
        color: '#248ec5',
        fontSize: 16,
        offsetCenter: [0, '20%']
      },
      title: {
        offsetCenter: [0, '40%'],
        fontSize: 12,
        color: '#248ec5'
      },
      data: [{
        // value: currentPressure.value,
        value: 99.7,
        name: '气压 (kPa)'
      }]
    }]
  }

  gaugeChartInstance.value.setOption(option)
}

const updateChart31 = (pressure) => {
  if (!gaugeChartInstance.value) return

  currentPressure.value = pressure.toFixed(1)
  gaugeChartInstance.value.setOption({
    series: [{
      data: [{
        // value: pressure,
        value: 99.7,
        name: '气压 (kPa)'
      }]
    }]
  })
}

const loadAndUpdateData = async () => {
  const data = await fetchPressureData()
  // currentPressure.value = data.apre.toFixed(1)
  currentPressure.value = 99.7
  updateTime.value = data.update_time
  updateChart31(data.apre)
}

const startDataRefresh = () => {
  timer = setInterval(async () => {
    await loadAndUpdateData()
  }, 5000)
}

//box32环境气体监测
const fetchGasData = () => {
  return new Promise((resolve) => {
    const hours = Array.from({ length: 12 }, (_, i) => `${i * 2}:00`)

    let co2Base = 600
    const co2Data = hours.map((_, i) => {
      co2Base += (Math.random() - 0.5) * 100
      return Math.max(300, Math.min(1000, Math.round(co2Base)))
    })

    let nh3Base = 15
    const nh3Data = hours.map((_, i) => {
      nh3Base += (Math.random() - 0.5) * 3
      return Math.max(5, Math.min(25, Math.round(nh3Base * 10) / 10))
    })

    resolve({
      hours,
      co2Data,
      nh3Data
    })
  })
}

const initChart32 = async () => {
  const gasData = (await dataScreenAPI.getGasConcentration()).data.data;

  if (!gasChart.value) return;

  if (gasChartInstance.value) {
    gasChartInstance.value.dispose();
  }

  // 处理数据
  const sortedData = [...gasData].sort((a, b) => {
    return a.timePeriod.localeCompare(b.timePeriod);
  });

  const hours = sortedData.map(item => item.timePeriod);
  const co2Data = sortedData.map(item => item.avgCo2 === null ? 0 : item.avgCo2);
  const nh3Data = sortedData.map(item => item.avgAmm === null ? 0 : item.avgAmm);

  gasChartInstance.value = echarts.init(gasChart.value);

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#4e92c6'
        }
      },
      formatter: params => {
        return `时间: ${params[0].axisValue}<br/>
                CO₂: ${params[0].value}ppm<br/>
                NH₃: ${params[1].value}ppm`
      }
    },
    legend: {
      data: ['CO₂浓度', 'NH₃浓度'],
      textStyle: {
        color: '#5eace5', // 修复了缺少#的颜色值
        fontSize: 10
      },
      itemWidth: 12,
      itemHeight: 8,
      top: 0,
      left: 'center',
      itemGap: 20
    },
    grid: {
      left: 40,
      right: 40,
      top: 30,
      bottom: 30,
      containLabel: false
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: hours,
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 9,
        interval: 1,
        formatter: function(value) {
          // 简化显示，只显示开始时间
          return value.split('-')[0];
        }
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: [
      {
        type: 'value',
        name: 'CO₂(ppm)',
        nameTextStyle: {
          color: '#4fd8de',
          fontSize: 10,
          padding: [0, 0, 0, -25]
        },
        min: 0, // 调整最小值，因为数据中有0
        max: 2000, // 调整最大值，因为数据中有超过1000的值
        axisLine: {
          show: true,
          lineStyle: {
            color: '#4fd8de'
          }
        },
        axisLabel: {
          color: '#4fd8de',
          fontSize: 9,
          formatter: '{value}'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(100, 100, 100, 0.1)'
          }
        }
      },
      {
        type: 'value',
        name: 'NH₃(ppm)',
        nameTextStyle: {
          color: '#f845f1',
          fontSize: 10,
          padding: [0, -25, 0, 0]
        },
        min: 0, // 调整最小值
        max: 100, // 调整最大值，因为数据中有88的值
        axisLine: {
          show: true,
          lineStyle: {
            color: '#f845f1'
          }
        },
        axisLabel: {
          color: '#f845f1',
          fontSize: 9,
          formatter: '{value}'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: 'CO₂浓度',
        type: 'line',
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: '#4fd8de'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(79, 216, 222, 0.3)' },
            { offset: 1, color: 'rgba(79, 216, 222, 0.05)' }
          ])
        },
        data: co2Data
      },
      {
        name: 'NH₃浓度', // 修正了NH₃₃为NH₃
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: '#f845f1'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(248, 69, 241, 0.3)' },
            { offset: 1, color: 'rgba(248, 69, 241, 0.05)' }
          ])
        },
        data: nh3Data
      }
    ]
  };

  gasChartInstance.value.setOption(option);
};

//box22温湿度部分
const mockData = {
  temperature: [
    { time: '00:00', value: 22.5 },
    { time: '02:00', value: 21.8 },
    { time: '04:00', value: 20.3 },
    { time: '06:00', value: 21.0 },
    { time: '08:00', value: 23.5 },
    { time: '10:00', value: 25.2 },
    { time: '12:00', value: 26.8 },
    { time: '14:00', value: 27.3 },
    { time: '16:00', value: 26.5 },
    { time: '18:00', value: 25.0 },
    { time: '20:00', value: 23.8 },
    { time: '22:00', value: 22.7 }
  ],
  humidity: [
    { time: '00:00', value: 65 },
    { time: '02:00', value: 68 },
    { time: '04:00', value: 70 },
    { time: '06:00', value: 72 },
    { time: '08:00', value: 68 },
    { time: '10:00', value: 62 },
    { time: '12:00', value: 58 },
    { time: '14:00', value: 55 },
    { time: '16:00', value: 58 },
    { time: '18:00', value: 62 },
    { time: '20:00', value: 65 },
    { time: '22:00', value: 67 }
  ]
}

async function fetchData() {
  try {
    return mockData
  } catch (error) {
    console.error('获取温湿度数据失败:', error)
    return mockData
  }
}

const initChart22 = async () => {
  const rawData = (await dataScreenAPI.getTempHumidity()).data.data;

  if (!chartRef.value) return;

  if (lineChartInstance.value) {
    lineChartInstance.value.dispose();
  }

  // 处理原始数据，转换为图表需要的格式
  const processData = () => {
    // 先按时间排序
    const sortedData = [...rawData].sort((a, b) => {
      return a.timePeriod.localeCompare(b.timePeriod);
    });

    const temperatureData = [];
    const humidityData = [];

    sortedData.forEach(item => {
      // 处理温度数据
      if (item.avgTemp !== null && item.avgTemp !== undefined && item.avgTemp !== 0) {
        temperatureData.push({
          time: item.timePeriod,
          value: item.avgTemp
        });
      }

      // 处理湿度数据
      if (item.avgHumidity !== null && item.avgHumidity !== undefined && item.avgHumidity !== 0) {
        humidityData.push({
          time: item.timePeriod,
          value: item.avgHumidity
        });
      }
    });

    return {
      temperature: temperatureData,
      humidity: humidityData
    };
  };

  const data = processData();

  lineChartInstance.value = echarts.init(chartRef.value);

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['温度(°C)', '湿度(%)'],
      textStyle: {
        color: '#258eaf'
      },
      right: 10,
      top: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        // 使用所有时间点作为x轴数据
        data: [...new Set([...data.temperature.map(item => item.time), ...data.humidity.map(item => item.time)])],
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
        axisLabel: {
          color: '#fff',
          // 如果时间点太多，可以旋转显示
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '温度(°C)',
        min: 0,  // 调整为更宽松的范围
        max: 50, // 根据数据中的最大值调整
        position: 'left',
        axisLine: {
          lineStyle: {
            color: '#FF9F43'
          }
        },
        axisLabel: {
          formatter: '{value}',
          color: '#FF9F43'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      {
        type: 'value',
        name: '湿度(%)',
        min: 0,  // 调整为更宽松的范围
        max: 100, // 湿度最大100%
        position: 'right',
        axisLine: {
          lineStyle: {
            color: '#0ABDE3'
          }
        },
        axisLabel: {
          formatter: '{value}',
          color: '#0ABDE3'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '温度(°C)',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#FF9F43'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(255, 159, 67, 0.5)'
            },
            {
              offset: 1,
              color: 'rgba(255, 159, 67, 0.1)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: data.temperature.map(item => item.value),
        // 确保数据点与x轴对应
        connectNulls: true
      },
      {
        name: '湿度(%)',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#0ABDE3'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(10, 189, 227, 0.5)'
            },
            {
              offset: 1,
              color: 'rgba(10, 189, 227, 0.1)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: data.humidity.map(item => item.value),
        // 确保数据点与x轴对应
        connectNulls: true
      }
    ]
  };

  lineChartInstance.value.setOption(option);
};

//box21饼图部分
const initChart21 = async () => {
  const response = (await dataScreenAPI.getDeviceTypeDistribution()).data.data;

  // 转换数据结构，将后端返回的数据适配到图表需要的格式
  const chartData = response.map(item => ({
    name: item.deviceType,
    value: item.count
  }));

  if (!pieChart.value) return

  if (pieChartInstance.value) {
    pieChartInstance.value.dispose()
  }

  pieChartInstance.value = echarts.init(pieChart.value)

  const option = {
    title: {
      text: '设备类型分布',
      left: 'center',
      textStyle: {
        color: '#73c0de',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#73c0de'
      }
    },
    series: [
      {
        name: '设备类型',
        type: 'pie',
        radius: ['35%', '55%'],
        position: 'left',
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#73c0de',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
            color: '#73c0de'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData  // 使用转换后的数据
      }
    ],
    color: [
      '#5470c6', '#91cc75', '#fac858', '#ee6666',
      '#73c0de', '#3ba272', '#fc8452', '#9a60b4'
    ]
  }

  pieChartInstance.value.setOption(option)
}

onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 1000)
  updateTime131()
  timer131 = setInterval(updateTime131, 1000)

  // 初始化所有图表
  initChart21()
  initChart22()
  initChart32()
  initChart31()
  initChart331()
  initChart231()

  // 加载数据
  loadAndUpdateData()
  startDataRefresh()

  // 获取AI控制状态
  fetchAiControlStatus()

  // 添加统一resize监听
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  // 清理定时器
  clearInterval(timer131)
  clearInterval(timer)

  // 移除resize监听
  window.removeEventListener('resize', handleResize)

  // 销毁所有图表实例
  pieChartInstance.value?.dispose()
  lineChartInstance.value?.dispose()
  gasChartInstance.value?.dispose()
  gaugeChartInstance.value?.dispose()
  barChartInstance231.value?.dispose()
  barChartInstance331.value?.dispose()
})
</script>

<style scoped>
/* 样式保持不变 */
.page-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  margin: 0 auto;
  box-sizing: border-box;
  flex-direction: column;
  background:
      url('@/assets/bigdata1.webp') center/cover no-repeat;
}

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

.page-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-y: auto;
}

.flex-container1 {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  height: 10%;
  display: flex;
}

.flex-container2 {
  position: absolute;
  top: 10%;
  left: 0;
  right: 0;
  height: 50%;
  display: flex;
}

.flex-container3 {
  position: absolute;
  top: 60%;
  left: 0;
  right: 0;
  height: 40%;
  display: flex;
}

.box11,
.box13 {
  width: 30%;
  justify-content: center;
  align-items: center;
}

.box12 {
  width: 70%;
}

.box21,
.box23 {
  width: calc(200% / 7);
}

.box22 {
  width: calc(300% / 7);
  display: flex;

}

.box31,
.box32,
.box33 {
  width: calc(100% / 3);
}

.box11 {
  background-color: transparent;
}

.box12 {
  background-color: transparent;
}

.box13 {
  background-color: transparent;
}

.box21 {
  background-color: transparent;
}

.box22 {
  background-color: transparent;
}

.box23 {
  background-color: transparent;
}

.box31 {
  background-color: transparent;
}

.box32 {
  background-color: transparent;
}

.box33 {
  background-color: transparent;
}

.container121 {
  position: relative;
  left: 15%;
  width: 70%;
  height: 100%;
  border: 1px solid #0f66c9;
}

#box121 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  height: 100%;
  //background-color: transparent(20, 159, 159);
  min-height: 1px;
  transition: transform 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  font-size: 2.0rem;
  font-family: 'Arial', sans-serif;
}

#box121:hover {
  transform: translate(-50%, -50%) scale(1.10);
}

[class^="box"] {
  box-sizing: border-box;
  border: 1px solid white;
  min-height: 1px;
}

.pie-chart-container {
  width: 100%;
  height: 100%;
  /* border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 150, 255, 0.3); */
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

.box321 {
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(223, 15, 15, 0.1);
  display: flex;
  flex-direction: column;
}

.chart-title {
  color: #258eaf;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  text-align: center;
}

.gas-chart {
  width: 100%;
  height: 90%;
  flex: 1;
}

.pressure-gauge {
  width: 90%;
  height: 90%;
  position: relative;
}

.gauge-chart {
  width: 100%;
  height: 100%;
}

.gauge-info {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
}

.value {
  font-size: 18px;
  font-weight: bold;
  color: #db2020;
}

.time31 {
  font-size: 12px;
  color: #db2020;
  margin-top: 2px;
}

.bar-chart {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

.time-display {
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  border: 2px solid;
  border-image: linear-gradient(45deg, purple, orange) 1;
  animation: borderAnimation 3s infinite alternate;
}

.ai-control-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70%;
  width: 100%;
}

.ai-control-container .dv-button {
  width: 80%;
  height: 60%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.ai-control-container .dv-button:hover {
  transform: scale(1.05);
}

@keyframes borderAnimation {
  from {
    border-image-source: linear-gradient(45deg, purple, orange);
  }

  to {
    border-image-source: linear-gradient(45deg, orange, purple);
  }
}
</style>
