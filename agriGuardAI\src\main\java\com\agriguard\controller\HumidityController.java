package com.agriguard.controller;

import com.agriguard.entity.device.HumidityHistory24;
import com.agriguard.entity.device.HumidityPie;
import com.agriguard.entity.device.HumidityRealTimeByMinute;
import com.agriguard.entity.device.TempPie;
import com.agriguard.pojo.*;
import com.agriguard.service.HumidityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/humidity")
public class HumidityController {
    @Autowired
    private HumidityService humidityService;

    // 使用Cron表达式配置每小时执行一次的定时任务
    //每个小时执行一次
//    @Scheduled(cron = "0 0 * * * ?")
    //使用每个小时执行一次
    @Scheduled(cron = "0 0 0/1 * * ?  ")
    public void scheduledTask() {
        try {
            // 调用Service中的业务方法
            humidityService.HumidityDateSys();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = humidityService.addHumidityRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    //获取湿度传感器详情
    @GetMapping("/detail")
    public Result<Device> getHumidityDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device humDetail = humidityService.findHumidityDetailById(deviceId);
            return Result.success(humDetail);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //分页查询湿度历史状态
    @GetMapping("/history")
    public Result<PageResult<HumidityRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData
    ) {
        try {
            PageResult<HumidityRealTime> pageResult = humidityService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //多条件分页查询湿度传感器状态
    @GetMapping("/list")
    public Result<PageResult<HumidityRealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,

            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<HumidityRealTime> pageResult = humidityService.findLatestStatus(pageNum, pageSize, deviceId, deviceName, place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    //获取每个分钟的平均湿度用于实时湿度变化展示，根据位置来查询--某个位置的
    @GetMapping("/avg")
    public Result<List<HumidityRealTimeByMinute>> getAvgHumidity(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<HumidityRealTimeByMinute> avgHumidity = humidityService.getAvgHumidity(place);
            return Result.success(avgHumidity);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史湿度数据，用于折线图展示
    @GetMapping("/history24")
    public Result<List<HumidityHistory24>> getHistory24Hours(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<HumidityHistory24> history24Hours = humidityService.getHistory24Hours(place);
            return Result.success(history24Hours);  // 返回成功响应
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }

    //获取过去24小时的历史湿度数据，用于饼图范围展示
    @GetMapping("/history24pie")
    public Result<List<HumidityPie>> getHistory24HoursForPie(@RequestHeader String Authorization, @RequestParam String place) {
        try {
            List<HumidityPie> HumidityPie = humidityService.getHistory24HoursForPie(place);
            return Result.success(HumidityPie);  // 返回成功响应

        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败:" + e.getMessage());
        }
    }
}
