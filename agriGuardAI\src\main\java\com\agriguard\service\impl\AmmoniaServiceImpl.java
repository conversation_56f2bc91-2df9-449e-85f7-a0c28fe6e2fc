package com.agriguard.service.impl;

import com.agriguard.entity.device.*;
import com.agriguard.mapper.AmmoniaMapper;
import com.agriguard.pojo.*;
import com.agriguard.service.AmmoniaService;
import com.agriguard.service.DeviceService;
import com.agriguard.utils.DateTimeUtil;
import com.agriguard.utils.TemperatureGenerator;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class AmmoniaServiceImpl implements AmmoniaService {
    @Autowired
    private AmmoniaMapper ammoniaMapper;
    @Autowired
    private DeviceService deviceService;

    @Override
    public void insertTemperature() {
        //默认氨气传感器设备编号为1
        int deviceId = 1;
        //模拟生成华为云氨气数据
        Integer amm = Integer.valueOf((TemperatureGenerator.generateTemperature())/10);
        System.out.println("生成的氨气: " + amm);
        //生成数据更新时间
        LocalDateTime updateTime = java.time.LocalDateTime.now();
        //将氨气实时状态表进行更新,实时状态表就不进行时间更新了，数据库会自己进行更新
        ammoniaMapper.updateAmmoniaToRealTime(deviceId,amm,updateTime);
        //将氨气数据插入温度缓存表
        ammoniaMapper.insertAmmoniaToDataCache(deviceId,amm,updateTime);
    }

    //定时对氨气数据进行分析，存储历史数据，并进行数据删除
    @Override
    public void AmmoniaDateSys() {

        //获取当前时间
        String[] hourRange = DateTimeUtil.getPreviousHourRange();
        System.out.println("上一个小时范围:");
        System.out.println("开始时间: " + hourRange[0]);
        System.out.println("结束时间: " + hourRange[1]);
        //将String类型的时间转换为LocalDateTime类型
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将字符串转换为LocalDateTime
        LocalDateTime startTime = LocalDateTime.parse(hourRange[0], formatter);
        LocalDateTime endTime = LocalDateTime.parse(hourRange[1], formatter);
        // 调用Mapper方法获取数据
        List<AmmoniaDataHistory> result = ammoniaMapper.getAmmoniaStatsByTimeRange(startTime, endTime);

        //h获取小时数
        int hour = startTime.getHour();
        // 设置插入时间为当前时间
        LocalDateTime now = LocalDateTime.now();
//        // 从now获取日期部分，精确到天
//        LocalDate currentDate = now.toLocalDate();
        // 从startTime获取日期部分，精确到天
        LocalDate currentDate = startTime.toLocalDate();
        for (AmmoniaDataHistory item : result) {
            item.setCollectHour(hour);
            item.setUpdateTime(now);
            item.setCollectDate(currentDate);
        }
        System.out.println("执行了氨气缓存表的定时任务");

        // 插入数据到历史表
        for (AmmoniaDataHistory item : result) {
            ammoniaMapper.addAmmoniaDateHistory(item);
        }

        //删除缓存表中的数据
        ammoniaMapper.deleteAmmoniaDateCacheByHour(startTime,endTime);
    }

    //查询设备id氨气传感器是否已存在
    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return ammoniaMapper.existsByDeviceId(deviceId) > 0;
    }

    @Override
    public String addAmmoniaRealTime(Integer deviceId) {
        // 设备存在性校验
        Device device = deviceService.findById(deviceId);
        if (device == null) return "设备不存在";

        // 设备类型校验
        if(!"氨气传感器".equals(device.getType())) return "该设备不为氨气传感器";

        // 重复性校验
        if(ammoniaMapper.existsByDeviceId(deviceId) > 0) return "该氨气传感器已存在";

        ammoniaMapper.addAmmoniaRealTime(deviceId);
        return "添加成功";
    }

    //查询氨气传感器详情
    @Override
    public Device findAmmoniaDetailById(Integer deviceId) {
        System.out.println("查询氨气传感器详情成功");
        return ammoniaMapper.findAmmoniaDetailById(deviceId);
    }

    public PageResult<AmmoniaRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        //创建PageResult对象
        PageResult<AmmoniaRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 转换日期范围
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        // 调用 mapper 进行查询
        List<AmmoniaRealTime> as = ammoniaMapper.findHistoryData(deviceId, start, end);
        // 将查询结果转换为 Page 对象
        Page<AmmoniaRealTime> p = (Page<AmmoniaRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询氨气历史数据成功");
        return pb;
    }


    //分页查询实时状态数据
    @Override
    public PageResult<AmmoniaRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, String place) {
        //创建PageResult对象
        PageResult<AmmoniaRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<AmmoniaRealTime> as = ammoniaMapper.findLatestStatus(deviceId, deviceName, place);
        // 将查询结果转换为 Page 对象
        Page<AmmoniaRealTime> p = (Page<AmmoniaRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询风机状态成功");
        return pb;
    }

    //查询实时状态数据
    @Override
    public List<AmmoniaRealTimeByMinute> getAvgAmmonia(String place) {
        System.out.println(place);
        return ammoniaMapper.getAvgAmmonia(place);
    }
    //获取过去24小时的历史氨气数据，用于折线图展示
    @Override
    public List<AmmoniaHistory24> getHistory24Hours(String place) {
        return ammoniaMapper.getHistory24Hours(place);
    }

    //获取过去24小时的历史氨气数据，用于饼图展示
    @Override
    public List<AmmoniaPie> getHistory24HoursForPie(String place) {
        return ammoniaMapper.getHistory24HoursForPie(place);
    }
}
