package com.agriguard.service.impl;

import com.agriguard.mapper.DeviceMapper;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;
import com.agriguard.service.DeviceService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.github.pagehelper.Page;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Service
public class DeviceServiceImpl implements DeviceService {
    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public Device findById(Integer id) {
        return deviceMapper.findById(id);
    }

    @Override
    public PageResult<Device> find(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName,
                                   String type, String place, String model, String factory, LocalDate installTime) {
        //创建PageResult对象
        PageResult<Device> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<Device> as = deviceMapper.find(deviceId, deviceName, type, place, model, factory, installTime);
        // 将查询结果转换为 Page 对象
        Page<Device> p = (Page<Device>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询设备成功");
        return pb;
    }

    @Override
    public List<Device> findAll() {
        List<Device> devices = deviceMapper.findAll();
        if(devices.isEmpty() || devices == null) {
            throw new RuntimeException("设备列表为空");
        }
        return devices;
    }

    @Override
    @Transactional
    public void addDevice(Device device) {
        // 使用LocalDateTime.now()代替Date
        device.setUpdate_time(LocalDateTime.now());
        deviceMapper.insertDevice(device);
    }

    @Override
    @Transactional
    public void updateDevice(Device device) {
        // 先查询设备是否存在
        Device existingDevice = deviceMapper.findById(device.getDeviceId());
        if (existingDevice == null) {
            throw new RuntimeException("设备不存在");
        }

        // 设置更新时间
        device.setUpdateTime(LocalDateTime.now());
        deviceMapper.updateDevice(device);
    }

    @Override
    @Transactional
    public void deleteDevice(Integer deviceId) {
        Device existingDevice = deviceMapper.findById(deviceId);
        if (existingDevice == null) {
            throw new RuntimeException("设备不存在");
        }
        deviceMapper.deleteById(deviceId);
    }

    @Override
    public List<String> get_all_location_of_device(String deviceType) {
        return deviceMapper.get_all_location_of_device(deviceType);
    }

    //获取设备总数
    @Override
    public Integer getTotalCount(String cache, String place) {
        String[] a = get_name(cache);
        if (a == null) {
            return null;
        }
        return deviceMapper.getTotalCount(a[0], a[1], place);
    }

    //获取异常设备数量
    @Override
    public Integer getAbnormalCount(String cache, String place) {
        String[] a = get_name(cache);
        if (a == null) {
            return null;
        }
        return deviceMapper.getAbnormalCount(a[0], a[1], place);
    }
    //获取异常设备id
    @Override
    public List<Integer> getAbnormalDeviceIds(String cache, String place) {
        String[] a = get_name(cache);
        if (a == null) {
            return Collections.emptyList();
        }
        return deviceMapper.getAbnormalDeviceIds(place, a[0], a[1]);
    }

    private String[] get_name(String cache_name){
        String cache;
        String real;
        switch (cache_name) {
            case "温度传感器":
                real = "temperature_real_time";
                cache = "temperature_data_cache";
                break;
            case "湿度传感器":
                real = "humidity_real_time";
                cache = "humidity_data_cache";
                break;
            case "二氧化碳传感器":
                real = "co2_real_time";
                cache = "co2_data_cache";
                break;
            case "氨气传感器":
                real = "ammonia_real_time";
                cache = "ammonia_data_cache";
                break;
            case "气压传感器":
                real = "air_pressure_real_time";
                cache = "air_pressure_data_cache";
                break;
            case "电流传感器":
                real = "electricity_real_time";
                cache = "electricity_data_cache";
                break;
            default:
                return null;
        }
        return new String[]{real,cache};
    }
}
