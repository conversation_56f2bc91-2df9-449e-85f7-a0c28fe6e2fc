package com.agriguard.service.aichat.function.impl;

import com.agriguard.service.aichat.function.FunctionDefinition;
import com.agriguard.service.aichat.function.FunctionExecutor;
import com.agriguard.service.FanService;
import com.agriguard.service.HeatService;
import com.agriguard.service.NappeService;
import com.agriguard.service.WindowService;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.FanRealTime;
import com.agriguard.pojo.HeatRealTime;
import com.agriguard.pojo.NappeRealTime;
import com.agriguard.pojo.WindowRealTime;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 获取设备运行状态的工具函数
 * 包括风机、水帘、加热片、窗户等设备状态
 */
@Slf4j
@Component
public class GetDeviceStatusFunction implements FunctionExecutor {
    
    @Autowired
    private FanService fanService;
    
    @Autowired
    private HeatService heatService;
    
    @Autowired
    private NappeService nappeService;
    
    @Autowired
    private WindowService windowService;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public FunctionDefinition getFunctionDefinition() {
        // 定义参数Schema
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // 设备类型参数（可选）
        Map<String, Object> deviceTypeParam = new HashMap<>();
        deviceTypeParam.put("type", "string");
        deviceTypeParam.put("enum", Arrays.asList("fan", "nappe", "heat", "window", "all"));
        deviceTypeParam.put("description", "设备类型，默认为all");
        deviceTypeParam.put("default", "all");
        properties.put("device_type", deviceTypeParam);
        
        // 设备ID参数（可选）
        Map<String, Object> deviceIdParam = new HashMap<>();
        deviceIdParam.put("type", "integer");
        deviceIdParam.put("description", "具体设备ID，可选");
        properties.put("device_id", deviceIdParam);
        
        // 位置参数（可选）
        Map<String, Object> locationParam = new HashMap<>();
        locationParam.put("type", "string");
        locationParam.put("description", "设备所在区域，可选");
        properties.put("location", locationParam);
        
        parameters.put("properties", properties);
        parameters.put("required", new String[]{}); // 没有必需参数
        
        return FunctionDefinition.builder()
                .name("get_device_status")
                .description("获取农业设备的当前运行状态，包括风机、水帘、加热片、窗户等")
                .parameters(parameters)
                .requiresAuth(false)
                .permissionLevel("read")
                .requiresConfirmation(false)
                .build();
    }
    
    @Override
    public Object execute(JSONObject parameters, Long userId) throws Exception {
        try {
            // 获取参数
            String deviceType = parameters.getString("device_type");
            Integer deviceId = parameters.getInteger("device_id");
            String location = parameters.getString("location");
            
            // 处理默认值
            if (deviceType == null || deviceType.trim().isEmpty()) {
                deviceType = "all";
            }
            
            log.info("获取设备状态，参数: deviceType={}, deviceId={}, location={}, userId={}", 
                    deviceType, deviceId, location, userId);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("timestamp", LocalDateTime.now().format(FORMATTER));

            // 构建请求参数Map，避免Map.of()的null值问题
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("device_type", deviceType);
            requestParams.put("device_id", deviceId);
            requestParams.put("location", location);
            result.put("request_params", requestParams);
            
            // 获取设备状态数据
            Map<String, Object> deviceData = new HashMap<>();
            
            if ("all".equals(deviceType)) {
                // 获取所有类型设备状态
                deviceData.put("fans", getFanStatus(deviceId, location));
                deviceData.put("heat_devices", getHeatStatus(deviceId, location));
                deviceData.put("nappe_devices", getNappeStatus(deviceId, location));
                deviceData.put("windows", getWindowStatus(deviceId, location));
            } else {
                // 获取指定类型设备状态
                switch (deviceType.toLowerCase()) {
                    case "fan":
                        deviceData.put("fans", getFanStatus(deviceId, location));
                        break;
                    case "heat":
                        deviceData.put("heat_devices", getHeatStatus(deviceId, location));
                        break;
                    case "nappe":
                        deviceData.put("nappe_devices", getNappeStatus(deviceId, location));
                        break;
                    case "window":
                        deviceData.put("windows", getWindowStatus(deviceId, location));
                        break;
                    default:
                        throw new IllegalArgumentException("不支持的设备类型: " + deviceType);
                }
            }
            
            result.put("device_data", deviceData);
            result.put("data_source", "AgriGuard设备监控系统");
            result.put("status", "success");
            
            log.info("设备状态获取成功，返回设备类型: {}", deviceData.keySet());
            
            return result;
            
        } catch (Exception e) {
            log.error("获取设备状态失败", e);
            throw new Exception("获取设备状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取风机状态
     */
    private List<Map<String, Object>> getFanStatus(Integer deviceId, String location) {
        try {
            // 使用分页查询获取风机状态，这里获取前10条记录
            PageResult<?> pageResult = fanService.findLatestStatus(
                    1, 10, deviceId, null, null, location, null);

            List<Map<String, Object>> fanList = new ArrayList<>();
            if (pageResult != null && pageResult.getRows() != null) {
                for (Object item : pageResult.getRows()) {
                    Map<String, Object> fanData = new HashMap<>();

                    // 处理HashMap类型的返回数据
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> fanMap = (Map<String, Object>) item;

                        fanData.put("device_id", fanMap.get("deviceId"));
                        fanData.put("device_name", fanMap.get("deviceName") != null ?
                                fanMap.get("deviceName") : "风机" + fanMap.get("deviceId"));
                        fanData.put("device_type", "fan");
                        fanData.put("status", fanMap.get("fanStatus"));
                        fanData.put("status_text",
                                Integer.valueOf(1).equals(fanMap.get("fanStatus")) ? "运行中" : "已停止");
                        fanData.put("gear", fanMap.get("gear"));
                        fanData.put("location", fanMap.get("place") != null ?
                                fanMap.get("place") : location);
                        fanData.put("last_update", fanMap.get("updateTime"));
                    } else {
                        // 如果是POJO类型，按原来的方式处理
                        FanRealTime fan = (FanRealTime) item;
                        fanData.put("device_id", fan.getDeviceId());
                        fanData.put("device_name", "风机" + fan.getDeviceId());
                        fanData.put("device_type", "fan");
                        fanData.put("status", fan.getFanStatus());
                        fanData.put("status_text", fan.getFanStatus() == 1 ? "运行中" : "已停止");
                        fanData.put("gear", fan.getGear());
                        fanData.put("location", location);
                        fanData.put("last_update", fan.getUpdateTime() != null ?
                                fan.getUpdateTime().format(FORMATTER) : null);
                    }
                    fanList.add(fanData);
                }
            }
            return fanList;
        } catch (Exception e) {
            log.warn("获取风机状态失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "风机状态获取失败: " + e.getMessage());
            return Arrays.asList(errorData);
        }
    }
    
    /**
     * 获取加热片状态
     */
    private List<Map<String, Object>> getHeatStatus(Integer deviceId, String location) {
        try {
            PageResult<?> pageResult = heatService.findLatestStatus(
                    1, 10, deviceId, null, null, location);

            List<Map<String, Object>> heatList = new ArrayList<>();
            if (pageResult != null && pageResult.getRows() != null) {
                for (Object item : pageResult.getRows()) {
                    Map<String, Object> heatData = new HashMap<>();

                    // 处理HashMap类型的返回数据
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> heatMap = (Map<String, Object>) item;

                        heatData.put("device_id", heatMap.get("deviceId"));
                        heatData.put("device_name", heatMap.get("deviceName") != null ?
                                heatMap.get("deviceName") : "加热片" + heatMap.get("deviceId"));
                        heatData.put("device_type", "heat");
                        heatData.put("status", heatMap.get("heatStatus"));
                        heatData.put("status_text",
                                Integer.valueOf(1).equals(heatMap.get("heatStatus")) ? "加热中" : "已停止");
                        heatData.put("location", heatMap.get("place") != null ?
                                heatMap.get("place") : location);
                        heatData.put("last_update", heatMap.get("updateTime"));
                    } else {
                        // 如果是POJO类型，按原来的方式处理
                        HeatRealTime heat = (HeatRealTime) item;
                        heatData.put("device_id", heat.getDeviceId());
                        heatData.put("device_name", "加热片" + heat.getDeviceId());
                        heatData.put("device_type", "heat");
                        heatData.put("status", heat.getHeatStatus());
                        heatData.put("status_text", heat.getHeatStatus() == 1 ? "加热中" : "已停止");
                        heatData.put("location", location);
                        heatData.put("last_update", heat.getUpdateTime() != null ?
                                heat.getUpdateTime().format(FORMATTER) : null);
                    }
                    heatList.add(heatData);
                }
            }
            return heatList;
        } catch (Exception e) {
            log.warn("获取加热片状态失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "加热片状态获取失败: " + e.getMessage());
            return Arrays.asList(errorData);
        }
    }
    
    /**
     * 获取水帘状态
     */
    private List<Map<String, Object>> getNappeStatus(Integer deviceId, String location) {
        try {
            PageResult<?> pageResult = nappeService.findLatestStatus(
                    1, 10, deviceId, null, null, location);

            List<Map<String, Object>> nappeList = new ArrayList<>();
            if (pageResult != null && pageResult.getRows() != null) {
                for (Object item : pageResult.getRows()) {
                    Map<String, Object> nappeData = new HashMap<>();

                    // 处理HashMap类型的返回数据
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> nappeMap = (Map<String, Object>) item;

                        nappeData.put("device_id", nappeMap.get("deviceId"));
                        nappeData.put("device_name", nappeMap.get("deviceName") != null ?
                                nappeMap.get("deviceName") : "水帘" + nappeMap.get("deviceId"));
                        nappeData.put("device_type", "nappe");
                        nappeData.put("status", nappeMap.get("nappeStatus"));
                        nappeData.put("status_text",
                                Integer.valueOf(1).equals(nappeMap.get("nappeStatus")) ? "运行中" : "已停止");
                        nappeData.put("location", nappeMap.get("place") != null ?
                                nappeMap.get("place") : location);
                        nappeData.put("last_update", nappeMap.get("updateTime"));
                    } else {
                        // 如果是POJO类型，按原来的方式处理
                        NappeRealTime nappe = (NappeRealTime) item;
                        nappeData.put("device_id", nappe.getDeviceId());
                        nappeData.put("device_name", "水帘" + nappe.getDeviceId());
                        nappeData.put("device_type", "nappe");
                        nappeData.put("status", nappe.getNappeStatus());
                        nappeData.put("status_text", nappe.getNappeStatus() == 1 ? "运行中" : "已停止");
                        nappeData.put("location", location);
                        nappeData.put("last_update", nappe.getUpdateTime() != null ?
                                nappe.getUpdateTime().format(FORMATTER) : null);
                    }
                    nappeList.add(nappeData);
                }
            }
            return nappeList;
        } catch (Exception e) {
            log.warn("获取水帘状态失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "水帘状态获取失败: " + e.getMessage());
            return Arrays.asList(errorData);
        }
    }
    
    /**
     * 获取窗户状态
     */
    private List<Map<String, Object>> getWindowStatus(Integer deviceId, String location) {
        try {
            PageResult<?> pageResult = windowService.findLatestStatus(
                    1, 10, deviceId, null, null, location);

            List<Map<String, Object>> windowList = new ArrayList<>();
            if (pageResult != null && pageResult.getRows() != null) {
                for (Object item : pageResult.getRows()) {
                    Map<String, Object> windowData = new HashMap<>();

                    // 处理HashMap类型的返回数据
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> windowMap = (Map<String, Object>) item;

                        windowData.put("device_id", windowMap.get("deviceId"));
                        windowData.put("device_name", windowMap.get("deviceName") != null ?
                                windowMap.get("deviceName") : "窗户" + windowMap.get("deviceId"));
                        windowData.put("device_type", "window");

                        // 处理窗户状态，可能是String或Integer类型
                        Object statusObj = windowMap.get("windowStatus");
                        Integer status = 0;
                        if (statusObj != null) {
                            if (statusObj instanceof String) {
                                status = "1".equals(statusObj) ? 1 : 0;
                            } else if (statusObj instanceof Integer) {
                                status = (Integer) statusObj;
                            }
                        }
                        windowData.put("status", status);
                        windowData.put("status_text", status == 1 ? "已开启" : "已关闭");
                        windowData.put("location", windowMap.get("place") != null ?
                                windowMap.get("place") : location);
                        windowData.put("last_update", windowMap.get("updateTime"));
                    } else {
                        // 如果是POJO类型，按原来的方式处理
                        WindowRealTime window = (WindowRealTime) item;
                        windowData.put("device_id", window.getDeviceId());
                        windowData.put("device_name", "窗户" + window.getDeviceId());
                        windowData.put("device_type", "window");
                        // WindowRealTime的windowStatus是String类型，需要转换
                        String statusStr = window.getWindowStatus();
                        Integer status = "1".equals(statusStr) ? 1 : 0;
                        windowData.put("status", status);
                        windowData.put("status_text", status == 1 ? "已开启" : "已关闭");
                        windowData.put("location", location);
                        // WindowRealTime的updateTime是String类型，直接使用
                        windowData.put("last_update", window.getUpdateTime());
                    }
                    windowList.add(windowData);
                }
            }
            return windowList;
        } catch (Exception e) {
            log.warn("获取窗户状态失败: {}", e.getMessage());
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", "窗户状态获取失败: " + e.getMessage());
            return Arrays.asList(errorData);
        }
    }
    
    @Override
    public boolean validateParameters(JSONObject parameters) {
        // 参数验证
        try {
            String deviceType = parameters.getString("device_type");
            if (deviceType != null) {
                List<String> validTypes = Arrays.asList("fan", "nappe", "heat", "window", "all");
                if (!validTypes.contains(deviceType.toLowerCase())) {
                    log.warn("无效的设备类型: {}", deviceType);
                    return false;
                }
            }
            
            Integer deviceId = parameters.getInteger("device_id");
            if (deviceId != null && deviceId <= 0) {
                log.warn("无效的设备ID: {}", deviceId);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("参数验证失败", e);
            return false;
        }
    }
    
    @Override
    public boolean checkPermission(Long userId) {
        // 设备状态查询不需要特殊权限
        return true;
    }
}
