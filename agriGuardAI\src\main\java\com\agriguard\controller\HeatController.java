package com.agriguard.controller;

import com.agriguard.pojo.*;
import com.agriguard.service.HeatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/heat")
public class HeatController {

    @Autowired
    private HeatService heatService;

    // 添加加热片状态，添加加热片设备id就可以
    @PostMapping("/add")
    public Result<String> add(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            String result = heatService.addHeatRealTime(deviceId);
            if (!"添加成功".equals(result)) {
                return Result.error(result);
            }
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    // 分页查询加热片状态（默认工作状态为1）
    @GetMapping("/list")
    public Result<PageResult<HeatRealTime>> list(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam(required = false) Integer deviceId,
            @RequestParam(required = false) String deviceName,
            @RequestParam(required = false) Integer heatStatus,
            @RequestParam(required = false) String place
    ) {
        try {
            PageResult<HeatRealTime> pageResult = heatService.findLatestStatus(pageNum, pageSize, deviceId,deviceName, heatStatus,place);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    //查询加热片详情
    @GetMapping("/detail")
    public Result<Device> getHeatDetail(@RequestHeader String Authorization, @RequestParam Integer deviceId) {
        try {
            Device fanDetail = heatService.findHeatDetailById(deviceId);
            return Result.success(fanDetail);
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败!");
        }
    }

    @PutMapping("/updatestatus")
    public Result<String> updateStatus(@RequestHeader String Authorization,
                                       @RequestParam String deviceId,
                                       @RequestParam Integer heatStatus
    ) {
        try {
            Integer deviceIda = Integer.parseInt(deviceId);
            heatService.updateStatus(deviceIda, heatStatus);
            return Result.success("修改成功");
        }   catch (Exception e) {
            e.printStackTrace();
            return Result.error("修改失败!");
        }
    }

    // 获取水帘历史数据
    @GetMapping("/history")
    public Result<PageResult<NappeRealTime>> getHistoryData(
            @RequestHeader String Authorization,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam Integer deviceId,
            @RequestParam(required = false) LocalDate beginData,
            @RequestParam(required = false) LocalDate endData) {
        try {
            PageResult<NappeRealTime> pageResult = heatService.findHistoryData(pageNum, pageSize, deviceId, beginData, endData);
            return Result.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
