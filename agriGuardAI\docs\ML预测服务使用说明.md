# ML预测服务使用说明

## 概述

AgriGuard系统已集成机器学习预测功能，可以根据环境传感器数据自动预测最优的设备控制策略。

## 功能特性

- **智能预测**: 基于温度、湿度、氨气、CO2、气压等环境参数预测设备控制状态
- **实时分析**: 获取当前环境数据并进行实时预测
- **安全可靠**: 包含异常处理和默认值策略
- **易于测试**: 提供完整的测试接口

## 系统架构

```
AgriGuard Java服务 -> MlPredictionService -> Python ML API -> 训练好的模型
```

## 快速开始

### 1. 启动Python ML服务

确保Python ML服务已经启动（默认端口5000）：
```bash
python python_ml_service.py
```

### 2. 配置Java服务

在`application.yml`中配置ML API地址：
```yaml
ml:
  api:
    url: http://localhost:5000
```

### 3. 测试服务

使用以下接口测试功能：

#### 检查ML服务健康状态
```http
GET /api/ml/health
```

#### 测试预测功能
```http
GET /api/ml/test-prediction
```

#### 获取当前环境参数
```http
GET /api/ml/env-params
```

## 预测结果

系统会预测7种设备的控制状态：

| 设备 | 状态值 | 说明 |
|------|--------|------|
| 大风机1 | 0-3 | 0:关闭, 1:低速, 2:中速, 3:高速 |
| 大风机2 | 0-3 | 0:关闭, 1:低速, 2:中速, 3:高速 |
| 水帘 | 0-1 | 0:关闭, 1:开启 |
| 加热片 | 0-1 | 0:关闭, 1:开启 |
| 窗户 | 0-1 | 0:关闭, 1:开启 |
| 小风扇1 | 0-3 | 0:关闭, 1:低速, 2:中速, 3:高速 |
| 小风扇2 | 0-3 | 0:关闭, 1:低速, 2:中速, 3:高速 |

## 日志输出

系统会在控制台输出详细的预测信息：

```
=== AI预测命令解析结果 ===
大风机1状态: 2 (中速)
大风机2状态: 1 (低速)
水帘状态: 1 (开启)
加热片状态: 0 (关闭)
窗户状态: 1 (开启)
小风扇状态: 2 (中速)
========================
```

## 服务集成

### 在Service中调用

```java
@Autowired
private AiCommandServiceImpl aiCommandService;

// 执行AI预测
aiCommandService.Aicommand();

// 获取预测结果
Integer[] predictions = aiCommandService.getMlPredictionCommands();
```

### 获取环境参数

```java
BigDecimal temperature = aiCommandService.getAiTemp();
Integer humidity = aiCommandService.getAiHum();
Integer ammonia = aiCommandService.getAiAmm();
Integer co2 = aiCommandService.getAiCo2();
```

## 异常处理

- **ML服务不可用**: 返回默认命令（所有设备关闭）
- **网络超时**: 自动重试机制
- **参数异常**: 使用默认值进行预测

## 配置说明

### 环境参数范围

| 参数 | 范围 | 默认值 |
|------|------|--------|
| temperature | -50~60°C | 25.0 |
| humidity | 0~100% | 60.0 |
| ammonia | 0~100 | 15.0 |
| co2 | 0~10000 | 1500.0 |
| internal_pressure | 90~110 | 101.3 |
| external_pressure | 90~110 | 101.5 |

### 超出范围处理

当传感器数据超出预期范围时，系统会：
1. 记录警告日志
2. 使用边界值进行预测
3. 触发异常处理机制

## 故障排除

### 常见问题

1. **ML服务连接失败**
   - 检查Python服务是否启动
   - 确认端口配置正确
   - 检查网络连接

2. **预测结果异常**
   - 确认传感器数据有效
   - 检查模型文件是否完整
   - 查看详细日志信息

3. **性能问题**
   - 检查网络延迟
   - 优化预测频率
   - 考虑使用缓存机制

### 调试模式

启用调试日志：
```yaml
logging:
  level:
    com.agriguard.service: DEBUG
```

## 后续开发

当前版本仅实现预测结果的解析和打印。后续版本将包括：

1. **自动执行控制命令**
2. **预测结果验证**
3. **学习效果反馈**
4. **批量预测支持**

## 联系支持

如有问题或建议，请联系技术支持团队。 