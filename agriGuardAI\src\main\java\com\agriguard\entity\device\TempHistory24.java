package com.agriguard.entity.device;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@Data
public class TempHistory24 {
    private LocalDate collectDate;
    private Integer collectHour;
    private BigDecimal maxTemp;
    private  BigDecimal minTemp;
    private  BigDecimal avgTemp;
}
