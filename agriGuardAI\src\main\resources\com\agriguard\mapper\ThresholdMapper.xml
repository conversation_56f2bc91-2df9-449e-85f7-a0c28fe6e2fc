<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.agriguard.mapper.ThresholdMapper">
    <!--更新阈值信息-->
    <update id="updateThresholdValue">
        UPDATE threshold
        <set>
            <if test="upValue != null">up_value = #{upValue},</if>
            <if test="downValue != null">down_value = #{downValue},</if>
        </set>
        WHERE threshold_type = #{thresholdType}
        AND place = #{place}
    </update>
    <!--获取所有阈值信息-->
    <select id="getAllThresholds" resultType="com.agriguard.entity.Threshold">
        SELECT * FROM threshold
    </select>

    <select id="selectThresholdTypeThreshold" resultType="com.agriguard.dto.SelectDevice">
        SELECT * FROM threshold WHERE place = #{place}
    </select>


</mapper>


