package com.agriguard.mapper;

import com.agriguard.pojo.Device;
import com.agriguard.pojo.FanRealTime;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface FanMapper {

    //多条件模糊查询风机状态
    List<FanRealTime> findLatestStatus(Integer deviceId, String deviceName,
                                       Integer fanStatus, String place, Integer gear);

    //添加风机状态
    void addFanRealTime(Integer deviceId);

    //修改风机状态
    void updateStatus(Integer deviceId, Integer fanStatus, Integer gear, LocalDateTime updateTime);


    //根据deviceId查询风机状态
    FanRealTime findLatestStatusByDeviceId(Integer deviceId);

    //修改档位
    void updateGear(Integer deviceId, Integer gear, LocalDateTime updateTime);

    //根据风机id查询风机详细信息
    Device findFanDetailById(Integer deviceId);

    //查询历史数据
    List<FanRealTime> findHistoryData(Integer deviceId, LocalDateTime start, LocalDateTime end);

    //命令操作
    void ordercommand(Integer deviceId, Integer fanStatus, Integer gear, LocalDateTime updateTime);

    //增加风机数据历史状态
    void addFanDataHistory(Integer deviceId, Integer fanStatus, Integer gear, LocalDateTime updateTime);

    //用户AI判断命令
    Integer fanAiCommand(Integer deviceId);
}
