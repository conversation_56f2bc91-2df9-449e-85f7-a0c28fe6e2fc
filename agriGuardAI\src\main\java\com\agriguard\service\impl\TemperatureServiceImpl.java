package com.agriguard.service.impl;

import com.agriguard.entity.device.TempHistory24;
import com.agriguard.entity.device.TempPie;
import com.agriguard.entity.device.TempRealTimeByMinute;
import com.agriguard.mapper.DeviceMapper;
import com.agriguard.mapper.TemperatureMapper;
import com.agriguard.pojo.Device;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.TemperatureDataHistory;
import com.agriguard.pojo.TemperatureRealTime;
import com.agriguard.service.DeviceService;
import com.agriguard.service.TemperatureService;
import com.agriguard.utils.DateTimeUtil;
import com.agriguard.utils.TemperatureGenerator;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class TemperatureServiceImpl implements TemperatureService {


    @Autowired
    private TemperatureMapper temperatureMapper;
    @Autowired
    private DeviceMapper deviceMapper;
    @Autowired
    private DeviceService deviceService;

    @Override
    public void insertTemperature() {
        //默认温度传感器设备编号为1
        int deviceId = 1;
        //模拟生成华为云温度数据
        BigDecimal temp = BigDecimal.valueOf((TemperatureGenerator.generateTemperature()) / 10);
        System.out.println("生成的温度: " + temp);
        //生成数据更新时间
        LocalDateTime updateTime = java.time.LocalDateTime.now();
        //将温度实时状态表进行更新,实时状态表就不进行时间更新了，数据库会自己进行更新
        temperatureMapper.updateTempToRealTime(deviceId, temp, updateTime);
        //将温度数据插入温度缓存表
        temperatureMapper.insertTempToDataCache(deviceId, temp, updateTime);
    }

    //定时对温度数据进行分析，存储历史数据，并进行数据删除
    @Override
    public void TempDateSys() {

        //获取当前时间
        String[] hourRange = DateTimeUtil.getPreviousHourRange();
        System.out.println("上一个小时范围:");
        System.out.println("开始时间: " + hourRange[0]);
        System.out.println("结束时间: " + hourRange[1]);
        //将String类型的时间转换为LocalDateTime类型
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将字符串转换为LocalDateTime
        LocalDateTime startTime = LocalDateTime.parse(hourRange[0], formatter);
        LocalDateTime endTime = LocalDateTime.parse(hourRange[1], formatter);
        // 调用Mapper方法获取数据
        List<TemperatureDataHistory> result = temperatureMapper.getTemperatureStatsByTimeRange(startTime, endTime);

        //h获取小时数
        int hour = startTime.getHour();
        // 设置插入时间为当前时间
        LocalDateTime now = LocalDateTime.now();
//        // 从now获取日期部分，精确到天
//        LocalDate currentDate = now.toLocalDate();
        // 从startTime获取日期部分，精确到天
        LocalDate currentDate = startTime.toLocalDate();
        for (TemperatureDataHistory item : result) {
            item.setCollectHour(hour);
            item.setUpdateTime(now);
            item.setCollectDate(currentDate);
        }
        System.out.println("执行了温度缓存表处理的定时任务！");

        // 插入数据到历史表
        for (TemperatureDataHistory item : result) {
            temperatureMapper.addTempDateHistory(item);
        }

        //删除缓存表中的数据
        temperatureMapper.deleteTempDateCacheByHour(startTime, endTime);
    }

    //查询设备id温度传感器是否已存在
    @Override
    public boolean existsByDeviceId(Integer deviceId) {
        return temperatureMapper.existsByDeviceId(deviceId) > 0;
    }

    @Override
    public String addTempRealTime(Integer deviceId) {
        // 设备存在性校验
        Device device = deviceService.findById(deviceId);
        if (device == null) return "设备不存在";

        // 设备类型校验
        if(!"温度传感器".equals(device.getType())) return "该设备不为温度传感器";

        // 重复性校验
        if(temperatureMapper.existsByDeviceId(deviceId) > 0) return "该温度传感器已存在";

        temperatureMapper.addTempRealTime(deviceId);
        return "添加成功";
    }

    //查询温度传感器详情
    @Override
    public Device findTempDetailById(Integer deviceId) {
        System.out.println("查询温度传感器详情成功");
        return temperatureMapper.findTempDetailById(deviceId);
    }

    public PageResult<TemperatureRealTime> findHistoryData(Integer pageNum, Integer pageSize, Integer deviceId, LocalDate beginData, LocalDate endData) {
        //创建PageResult对象
        PageResult<TemperatureRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 转换日期范围
        LocalDateTime start = beginData != null ? beginData.atStartOfDay() : null;
        LocalDateTime end = endData != null ? endData.plusDays(1).atStartOfDay() : null;
        // 调用 mapper 进行查询
        List<TemperatureRealTime> as = temperatureMapper.findHistoryData(deviceId, start, end);
        // 将查询结果转换为 Page 对象
        Page<TemperatureRealTime> p = (Page<TemperatureRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询温度历史数据成功");
        return pb;
    }


    //分页查询实时状态数据
    @Override
    public PageResult<TemperatureRealTime> findLatestStatus(Integer pageNum, Integer pageSize, Integer deviceId, String deviceName, String place) {
        //创建PageResult对象
        PageResult<TemperatureRealTime> pb = new PageResult<>();
        // 开启分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 调用 mapper 进行查询
        List<TemperatureRealTime> as = temperatureMapper.findLatestStatus(deviceId, deviceName, place);
        // 将查询结果转换为 Page 对象
        Page<TemperatureRealTime> p = (Page<TemperatureRealTime>) as;
        // 创建 PageResult 对象并填充数据
        pb.setTotal(p.getTotal());
        pb.setRows(p.getResult());
        System.out.println("查询温度状态成功");
        return pb;
    }

    //查询实时状态数据
    @Override
    public List<TempRealTimeByMinute> getAvgTemperature(String place) {
        System.out.println(place);
        return temperatureMapper.getAvgTemperature(place);
    }

    //获取过去24小时的历史温度数据，用于折线图展示
    @Override
    public List<TempHistory24> getHistory24Hours(String place) {
        return temperatureMapper.getHistory24Hours(place);
    }

    //获取过去24小时的历史温度数据，用于饼图展示
    @Override
    public List<TempPie> getHistory24HoursForPie(String place) {
        return temperatureMapper.getHistory24HoursForPie(place);
    }

}
