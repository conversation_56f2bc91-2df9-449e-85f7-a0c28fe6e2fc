package com.agriguard.controller;

import com.agriguard.entity.Alarm;
import com.agriguard.pojo.PageResult;
import com.agriguard.pojo.Result;
import com.agriguard.service.iotservice.AlarmService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/alarms")
@RequiredArgsConstructor
public class AlarmController {
    private final AlarmService alarmService;

    @GetMapping
    public PageResult<Alarm> getAlarms(
            @RequestParam(required = false) String alarmTypes,
            @RequestParam(required = false) String place,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @PageableDefault(page = 0, size = 10) Pageable pageable) {
        return alarmService.getAlarms(alarmTypes, place, startTime, endTime, pageable);
    }

    @PostMapping
    public Result<String> HandleAlarms(
            @RequestParam String alertId,
            @RequestHeader String Authorization
    ) {
        return Result.success(alarmService.HandleAlarms(alertId));
    }

}
