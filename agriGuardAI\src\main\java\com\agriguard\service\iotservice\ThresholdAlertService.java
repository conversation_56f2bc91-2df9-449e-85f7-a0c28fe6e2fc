package com.agriguard.service.iotservice;

import com.agriguard.entity.Alarm;
import com.agriguard.entity.Regulation;
import com.agriguard.entity.Threshold;
import com.agriguard.mapper.*;
import com.agriguard.pojo.Device;
import com.agriguard.service.code.EmailService;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ThresholdAlertService {

    @Autowired
    private ThresholdMapper thresholdMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private AlarmMapper alarmMapper;

    @Autowired
    private EmailService emailService;
    @Autowired
    private SpringTemplateEngine templateEngine;
    @Autowired
    private RegulationMapper regulationMapper;
    @Autowired
    private UserMapper userMapper;

    private static final Map<String, String> PARAM_TABLE_MAP = new HashMap<>();

    static {
        PARAM_TABLE_MAP.put("温度", "temperature_real_time");
        PARAM_TABLE_MAP.put("湿度", "humidity_real_time");
        PARAM_TABLE_MAP.put("氨气", "ammonia_real_time");
        PARAM_TABLE_MAP.put("二氧化碳", "co2_real_time");
        PARAM_TABLE_MAP.put("气压", "air_pressure_real_time");
        PARAM_TABLE_MAP.put("电流", "electricity_real_time");
    }

    private static final Map<String, String> PARAM_FIELD_MAP = new HashMap<>();

    static {
        PARAM_FIELD_MAP.put("温度", "temp");
        PARAM_FIELD_MAP.put("湿度", "hum");
        PARAM_FIELD_MAP.put("氨气", "amm");
        PARAM_FIELD_MAP.put("二氧化碳", "co2");
        PARAM_FIELD_MAP.put("气压", "apre");
        PARAM_FIELD_MAP.put("电流", "ele");
    }

    public void checkAllThresholds() {
        List<Threshold> thresholds = thresholdMapper.getAllThresholds();
        for (Threshold threshold : thresholds) {
            String tableName = PARAM_TABLE_MAP.get(threshold.getThresholdType());
            if (tableName == null) {
                continue;
            }
            List<Map<String, Object>> realTimeData = deviceMapper.getRealTimeData(tableName, threshold.getPlace());
            checkThresholdForData(realTimeData, threshold);
        }
    }

    private void checkThresholdForData(List<Map<String, Object>> realTimeData, Threshold threshold) {
        List<Integer> exceededDeviceIds = new ArrayList<>();
        Map<Integer, BigDecimal> deviceIdToValueMap = new HashMap<>();
        String paramType = threshold.getThresholdType();

        // 根据参数类型确定字段名和数据类型
        boolean isDecimalType = Arrays.asList("温度", "电流", "气压").contains(paramType);
        String fieldName = PARAM_FIELD_MAP.getOrDefault(paramType, "temp");


        for (Map<String, Object> data : realTimeData) {
            Integer deviceId = (Integer) data.get("device_id");
            Number rawValue = (Number) data.get(fieldName);

            // 统一转换为BigDecimal进行比较
            BigDecimal realValue = isDecimalType ?
                    new BigDecimal(rawValue.toString()) :
                    BigDecimal.valueOf(rawValue.longValue());

            BigDecimal upValue = new BigDecimal(threshold.getUpValue());
            BigDecimal downValue = new BigDecimal(threshold.getDownValue());

            // 添加阈值比较逻辑
            if (realValue.compareTo(upValue) > 0 || realValue.compareTo(downValue) < 0) {
                exceededDeviceIds.add(deviceId);
                deviceIdToValueMap.put(deviceId, realValue);
            }
        }

        // 移动处理逻辑到循环外
        if (!exceededDeviceIds.isEmpty()) {
            List<Device> devices = deviceMapper.getDevicesByDeviceIds(exceededDeviceIds);
            handleExceededDevices(devices, threshold, deviceIdToValueMap);
        }else {
            log.info("{} 类型数据在 {} 区域阈值范围内（{} - {}）",
                    threshold.getThresholdType(),
                    threshold.getPlace(),
                    threshold.getDownValue(),
                    threshold.getUpValue());
        }
    }

    //发送邮件
    private void handleExceededDevices(List<Device> devices, Threshold threshold, Map<Integer, BigDecimal> deviceIdToValueMap) {
        // 修改后的接收人获取逻辑
        Regulation regulation = regulationMapper.getRegulationByParams(threshold.getPlace(), threshold.getThresholdType());
        if (regulation == null || StringUtils.isBlank(regulation.getPersonName())) {
            return;
        }

        // 分割用户名并查询邮箱
        List<String> userNames = Arrays.asList(regulation.getPersonName().split("\\s*,\\s*"));
        List<String> receivers = userMapper.findEmailsByUserNames(userNames);
        // 在handleExceededDevices方法中添加调试日志
        System.out.println("解析的用户名列表: " + userNames);
        System.out.println("查询到的邮箱列表: " + receivers);

        // 准备邮件模板上下文（合并所有参数类型）
        Context context = new Context();
        Map<String, List<Map<String, String>>> combinedAlerts = new HashMap<>();

        // 收集所有参数类型的报警信息
        for (Device device : devices) {
            BigDecimal realValue = deviceIdToValueMap.get(device.getDeviceId());

            // 构建报警信息（按参数类型分组）
            Map<String, String> alertInfo = new HashMap<>();
            alertInfo.put("deviceName", device.getDeviceName());
            alertInfo.put("deviceId", device.getDeviceId().toString());
            alertInfo.put("currentValue", realValue.toString());
            alertInfo.put("thresholdRange", String.format("%.1f - %.1f",
                    new BigDecimal(threshold.getDownValue()),
                    new BigDecimal(threshold.getUpValue())));

            // 按参数类型分组存储
            combinedAlerts
                    .computeIfAbsent(threshold.getThresholdType(), k -> new ArrayList<>())
                    .add(alertInfo);

            // 报警记录保持原有插入逻辑
            Alarm alarm = new Alarm();
            alarm.setDeviceName(device.getDeviceName());
            alarm.setPlace(threshold.getPlace());
            alarm.setAlarmTypes(threshold.getThresholdType());
            alarm.setCurrentValue(realValue.toString());
            alarm.setThreshold(String.format("%.1f - %.1f",
                    new BigDecimal(threshold.getDownValue()),
                    new BigDecimal(threshold.getUpValue())));
            alarm.setUpdateTime(LocalDateTime.now());
            alarm.setProcessingStatus("未处理");
            // 新增责任人字段设置
            alarm.setPersonName(regulation.getPersonName());
            alarmMapper.insertAlarm(alarm);
        }

        // 设置模板变量
        context.setVariable("place", threshold.getPlace());
        context.setVariable("combinedAlerts", combinedAlerts);
        context.setVariable("updateTime", LocalDateTime.now());

        // 渲染邮件内容
        String emailContent = templateEngine.process("alert-email", context);
        //合并所有渲染好的邮件内容



        // 统一发送合并邮件
        if (!receivers.isEmpty() && !devices.isEmpty()) {
            String subject = String.format("[系统报警] %s区域-%s超标报警", threshold.getPlace(),threshold.getThresholdType());
            // 修改为遍历所有接收者
            for (String receiver : receivers) {
                emailService.sendAlertEmail(receiver, subject, emailContent);
            }
        }
    }
}

