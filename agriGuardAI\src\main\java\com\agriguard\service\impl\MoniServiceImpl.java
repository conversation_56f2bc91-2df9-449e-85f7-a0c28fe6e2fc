package com.agriguard.service.impl;

import com.agriguard.mapper.*;
import com.agriguard.service.MoniService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.agriguard.utils.MoniDataUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
public class MoniServiceImpl implements MoniService {


    @Autowired
    private TemperatureMapper temperatureMapper;
    @Autowired
    private HumidityMapper humidityMapper;
    @Autowired
    private AmmoniaMapper ammoniaMapper;
    @Autowired
    private Co2Mapper co2Mapper;
    @Autowired
    private AirPressureMapper airPressureMapper;
    @Autowired
    private ElectricityMapper electricityMapper;

    @Override
    public void TempMoniDate() {
        BigDecimal temperature1 = new BigDecimal(MoniDataUtil.TemperatureGenerator.generateRandomTemperature());
        LocalDateTime updateTime = LocalDateTime.now();
        temperatureMapper.updateTempToRealTime(53, temperature1, updateTime);
        temperatureMapper.insertTempToDataCache(53, temperature1, updateTime);
        BigDecimal temperature2 = new BigDecimal(MoniDataUtil.TemperatureGenerator.generateRandomTemperature());
        temperatureMapper.updateTempToRealTime(52, temperature2, updateTime);
        temperatureMapper.insertTempToDataCache(52, temperature2, updateTime);
    }

    @Override
    public void HumMoniDate() {
        Integer humidity1 = MoniDataUtil.HumidityGenerator.generateRandomHumidity();
        LocalDateTime updateTime = LocalDateTime.now();
        humidityMapper.updateHumToRealTime(56, humidity1, updateTime);
        humidityMapper.insertHumToDataCache(56, humidity1, updateTime);
        Integer humidity2 = MoniDataUtil.HumidityGenerator.generateRandomHumidity();
        humidityMapper.updateHumToRealTime(57, humidity2, updateTime);
        humidityMapper.insertHumToDataCache(57, humidity2, updateTime);
    }

    @Override
    public void Co2MoniDate() {
        Integer co2 = MoniDataUtil.CO2Generator.generateRandomCO2();
        LocalDateTime updateTime = LocalDateTime.now();
        co2Mapper.updateCo2ToRealTime(61,co2,updateTime);
        co2Mapper.insertCo2ToDataCache(61,co2,updateTime);
    }

    @Override
    public void AmmMoniDate() {
//        Integer ammonia = MoniDataUtil.AmmoniaGenerator.generateRandomAmmonia();
        Integer ammonia = 0;
        LocalDateTime updateTime = LocalDateTime.now();
        ammoniaMapper.updateAmmoniaToRealTime(60,ammonia,updateTime);
        ammoniaMapper.insertAmmoniaToDataCache(60,ammonia,updateTime);

    }

    @Override
    public void ApreMoniDate() {
        BigDecimal pressure = BigDecimal.valueOf(MoniDataUtil.PressureGenerator.generateRandomPressure());
        LocalDateTime updateTime = LocalDateTime.now();
        airPressureMapper.updateAirPressureToRealTime(62,pressure,updateTime);
        airPressureMapper.insertAirPressureToDataCache(62,pressure,updateTime);
    }

    @Override
    public void EleMoniDate() {
        BigDecimal current = BigDecimal.valueOf(MoniDataUtil.CurrentGenerator.generateRandomCurrent());
        LocalDateTime updateTime = LocalDateTime.now();
        electricityMapper.updateElectricityToRealTime(69,current,updateTime);
        electricityMapper.insertElectricityToDataCache(69,current,updateTime);
    }
}
