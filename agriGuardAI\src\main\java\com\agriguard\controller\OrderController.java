package com.agriguard.controller;

import com.agriguard.pojo.Result;
import com.agriguard.service.CommandHandlerService;
import com.agriguard.service.iotservice.HuaweiIotCommandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private List<CommandHandlerService> commandHandlers; // Spring会自动注入所有实现

    @Autowired
    private HuaweiIotCommandService huaweiIotCommandService;

    @PostMapping("/command")
    public Result<String> command(@RequestParam String commandName, String parasValue, Integer deviceId){
        try {
            // 1. 发送命令到IoT平台
            if (!huaweiIotCommandService.sendCommand(commandName, parasValue)) {
                return Result.error("命令发送失败");
            }
            System.out.println("开始配置命令并存储数据库！");

            //发送成功并返回响应后的处理
            // 2. 查找合适的处理器
            CommandHandlerService handler = commandHandlers.stream()
                    .filter(h -> h.supports(commandName))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("不支持的命令类型"));

            // 3. 处理命令并更新数据库
            System.out.println(deviceId);
            return handler.handle(commandName, parasValue, deviceId);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("发送失败!");
        }
    }
}
