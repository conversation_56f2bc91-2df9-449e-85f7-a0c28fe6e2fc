package com.agriguard.controller.aichat;

import com.agriguard.dto.aichat.ChatRequestDTO;
import com.agriguard.dto.aichat.ChatResponseDTO;
import com.agriguard.pojo.Result;
import com.agriguard.service.aichat.AiChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * AI聊天控制器
 */
@Slf4j
@RestController
@RequestMapping("/ai")
public class AiChatController {

    @Autowired
    private AiChatService aiChatService;

    /**
     * AI聊天接口
     */
    @PostMapping("/chat")
    public Result<ChatResponseDTO> chat(@Validated @RequestBody ChatRequestDTO chatRequest) {
        try {
            Long userId = getCurrentUserId();
            if (userId == null) {
                return Result.error("用户未登录");
            }

            ChatResponseDTO response = aiChatService.chat(chatRequest, userId);

            if (response.getSuccess()) {
                return Result.success(response);
            } else {
                return Result.error(response.getErrorMessage());
            }

        } catch (Exception e) {
            return Result.error("AI服务暂时不可用，请稍后再试");
        }
    }

    /**
     * 获取用户会话列表
     */
    @GetMapping("/sessions")
    public Result<java.util.List<java.util.Map<String, Object>>> getSessions() {
        try {
            Long userId = getCurrentUserId();
            if (userId == null) {
                return Result.error("用户未登录");
            }

            java.util.List<java.util.Map<String, Object>> sessions = aiChatService.getUserSessions(userId);
            return Result.success(sessions);
        } catch (Exception e) {
            return Result.error("获取会话列表失败");
        }
    }

    /**
     * 获取会话历史
     */
    @GetMapping("/session/{sessionId}")
    public Result<String> getSessionHistory(@PathVariable String sessionId) {
        try {
            Long userId = getCurrentUserId();
            if (userId == null) {
                return Result.error("用户未登录");
            }

            String history = aiChatService.getSessionHistory(sessionId, userId);
            return Result.success(history);
        } catch (SecurityException e) {
            return Result.error("无权访问此会话");
        } catch (Exception e) {
            return Result.error("获取会话历史失败");
        }
    }

    /**
     * 清空会话历史
     */
    @DeleteMapping("/session/{sessionId}")
    public Result<String> clearSession(@PathVariable String sessionId) {
        try {
            Long userId = getCurrentUserId();
            if (userId == null) {
                return Result.error("用户未登录");
            }

            aiChatService.clearSession(sessionId, userId);
            return Result.success("会话已清空");
        } catch (SecurityException e) {
            return Result.error("无权操作此会话");
        } catch (Exception e) {
            return Result.error("清空会话失败");
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<java.util.Map<String, Object>> health() {
        try {
            java.util.Map<String, Object> healthStatus = new java.util.HashMap<>();
            healthStatus.put("status", "healthy");
            healthStatus.put("version", "1.0.0");
            healthStatus.put("uptime", java.time.Instant.now().getEpochSecond());
            healthStatus.put("lastCheck", System.currentTimeMillis());
            healthStatus.put("message", "AI服务运行正常");

            return Result.success(healthStatus);
        } catch (Exception e) {
            return Result.error("AI服务异常");
        }
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()
                    && !"anonymousUser".equals(authentication.getPrincipal())) {
                // 这里根据您的用户认证系统调整
                // 假设Principal是User对象或者用户名字符串
                Object principal = authentication.getPrincipal();
                if (principal instanceof String) {
                    // 如果是用户名，需要根据用户名查询用户ID
                    // 这里暂时返回一个固定值，您需要根据实际情况修改
                    return 1L; // TODO: 根据用户名查询实际用户ID
                }
                // 如果Principal是User对象，直接获取ID
                // return ((User) principal).getId();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
} 