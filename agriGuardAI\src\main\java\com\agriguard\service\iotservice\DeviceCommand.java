package com.agriguard.service.iotservice;

import com.agriguard.mapper.*;
import com.agriguard.service.IotDeviceServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;


@Slf4j
@RequiredArgsConstructor
@Service
public class DeviceCommand implements IotDeviceServer {



    @Autowired
    private FanMapper fanMapper;
    @Autowired
    private NappeMapper nappeMapper;
    @Autowired
    private WindowMapper windowMapper;
    @Autowired
    private HeatMapper heatMapper;

    @Override
    public boolean findDeviceType(String deviceType) {
        String type = "device";
        return type.equals(deviceType);
    }

    /**
     * 先查找当天该设备有没有记录数据，有的话修改，否则添加
     * @param
     */
    @Override
    public void addData(String value) {

        System.out.println("addDatad的命令是："+value);
        System.out.println("6666666666666:"+splitString(value)[0]);
        System.out.println("7777777777777:"+splitString(value)[1]);
        System.out.println("8888888888888:"+splitString(value)[2]);

        //取出标识符
        String tpye = (String) splitString(value)[0];
        //取出具体设备id
        Integer deviceid = (Integer) splitString(value)[1];
        //取出具体设备命令
        Integer data = (Integer) splitString(value)[2];

        //生成一个统一的时间便于实时状态表和历史数据表统一时间
        LocalDateTime updateTime = LocalDateTime.now();
        switch (tpye) {
            case "0E":
                //风机的id会变化，所以不用分开写
                if (data == 0){
                    fanMapper.ordercommand(deviceid,0,data,updateTime);
                    fanMapper.addFanDataHistory(deviceid,0,data,updateTime);
                }else {
                    fanMapper.ordercommand(deviceid,1,data,updateTime);
                    fanMapper.addFanDataHistory(deviceid,1,data,updateTime);
                }
                break;
            case "0F":
                //风扇的id会变化，所以不用分开写
                if (data == 0){
                    fanMapper.ordercommand(deviceid,0,data,updateTime);
                    fanMapper.addFanDataHistory(deviceid,0,data,updateTime);
                }else {
                    fanMapper.ordercommand(deviceid,1,data,updateTime);
                    fanMapper.addFanDataHistory(deviceid,1,data,updateTime);
                }
                break;
            case "0G":
                //窗户的id为8,9,10,72
//                windowMapper.ordercommand(8,data);
//                windowMapper.ordercommand(9,data);
//                windowMapper.ordercommand(10,data);
//                windowMapper.ordercommand(72,data);
                windowMapper.ordercommand(deviceid,data);
                break;
            case "0H":
                //水帘id为2和3
                nappeMapper.ordercommand(2,data);
                nappeMapper.addNappeDataHistory(2,data,updateTime);
                nappeMapper.ordercommand(3,data);
                nappeMapper.addNappeDataHistory(3,data,updateTime);
                break;
            case "0I":
                //加热片id为11，12,73
                heatMapper.ordercommand(11,data,updateTime);
                heatMapper.updateHeatDateHistory(11,data,updateTime);
                heatMapper.ordercommand(12,data,updateTime);
                heatMapper.updateHeatDateHistory(12,data,updateTime);
                heatMapper.ordercommand(73,data,updateTime);
                heatMapper.updateHeatDateHistory(73,data,updateTime);
                break;
            default:
                log.info("硬件组下发命令错误");
        }
        log.info("硬件组下发了命令，其命令为：{}，时间为：{}",value,updateTime);
    }

    //取出前两个，后面全部的方法
    public static Object[] splitString(String input) {
        if (input == null || input.length() != 6) {
            return new String[]{null, null, null};
        }

        try {
            String firstTwoChars = input.substring(0, 2);
            int middleThreeDigits = Integer.parseInt(input.substring(2, 5));
            int lastDigit = Integer.parseInt(input.substring(5));

            return new Object[]{firstTwoChars, middleThreeDigits, lastDigit};
        } catch (NumberFormatException e) {
            return new String[]{null, null, null};
        }
    }

}
