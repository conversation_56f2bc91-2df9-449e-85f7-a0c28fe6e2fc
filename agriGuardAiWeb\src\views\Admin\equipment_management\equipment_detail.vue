<template>
  <div class="equipment-container">
    <div class="header">
      <h1>设备管理</h1>
      <el-button type="primary" @click="openDialog('add')">新增设备</el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-bar">
      <el-input v-model="searchParams.deviceId" placeholder="设备ID" clearable
                style="width: 150px; margin-right: 10px;" />
      <el-input v-model="searchParams.deviceName" placeholder="设备名称" clearable
                style="width: 200px; margin-right: 10px;" />
      <el-input v-model="searchParams.type" placeholder="设备类型" clearable
                style="width: 150px; margin-right: 10px;">
      </el-input>
      <el-input v-model="searchParams.place" placeholder="位置" clearable
                style="width: 150px; margin-right: 10px;" />
      <el-input v-model="searchParams.model" placeholder="型号" clearable
                style="width: 150px; margin-right: 10px;" />
      <el-input v-model="searchParams.factory" placeholder="厂家" clearable
                style="width: 150px; margin-right: 10px;" />
      <el-date-picker v-model="searchParams.installTime" type="date" placeholder="安装日期" value-format="YYYY-MM-DD"
                      style="width: 180px; margin-right: 10px;" />
      <el-button type="primary" @click="fetchDevices">搜索</el-button>
      <el-button @click="resetSearch">重置</el-button>
    </div>

    <!-- 设备列表 -->
    <el-table :data="deviceList" style="width: 100%" border v-loading="loading">
      <el-table-column prop="deviceId" label="ID" width="80" />
      <el-table-column prop="deviceName" label="设备名称" />
      <el-table-column prop="type" label="设备类型" />
      <el-table-column prop="place" label="位置" />
      <el-table-column prop="model" label="型号" />
      <el-table-column prop="factory" label="厂家" />
      <el-table-column label="安装时间" width="120">
        <template #default="{ row }">
          {{ formatDate(row.installTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="openDialog('edit', row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteDevice(row.deviceId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
                     layout="total, sizes, prev, pager, next, jumper" @current-change="fetchDevices"
                     @size-change="handleSizeChange" />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增设备' : '编辑设备'" width="600px">
      <el-form :model="formData" label-width="100px" ref="formRef">
        <el-form-item label="设备名称" prop="deviceName" required>
          <el-input v-model="formData.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="type" required>
          <el-input v-model="formData.type" placeholder="请输入设备类型" />
        </el-form-item>
        <el-form-item label="位置" prop="place">
          <el-input v-model="formData.place" placeholder="请输入位置" />
        </el-form-item>
        <el-form-item label="型号" prop="model">
          <el-input v-model="formData.model" placeholder="请输入型号" />
        </el-form-item>
        <el-form-item label="厂家" prop="factory">
          <el-input v-model="formData.factory" placeholder="请输入厂家" />
        </el-form-item>
        <el-form-item label="安装日期" prop="installTime">
          <el-date-picker v-model="formData.installTime" type="date" placeholder="选择安装日期"
                          value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="设备描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">
          {{ dialogType === 'add' ? '创建' : '更新' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import eqAPI from '@/api/equipment_management.js'

// 搜索参数
const searchParams = reactive({
  deviceId: '',
  deviceName: '',
  type: '',
  place: '',
  model: '',
  factory: '',
  installTime: ''
})

// 设备列表数据
const deviceList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 弹窗相关
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const formData = reactive({
  deviceId: '',
  deviceName: '',
  type: '',
  place: '',
  model: '',
  factory: '',
  installTime: '',
  description: ''
})
const formRef = ref(null)

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString()
}

// 获取设备列表
const fetchDevices = async () => {
  loading.value = true
  try {
    const response = await eqAPI.find_device(
        currentPage.value,
        pageSize.value,
        searchParams.deviceId,
        searchParams.deviceName,
        searchParams.type,
        searchParams.place,
        searchParams.model,
        searchParams.factory,
        searchParams.installTime
    )
    console.log(response.data.data)
    deviceList.value = response.data.data.rows || []
    total.value = response.data.data.total || 0
    ElMessage.success('设备列表加载成功')
  } catch (error) {
    ElMessage.error('获取设备列表失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchParams.deviceId = ''
  searchParams.deviceName = ''
  searchParams.type = ''
  searchParams.place = ''
  searchParams.model = ''
  searchParams.factory = ''
  searchParams.installTime = ''
  fetchDevices()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchDevices()
}

// 打开弹窗
const openDialog = (type, row) => {
  dialogType.value = type
  if (type === 'add') {
    // 重置表单
    Object.assign(formData, {
      deviceId: '',
      deviceName: '',
      type: '',
      place: '',
      model: '',
      factory: '',
      installTime: '',
      description: ''
    })
  } else if (type === 'edit') {
    // 填充表单
    Object.assign(formData, {
      deviceId: row.deviceId,
      deviceName: row.deviceName,
      type: row.type,
      place: row.place,
      model: row.model,
      factory: row.factory,
      installTime: row.installTime,
      description: row.description || ''
    })
  }
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  try {
    // 表单验证
    if (!formData.deviceName || !formData.type) {
      ElMessage.warning('请填写设备名称和类型')
      return
    }

    if (dialogType.value === 'add') {
      await eqAPI.add_device(
          formData.deviceId,
          formData.deviceName,
          formData.type,
          formData.place,
          formData.model,
          formData.factory,
          formData.installTime,
          formData.description
      )
      ElMessage.success('设备添加成功')
    } else {
      await eqAPI.update_device(
          formData.deviceId,
          formData.deviceName,
          formData.type,
          formData.place,
          formData.model,
          formData.factory,
          formData.installTime,
          formData.description
      )
      ElMessage.success('设备更新成功')
    }

    // 刷新列表
    fetchDevices()
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
  }
}

// 删除设备
const deleteDevice = (id) => {
  ElMessageBox.confirm('确定要删除这个设备吗？删除后无法恢复！', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await eqAPI.delete_device(id)
      ElMessage.success('设备已删除')
      fetchDevices()
    } catch (error) {
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {
    // 用户取消
  })
}

// 初始化加载设备列表
onMounted(() => {
  fetchDevices()
})
</script>

<style scoped>
.equipment-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table) {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table__header) th {
  background-color: #f8f8f9;
  font-weight: bold;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}
</style>
