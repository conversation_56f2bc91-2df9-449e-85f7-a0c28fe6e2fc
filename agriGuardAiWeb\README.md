# agriGuardAiWeb

农业智能监控系统前端项目，基于Vue 3和Element Plus构建的现代化Web应用。

## 项目简介

agriGuardAiWeb是一个专业的农业环境监控管理系统，提供用户管理、设备管理、传感器监控等功能，帮助农业从业者更好地管理和监控农业生产环境。

## 技术栈

- **前端框架**: Vue 3 + Vite
- **UI组件库**: Element Plus
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **开发语言**: JavaScript

## 主要功能

### 🔐 用户权限管理
- **双角色系统**: 管理员和普通用户两种角色
- **用户管理**: 支持用户的增删改查操作
- **角色切换**: 管理员可提升/降级用户角色
- **智能搜索**: 支持用户名和邮箱的模糊匹配搜索
- **角色筛选**: 可按角色类型筛选用户列表

### 📊 管理后台
- **仪表盘**: 系统概览和数据统计
- **设备管理**: 风扇、加热元件、水帘、窗户等设备控制
- **传感器监控**: 温度、湿度、二氧化碳、氨气等环境参数监控
- **人员管理**: 用户账号和权限管理

### 🎨 界面特性
- **现代化设计**: Element Plus组件库，界面美观统一
- **响应式布局**: 支持移动端和桌面端自适应
- **中文本地化**: 完整的中文界面和提示信息
- **用户体验优化**: 加载状态、消息提示、确认对话框等

## 推荐IDE设置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (请禁用Vetur插件)

## 项目配置

参考 [Vite配置文档](https://vite.dev/config/)

## 项目安装

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

### 生产环境构建

```sh
npm run build
```

## 用户管理功能详情

### 界面特性
- **Element Plus重构**: 使用现代化组件库，界面更加美观
- **卡片式布局**: 页面结构清晰，层次分明
- **高级搜索**: 支持关键词搜索和角色筛选
- **智能分页**: 可调整每页显示数量，支持页码跳转
- **角色标签**: 彩色标签区分不同角色用户
- **操作确认**: 重要操作都有确认提示

### 功能特点
- **用户列表**: 分页显示所有用户信息
- **添加用户**: 管理员可创建新用户账号
- **编辑用户**: 修改用户基本信息和密码
- **删除用户**: 安全删除用户账号
- **角色管理**: 提升普通用户为管理员或降级
- **搜索功能**: 按用户名、邮箱模糊搜索
- **角色筛选**: 按管理员/普通用户筛选

### 权限说明
- **管理员**: 拥有所有功能权限，可以管理用户和系统设置
- **普通用户**: 基础功能权限，无法进行管理操作

### 安全特性
- 统一的错误处理机制
- 完整的表单验证
- 权限验证和友好提示
- 操作日志和安全审计

## 使用说明

1. **登录系统**: 使用管理员账号登录
2. **用户管理**: 在侧边栏点击"人员管理" → "用户管理"
3. **搜索用户**: 使用搜索框输入关键词或选择角色筛选
4. **管理操作**: 可以添加、编辑、删除用户，或切换用户角色
5. **权限控制**: 所有管理操作都需要管理员权限

## 注意事项

- 新注册用户默认为普通用户角色
- 删除用户操作不可恢复，请谨慎操作
- 用户名在编辑时不可修改
- 所有管理功能都需要管理员权限

## 开发说明

- API接口文档请参考 `docs/用户权限管理API文档.md`
- 项目使用Pinia进行状态管理
- 所有API请求都经过统一的错误处理
- 组件采用Element Plus中文语言包

## 项目结构

```
src/
├── api/              # API接口文件
├── views/            # 页面组件
│   ├── Admin/        # 管理后台页面
│   ├── Auth/         # 认证相关页面
│   └── User/         # 用户相关页面
├── router/           # 路由配置
├── stores/           # 状态管理
└── assets/           # 静态资源
```
