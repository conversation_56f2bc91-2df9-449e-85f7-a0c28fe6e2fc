import axios from 'axios'

const API_BASE_URL = '/api/user';

const auth = axios.create({
    baseURL: API_BASE_URL,
});

export default {
    loginWithPassword(data) {
        return auth.post(`/login`, {
            account: data.account,
            password: data.password
        })
    },

    // 注册接口增加验证码参数
    Register(data) {
        return auth.post(`/register`, {
            userName: data.userName,
            password: data.password,
            realName: data.realName,
            email: data.email,
            phone: data.phone,
            code: data.code  // 新增验证码字段
        })
    },

    // 新增发送注册验证码接口
    sendRegisterCode(email) {
        return auth.post(`/register/send-code?email=${email}`)
    },

    is_expire(token) {
        return auth.get(`/is_expire`, {
            headers: {
                'Authorization': token
            }
        })
    },

    // 发送重置密码验证码
    sendResetPasswordCode(email) {
        return auth.post(`/forget-pwd/send-code?email=${email}`)
    },

    // 提交重置密码请求
    verifyResetPasswordCode(data) {
        return auth.post(`/forget-pwd/reset`, data)
    }
}
